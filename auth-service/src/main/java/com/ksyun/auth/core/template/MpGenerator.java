package com.ksyun.auth.core.template;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;
import com.baomidou.mybatisplus.generator.query.SQLQuery;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.Consumer;

/**
 * 代码生成
 * <AUTHOR>
 * @date 2021-12-02
 */
public class MpGenerator {
    public static void main(String[] args) {
        //用来获取Mybatis-Plus.properties文件的配置信息
        final ResourceBundle rb = ResourceBundle.getBundle("mybatis-plus");
        String baseDir = rb.getString("baseDir");
        String entityOutputDir = baseDir + rb.getString("entityOutputDir");
        String outputDirXml = baseDir + rb.getString("OutputDirXml");
        String outputDir = baseDir + rb.getString("OutputDir");
        String serviceOutputDir = baseDir + rb.getString("serviceOutputDir");
        String author = rb.getString("author");
        String className = rb.getString("className");
        String parent = rb.getString("parent");
        HashMap<OutputFile, String> pathMap = new HashMap<>();
        pathMap.put(OutputFile.entity, entityOutputDir + "/" + className);
        pathMap.put(OutputFile.controller, outputDir + "/controller/" + className);
        pathMap.put(OutputFile.service, serviceOutputDir + "/service/" + className);
        pathMap.put(OutputFile.serviceImpl, serviceOutputDir + "/service/" + className + "/impl");
        pathMap.put(OutputFile.mapper, serviceOutputDir + "/dao/" + className);

        // 数据源配置
        String url = rb.getString("url");
        String username = rb.getString("userName");
        String password = rb.getString("password");

        DataSourceConfig.Builder dsb = new DataSourceConfig.Builder(url, username, password)
                .dbQuery(new MySqlQuery())
                //.schema("mybatis-plus")
                .typeConvert(new MySqlTypeConvert())
                .keyWordsHandler(new MySqlKeyWordsHandler())
                .databaseQueryClass(SQLQuery.class);

        FastAutoGenerator.create(dsb)
                .globalConfig(builder -> {
                    builder.outputDir(outputDir)
                            .author(author)
                            .enableSwagger()
                            .dateType(DateType.TIME_PACK)
                            .commentDate("yyyy-MM-dd");
                })
                .dataSourceConfig(builder -> {
                })
                .packageConfig(builder -> {
                    builder.parent(parent)
                            .moduleName(null)
                            .entity("po")
                            .service("auth.service" + className)
                            .serviceImpl("auth.service" + className + ".impl")
                            .mapper("auth.dao" + className)
                            .xml("mapper.xml")
                            .controller("privilege.controller" + className)
                            .pathInfo(pathMap)
                            .build();
                })
                .strategyConfig(builder -> {
                    builder.entityBuilder()
                            .enableLombok()
                            .entityBuilder().formatFileName("%sEntity")
                            .enableFileOverride();
                    builder.mapperBuilder()
                            .formatMapperFileName("%sMapper")
                            .formatXmlFileName("%sMapper")
                            .enableFileOverride();
                    builder.serviceBuilder()
                            .formatServiceFileName("%sService")
                            .formatServiceImplFileName("%sServiceImpl")
                            .enableFileOverride();
                    builder.controllerBuilder()
                            .formatFileName("%sController")
                            .enableFileOverride();
                })
                .injectionConfig(builder -> {
                    builder.beforeOutputFile((tableInfo, objectMap) -> {
                        System.out.println("tableInfo: " + tableInfo.getEntityName() + " objectMap: " + objectMap.size());
                    });
                })
                .execute();
    }
}
