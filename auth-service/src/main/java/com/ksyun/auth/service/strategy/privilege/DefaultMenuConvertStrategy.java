/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component(Constants.DEFAULT_MENU)
public class DefaultMenuConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {
    private static final String PROP_KEY = "isDefault";
    private static final String PROP_VALUE = "true";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (!StringUtil.isEmpty(s)) {
            PrivilegeProps props = new PrivilegeProps();
            props.setPropKey(PROP_KEY);
            props.setPropValue(PROP_VALUE);
            list.add(props);
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        Set<String> defaultProp = Sets.newHashSet();
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (!StringUtil.isEmpty(kv[0])) {
                        if (PROP_KEY.equals(kv[0])) {
                            if (PROP_KEY.equals(kv[0]) && kv.length > 1) {
                                defaultProp.add(DefaultMenuConvertStrategy.DefaultMenuEnum.valOf(kv[1]).getLabel());
                            }
                        }
                    }
                }
            }
        }
        return TupleVo.of("defaultProp", StringUtils.join(defaultProp, ","));
    }

    @Getter
    public enum DefaultMenuEnum {
        TRUE("是", PROP_VALUE),
        FALSE("否", ""),
        ;
        String label;
        String val;

        DefaultMenuEnum(String label, String val) {
            this.label = label;
            this.val = val;
        }

        public static DefaultMenuEnum valOf(String val) {
            for (DefaultMenuEnum defaultMenuEnum : DefaultMenuEnum.values()) {
                if (defaultMenuEnum.val.equals(val)) {
                    return defaultMenuEnum;
                }
            }
            return FALSE;
        }
    }
}
