package com.ksyun.auth.service.strategy.privilege;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.ksyun.auth.dao.PrivilegeMapper;
import com.ksyun.auth.dao.PrivilegePermissionGroupMapper;
import com.ksyun.auth.dto.PrivilegeExcelDto;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.AppAksk;
import com.ksyun.common.entity.Privilege;
import com.ksyun.common.entity.PrivilegeProps;
import com.ksyun.common.entity.Category;
import com.ksyun.common.entity.PermissionGroup;
import com.ksyun.common.entity.PrivilegePermissionGroup;
import com.ksyun.auth.dao.AppAkskMapper;
import com.ksyun.auth.dao.CategoryMapper;
import com.ksyun.auth.dao.PermissionGroupMapper;
import com.ksyun.auth.dao.PrivilegePropsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName PrivilegeUpdateStrategyPrivilege
 * @Desc: 权限导入(更新)策略
 * @Author: PANCHAO
 * @Email: <EMAIL>
 * @Date: 2021/4/25 17:27
 * @Company: 北京金山云网络技术有限公司
 * @Copyright: 2018 Ksyun All Rights Reserved Kingsoft Corp.
 * <p/>
 * ---------------------------------------------------------
 * Version    Author    Status    Date
 * V1.0      PANCHAO      C     2021/4/25
 */
@Component(Constants.PRIVILEGE_UPDATE_STRATEGY)
@Slf4j
public class PrivilegeUpdateStrategy extends AbstractPrivilegeImportStrategy {
    @Autowired
    private PrivilegeMapper privilegeMapper;
    @Autowired
    private PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    private PrivilegePropsMapper privilegePropsMapper;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private PermissionGroupMapper permissionGroupMapper;
    @Autowired
    private AppAkskMapper appAkskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImport(List list) {
        log.info("(PrivilegeUpdateStrategy)Privilege import...");
        if (!CollectionUtil.isNotEmpty(list)) {
            return;
        }
        log.info("(PrivilegeUpdateStrategy)Privilege list size is {}", list.size());
        Set<String> updatePrivilegeCodes = (Set<String>) list.stream()
                .map(o -> ((PrivilegeExcelDto) o).getCode()).collect(Collectors.toSet());
        List<Privilege> allPrivileges = privilegeMapper.selectList(null);
        Set<String> allPrivilegeCodes = allPrivileges.stream().map(p -> p.getCode()).collect(Collectors.toSet());
        Sets.SetView<String> notExistPrivilegeCodes = Sets.difference(updatePrivilegeCodes, allPrivilegeCodes);
        log.info("Privilege Code not exist{},ignore to perform.", notExistPrivilegeCodes.toString());
        List<PrivilegeExcelDto> privilegeExcelDataList = (List<PrivilegeExcelDto>) list.stream()
                .filter(o -> !notExistPrivilegeCodes.contains(((PrivilegeExcelDto) o).getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtil.isNotEmpty(privilegeExcelDataList)) {
            return;
        }
        Map<String, String> akMap = getAkMap();
        Map<String, Category> categoryMap = getCategoryMap();
        Map<String, PermissionGroup> permissionGroupMap = getPermissionGroup();
        Map<String, Privilege> privilegeMap = allPrivileges.stream()
                .collect(Collectors.toMap(Privilege::getCode, privilege -> privilege));
        //Map<String, PrivilegePermissionGroup> privilegePermissionGroupMap = getPrivilegePermissionGroup();

        for (PrivilegeExcelDto privilegeExcelData : privilegeExcelDataList) {
            Privilege privilege = privilegeMap.get(privilegeExcelData.getCode());
            //删除历史权限点与权限组的关系
            LambdaQueryWrapper<PrivilegePermissionGroup> privilegePermissionGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
            privilegePermissionGroupLambdaQueryWrapper.eq(PrivilegePermissionGroup::getPrivilegeId, privilege.getId());
            privilegePermissionGroupMapper.delete(privilegePermissionGroupLambdaQueryWrapper);
        }

        for (PrivilegeExcelDto privilegeExcelData : privilegeExcelDataList) {
            String categoryName = privilegeExcelData.getCategoryName();
            String groupName = privilegeExcelData.getGroupName();
            Category category = categoryMap.get(categoryName);
            //新增分类
            if (category == null) {
                category = insertCategory(categoryName);
                categoryMap.put(categoryName, category);
            }
            PermissionGroup permissionGroup = permissionGroupMap.get(groupName + "^" + category.getId());
            //新增权限组
            if (permissionGroup == null) {
                permissionGroup = insertPermissionGroup(category, groupName, privilegeExcelData.getGroupDesc());
                permissionGroupMap.put(groupName + "^" + category.getId(), permissionGroup);
            }
            //更新权限点
            Privilege privilege = privilegeMap.get(privilegeExcelData.getCode());
            if (Objects.isNull(privilege)) {
                log.warn("privilege [{}] does not exist", privilegeExcelData.getCode());
                continue;
            }
            privilege.setName(privilegeExcelData.getName());
            privilege.setType(privilegeExcelData.getType());
            privilege.setIcon(privilegeExcelData.getIcon());
            Privilege parentPrivilege = privilegeMap.get(privilegeExcelData.getParentCode());
            if (!Objects.isNull(parentPrivilege)) {
                privilege.setParentId(parentPrivilege.getId());
            }
            privilege.setUrl(privilegeExcelData.getUrl());
            privilege.setAk(akMap.get(privilegeExcelData.getAk()));
            privilegeMapper.updateById(privilege);
            //删除历史权限点PrivilegeProps元数据
            LambdaQueryWrapper<PrivilegeProps> privilegePropsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            privilegePropsLambdaQueryWrapper.eq(PrivilegeProps::getPrivilegeId, privilege.getId());
            privilegePropsMapper.delete(privilegePropsLambdaQueryWrapper);
            //新增最新权限点PrivilegeProps元数据
            for (PrivilegeProps privilegeProps : privilegeExcelData.getPrivilegeProps()) {
                privilegeProps.setPrivilegeId(privilege.getId());
                privilegePropsMapper.insert(privilegeProps);
            }

            //绑定权限组和权限点关联关系
            PrivilegePermissionGroup privilegePermissionGroup = new PrivilegePermissionGroup();
            privilegePermissionGroup.setPermissionGroupId(permissionGroup.getId());
            privilegePermissionGroup.setPrivilegeId(privilege.getId());
            privilegePermissionGroup.setCreateTime(LocalDateTime.now());
            privilegePermissionGroupMapper.insert(privilegePermissionGroup);
        }
    }

    private Map<String, Category> getCategoryMap() {
        List<Category> categories = categoryMapper.selectList(null);
        return categories.stream().collect(Collectors.toMap(Category::getName, category -> category));
    }

    private Map<String, PermissionGroup> getPermissionGroup() {
        LambdaQueryWrapper<PermissionGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PermissionGroup::getStatus, "NORMAL");
        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectList(queryWrapper);
        return permissionGroups.stream().collect(
                Collectors.toMap(permissionGroup -> permissionGroup.getName() + "^" + permissionGroup.getCategoryId(),
                        permissionGroup -> permissionGroup)
        );
    }

    private Map<String, String> getAkMap() {
        List<AppAksk> appAksks = appAkskMapper.selectList(null);
        return appAksks.stream().collect(Collectors.toMap(AppAksk::getAk, AppAksk::getAk));
    }

    private Category insertCategory(String categoryName) {
        Category category = new Category();
        category.setName(categoryName);
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        categoryMapper.insert(category);
        return category;
    }

    private PermissionGroup insertPermissionGroup(Category category, String groupName, String description) {
        PermissionGroup permissionGroup = new PermissionGroup();
        permissionGroup.setName(groupName);
        permissionGroup.setDescription(description);
        permissionGroup.setCategoryId(category.getId());
        permissionGroup.setCreateTime(LocalDateTime.now());
        permissionGroup.setUpdateTime(LocalDateTime.now());
        permissionGroupMapper.insert(permissionGroup);
        return permissionGroup;
    }

    private Map<String, PrivilegePermissionGroup> getPrivilegePermissionGroup() {
        List<PrivilegePermissionGroup> privilegePermissionGroups = privilegePermissionGroupMapper.selectList(null);
        return new HashSet<>(privilegePermissionGroups).stream().collect(
                Collectors.toMap(privilegePermissionGroup -> privilegePermissionGroup.getPermissionGroupId() + "^" + privilegePermissionGroup.getPrivilegeId(), Function.identity(),
                        (oldPrivilegePermissionGroup, newPrivilegePermissionGroup) -> newPrivilegePermissionGroup));
    }
}
