/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.ksyun.common.constant.Constants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component(Constants.ENUM)
public class TypeConvertStrategy implements ConvertStrategy<String, Integer, String, String> {

    @Override
    public Integer fromExcel(String s) {
        return TypeEnum.nameOf(s);
    }

    @Override
    public String toExcel(String s) {
        return TypeEnum.codeOf(Integer.parseInt(s)).label;
    }

    @Getter
    public enum TypeEnum {
        MENU(1, "菜单"), BUTTON(2, "按钮");
        int code;
        String label;

        TypeEnum(int code, String label) {
            this.code = code;
            this.label = label;
        }

        public static int nameOf(String label) {
            for (TypeEnum t : TypeEnum.values()) {
                if (Objects.equals(label, t.label)) {
                    return t.code;
                }
            }
            return 1;
        }

        public static TypeEnum codeOf(int code) {
            for (TypeEnum t : TypeEnum.values()) {
                if (code == t.code) {
                    return t;
                }
            }
            return BUTTON;
        }
    }
}
