package com.ksyun.auth.service.listener;

import com.google.common.collect.Lists;
import com.ksyun.auth.dto.PrivilegeExcelRowDto;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.exception.BusinessException;
import com.ksyun.auth.service.PermissionGroupService;
import com.ksyun.auth.service.event.PrivilegeDataExportEvent;
import com.ksyun.common.annotation.Column;
import com.ksyun.auth.dto.ExportPrivilegeDto;
import com.ksyun.auth.service.strategy.privilege.ConvertStrategy;
import jodd.io.FileUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component
public class PrivilegeDataExportEventListener implements ApplicationListener<PrivilegeDataExportEvent> {

    @Value("${upload.tmp.dir}")
    String tmpDir;
    @Autowired
    Map<String, ConvertStrategy> strategies;
    @Autowired
    PermissionGroupService permissionGroupService;


    @SneakyThrows
    @Override
    public void onApplicationEvent(PrivilegeDataExportEvent privilegeDataExportEvent) {
        List<ExportPrivilegeDto> list = Lists.newArrayList();
        list.addAll(permissionGroupService.getAllPrivileges());
        writeExcel(privilegeDataExportEvent.getFileName(), convert(list));
    }

    private List<PrivilegeExcelRowDto> convert(List<ExportPrivilegeDto> list) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        List<PrivilegeExcelRowDto> privilegeExcelRowList = Lists.newArrayList();
        for (ExportPrivilegeDto data : list) {
            PrivilegeExcelRowDto privilegeExcelRow = new PrivilegeExcelRowDto();
            org.springframework.beans.BeanUtils.copyProperties(data, privilegeExcelRow);
            Field[] fields = privilegeExcelRow.getClass().getDeclaredFields();
            for (Field field : fields) {
                Column annotation = field.getDeclaredAnnotation(Column.class);
                if (Column.Strategy.NONE != annotation.strategy()) {
                    if (Column.Type.MAJOR == annotation.type()) {
                        Object s = strategies.get(annotation.strategy().name()).toExcel(BeanUtils.getProperty(data, field.getName()));
                        BeanUtils.setProperty(privilegeExcelRow, field.getName(), s);
                    } else {
                        TupleVo tuple = (TupleVo) strategies.get(annotation.strategy().name()).toExcel(data.getPrivilegeProps());
                        BeanUtils.setProperty(privilegeExcelRow, tuple.getKey(), tuple.getValue());
                    }
                }
            }
            privilegeExcelRowList.add(privilegeExcelRow);
        }
        return privilegeExcelRowList;
    }

    public Workbook getWorkbook(String file) throws IOException {
        return new XSSFWorkbook(new FileInputStream(file));
    }

    public void writeExcel(String fileName, List list) throws IOException {
        Workbook workbook = null;
        FileOutputStream outputStream = null;
        String exportFilePath = tmpDir + File.separator + fileName;
        String templateFilePath = tmpDir + File.separator + Constants.PRIVILEGE_TEMPLATE_FILE_NAME;
        FileUtil.copy(templateFilePath, exportFilePath);
        try {
            workbook = getWorkbook(exportFilePath);
            Sheet sheet = workbook.getSheet("Sheet1");
            int rowNum = 2;
            for (Iterator<PrivilegeExcelRowDto> it = list.iterator(); it.hasNext(); ) {
                PrivilegeExcelRowDto data = it.next();
                if (data == null) {
                    continue;
                }
                convertToRow(sheet, rowNum++, data);
            }
            outputStream = new FileOutputStream(exportFilePath);
            workbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.warn("写Excel失败，文件名：{}，错误信息：", exportFilePath, e);
            throw new BusinessException("写Excel失败", e.getMessage());
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
                if (null != outputStream) {
                    outputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭数据流出错！错误信息：", e);
            }
        }
    }

    private void convertToRow(Sheet sheet, int rowNum, PrivilegeExcelRowDto data) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        //输出行数据
        Row row = sheet.createRow(rowNum);
        Field[] fields = data.getClass().getDeclaredFields();
        for (Field field : fields) {
            Column annotation = field.getDeclaredAnnotation(Column.class);
            int index = annotation.index();
            String property = BeanUtils.getProperty(data, field.getName());
            Cell cell = row.createCell(index, Cell.CELL_TYPE_STRING);
            cell.setCellValue(null == property ? "" : property);
        }
    }

}
