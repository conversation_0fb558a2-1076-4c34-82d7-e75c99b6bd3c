package com.ksyun.auth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ksyun.auth.dao.UserPropsMapper;

import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.entity.AppAksk;
import com.ksyun.common.entity.UserProps;
import com.ksyun.auth.dao.AppAkskMapper;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.exception.BusinessException;
import com.ksyun.auth.service.AppAkSkService;
import com.ksyun.auth.vo.AddAppAkSkVo;
import com.ksyun.auth.vo.IAASAkSkAddVo;
import com.ksyun.auth.vo.IAASAkSkUpdateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * appAKSK服务类
 * 主要是用于应用之间生成token
 */
@Service
@Slf4j
public class AppAkSkServiceImpl extends ServiceImpl<AppAkskMapper, AppAksk> implements AppAkSkService {

    @Autowired
    private UserPropsMapper userPropsMapper;
    @Resource
    private AppAkskMapper appAkskMapper;

    @Override
    @Transactional
    public void add(AddAppAkSkVo parameter) {
        QueryWrapper<AppAksk> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ak", parameter.getAppAk());
        Assert.isTrue(appAkskMapper.selectCount(queryWrapper) == 0, BusinessExceptionEnum.AK_SK_EXISTED);

        AppAksk appAksk = new AppAksk();
        appAksk.setAk(parameter.getAppAk());
        appAksk.setSk(parameter.getAppSk());
        appAksk.setDescription(parameter.getAppName());
        appAksk.setCreateBy(parameter.getCreateBy());
        appAksk.setCreateTime(LocalDateTime.now());
        int savedCount = appAkskMapper.insert(appAksk);
        if (savedCount != 1) {
            throw new BusinessException("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBind(IAASAkSkAddVo parameter) {
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        int akCount = userPropsMapper.insert(new UserProps(parameter.getUserId(), "ak", parameter.getAk()));
        userPropsMapper.insert(new UserProps(parameter.getUserId(), "ak.create-time", now));
        log.info("{} ak[{}] saved at {}", akCount, parameter.getAk(), now);
        int skCount = userPropsMapper.insert(new UserProps(parameter.getUserId(), "sk", parameter.getSk()));
        userPropsMapper.insert(new UserProps(parameter.getUserId(), "sk.create-time", now));
        log.info("{} sk[{}] saved at {}", skCount, parameter.getSk(), now);
        int appIdCount = userPropsMapper.insert(new UserProps(parameter.getUserId(), "appId", parameter.getAppId()));
        int statusCount = userPropsMapper.insert(new UserProps(parameter.getUserId(), "aksk-status", "NORMAL"));
        log.info("{} appId[{}] saved, status is NORMAL", skCount, parameter.getAppId(), now);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBind(Long userId) {
        for (String key : Lists.newArrayList("ak", "sk", "ak.create-time", "sk.create-time", "appId", "aksk-status")) {
            QueryWrapper<UserProps> queryWrapper = new QueryWrapper();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("prop_key", key);
            userPropsMapper.delete(queryWrapper);
        }
        log.info("Unbind IAAS AKSK, userId={}, deleted={}", userId);
    }

    @Override
    public void update(IAASAkSkUpdateVo parameter) {
        UserProps userProps = new UserProps();
        userProps.setPropValue(parameter.getAppId());
        UpdateWrapper<UserProps> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", parameter.getUserId());
        updateWrapper.eq("prop_key", "appId");
        int update = userPropsMapper.update(userProps, updateWrapper);
        log.info("Update IAAS appId, userId={}, appId={},updated={}", parameter.getUserId(), parameter.getAppId(), update);
    }


}
