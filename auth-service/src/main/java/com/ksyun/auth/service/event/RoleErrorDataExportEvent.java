/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.event;

import com.ksyun.auth.dto.RoleErrorExcelDto;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020
 */
@Setter
@Getter
public class RoleErrorDataExportEvent extends ApplicationEvent {
    private List<RoleErrorExcelDto> list;
    private String fileName;

    public RoleErrorDataExportEvent(String fileName, List<RoleErrorExcelDto> list) {
        super(list);
        this.list = list;
        this.fileName = fileName;
    }
}
