package com.ksyun.auth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.auth.dao.RsaKeyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;
import com.jcraft.jsch.JSchException;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import com.ksyun.common.entity.RsaKey;
import com.ksyun.auth.service.RsaKeyService;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
@Service
public class RsaKeyServiceImpl extends ServiceImpl<RsaKeyMapper, RsaKey> implements RsaKeyService {

    @Override
    public RsaKey generateKeyPair(String name, int keySize, String userId) {
        log.info("开始生成RSA密钥对, name={}, keySize={}, userId={}", name, keySize, userId);
        try {
            // 使用JSch生成密钥对
            JSch jsch = new JSch();
            KeyPair keyPair = KeyPair.genKeyPair(jsch, KeyPair.RSA, keySize);
            log.info("成功生成JSch密钥对");

            // 生成SSH格式的公钥
            String sshRsaPublicKey = generateSshRsaPublicKey(keyPair);
            // 生成OpenSSH格式的私钥
            String opensshPrivateKey = generateOpenSshPrivateKey(keyPair);
            log.info("成功生成SSH格式公钥和OpenSSH格式私钥");

            // 清理密钥对
            keyPair.dispose();

            // 创建并保存密钥信息
            RsaKey rsaKey = new RsaKey();
            rsaKey.setName(name);
            rsaKey.setType("平台生成");
            rsaKey.setPublicKey(sshRsaPublicKey);
            rsaKey.setPrivateKey(opensshPrivateKey);
            rsaKey.setKeySize(keySize);
            rsaKey.setUserId(userId);
            rsaKey.setIsActive(true);
            rsaKey.setCreateTime(LocalDateTime.now());
            save(rsaKey);
            log.info("成功保存RSA密钥对信息, name={}, userId={}", name, userId);

            return rsaKey;
        } catch (Exception e) {
            log.error("生成RSA密钥对失败, name={}, keySize={}, userId={}, error={}", name, keySize, userId, e.getMessage(), e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }
    
    private String generateSshRsaPublicKey(KeyPair keyPair) {
        log.info("开始生成SSH格式公钥");
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            keyPair.writePublicKey(baos, "Generated by KCDE");
            log.info("成功生成SSH格式公钥");
            return new String(baos.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("生成SSH格式公钥失败, error={}", e.getMessage(), e);
            throw new RuntimeException("生成SSH格式公钥失败", e);
        }
    }
    
    private String generateOpenSshPrivateKey(KeyPair keyPair) {
        log.info("开始生成OpenSSH格式私钥");
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            keyPair.writePrivateKey(baos);
            log.info("成功生成OpenSSH格式私钥");
            return new String(baos.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("生成OpenSSH格式私钥失败, error={}", e.getMessage(), e);
            throw new RuntimeException("生成OpenSSH格式私钥失败", e);
        }
    }

    @Override
    public RsaKey uploadPublicKey(String name, String publicKey, String userId) {
        log.info("开始上传RSA公钥, name={}, userId={}", name, userId);
        // 创建并保存密钥信息
        RsaKey rsaKey = new RsaKey();
        rsaKey.setName(name);
        rsaKey.setType("用户上传");
        rsaKey.setPublicKey(publicKey);
        rsaKey.setUserId(userId);
        rsaKey.setIsActive(true);
        rsaKey.setCreateTime(LocalDateTime.now());
        save(rsaKey);
        log.info("成功保存上传的RSA公钥信息, name={}, userId={}", name, userId);

        return rsaKey;
    }

    @Override
    public boolean validateSshPublicKey(String publicKey) {
        log.info("开始验证SSH公钥格式");
        if (publicKey == null || publicKey.trim().isEmpty()) {
            log.info("SSH公钥为空");
            return false;
        }

        String[] parts = publicKey.trim().split("\\s+", 3);
        if (parts.length < 2) {
            log.info("SSH公钥格式错误: 缺少必要部分");
            return false;
        }

        // 验证密钥类型
        String keyType = parts[0];
        if (!"ssh-rsa".equals(keyType)) {
            log.info("SSH公钥类型错误: 期望ssh-rsa, 实际{}", keyType);
            return false;
        }

        // 验证Base64部分
        String keyData = parts[1];
        try {
            // 尝试解码Base64
            byte[] decoded = Base64.getDecoder().decode(keyData);

            // 验证解码后的数据是否符合SSH公钥格式
            // SSH公钥格式: [length][ssh-rsa][length][exponent][length][modulus]
            if (decoded.length < 10) { // 最小长度检查
                log.info("SSH公钥数据长度不足");
                return false;
            }

            // 验证密钥类型字段
            byte[] typeLength = new byte[4];
            System.arraycopy(decoded, 0, typeLength, 0, 4);
            int length = ((typeLength[0] & 0xFF) << 24) |
                    ((typeLength[1] & 0xFF) << 16) |
                    ((typeLength[2] & 0xFF) << 8) |
                    (typeLength[3] & 0xFF);

            if (length <= 0 || length > decoded.length - 4) {
                log.info("SSH公钥类型字段长度无效");
                return false;
            }

            log.info("SSH公钥格式验证通过");
            return true;
        } catch (IllegalArgumentException e) {
            // Base64解码失败
            log.info("SSH公钥Base64解码失败: {}", e.getMessage());
            return false;
        }
    }
}
