package com.ksyun.auth.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ksyun.auth.dao.PrivilegeMapper;
import com.ksyun.auth.dao.PrivilegePermissionGroupMapper;
import com.ksyun.auth.dao.UserMapper;
import com.ksyun.auth.service.PermissionGroupService;
import com.ksyun.auth.service.event.PrivilegeDataExportEvent;
import com.ksyun.auth.service.event.PrivilegeFileReceiveEvent;
import com.ksyun.auth.service.listener.PrivilegeFileReceiveEventListener;
import com.ksyun.auth.dto.PrivilegeExcelDto;
import com.ksyun.auth.dto.PrivilegeExcelRowDto;
import com.ksyun.auth.service.strategy.ImportStrategy;
import com.ksyun.auth.vo.*;
import com.ksyun.common.enums.PermissionGroupSourceEnum;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.utils.DateUtil;
import com.ksyun.common.utils.StringEscapeUtils;
import com.ksyun.common.entity.PrivilegeProps;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.entity.AppAksk;
import com.ksyun.common.entity.Category;
import com.ksyun.common.entity.PermissionGroup;
import com.ksyun.common.entity.PrivilegePermissionGroup;
import com.ksyun.common.entity.RolePermissionGroup;
import com.ksyun.common.entity.RolePrivilege;
import com.ksyun.auth.dao.AppAkskMapper;
import com.ksyun.auth.dao.CategoryMapper;
import com.ksyun.auth.dao.GroupRoleMapper;
import com.ksyun.auth.dao.PermissionGroupMapper;
import com.ksyun.auth.dao.PrivilegePropsMapper;
import com.ksyun.auth.dao.RolePermissionGroupMapper;
import com.ksyun.auth.dao.RolePrivilegeMapper;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.enums.PermissionStatusEnum;
import com.ksyun.auth.service.strategy.privilege.DefaultMenuConvertStrategy;
import com.ksyun.auth.service.strategy.privilege.HideConvertStrategy;
import com.ksyun.auth.service.strategy.privilege.NetConvertStrategy;
import com.ksyun.auth.service.strategy.privilege.PositionConvertStrategy;
import com.ksyun.auth.service.strategy.privilege.ServiceConvertStrategy;
import com.ksyun.auth.service.strategy.privilege.TypeConvertStrategy;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *群组和权限服务类
 *主要用于给某个群组赋予权限
 */
@Service
@Slf4j
public class PermissionGroupServiceImpl implements PermissionGroupService {

    @Autowired
    private AppAkskMapper appAkskMapper;
    @Autowired
    private PermissionGroupMapper permissionGroupMapper;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private RolePermissionGroupMapper rolePermissionGroupMapper;
    @Autowired
    private PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    private PrivilegePropsMapper privilegePropsMapper;
    @Autowired
    private PrivilegeMapper privilegeMapper;
    @Autowired
    private RolePrivilegeMapper rolePrivilegeMapper;
    @Autowired
    ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private Map<String, ImportStrategy> strategies;

    @Autowired
    private GroupRoleMapper groupRoleMapper;

    @Autowired
    UserMapper userMapper;

    @Override
    public Page<PermissionGroup> getPermissionGroups(PermissionGroupQueryVo vo) {
        //必须要有categoryId才可查询此类别下的权限组
        QueryWrapper<PermissionGroup> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
        queryWrapper.like(!StringUtil.isEmpty(vo.getName()), "name", StringEscapeUtils.escapeSqlQueryStr(vo.getName()));
        queryWrapper.orderByDesc("create_time");

        Long count = permissionGroupMapper.selectCount(queryWrapper);
        int totalPageSize = (int) Math.ceil((double) count / vo.getPageSize());
        if (vo.getPageNo() > totalPageSize) {
            vo.setPageNo(totalPageSize);
        }
        Page<PermissionGroup> page = new Page<>(vo.getPageNo(), vo.getPageSize());
        Page<PermissionGroup> permissionGroupPage = permissionGroupMapper.selectPage(page, queryWrapper);
        return permissionGroupPage;
    }

    @Override
    public List<CategoryVo> getPermissionGroupsAll(PermissionGroupQueryVo vo) {
        List<Category> categoryList = categoryMapper.selectList(null);
        List<CategoryVo> categoryPermissionGroupsVoList = new ArrayList<>();
        for (Category category : categoryList) {
            QueryWrapper<PermissionGroup> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
            queryWrapper.eq("category_id", category.getId());
            queryWrapper.like(null != vo.getName(), "name", StringEscapeUtils.escapeSqlQueryStr(vo.getName()));
            List<PermissionGroup> permissionGroupList = permissionGroupMapper.selectList(queryWrapper);
            if (permissionGroupList == null || permissionGroupList.isEmpty()) {
                continue;
            }
            CategoryVo categoryPermissionGroupsVo = new CategoryVo();
            categoryPermissionGroupsVo.setId(category.getId());
            categoryPermissionGroupsVo.setName(category.getName());
            categoryPermissionGroupsVo.setPermissionGroupList(permissionGroupList.stream().map(p -> {
                PermissionGroupVo permissionGroupVo = new PermissionGroupVo();
                BeanUtils.copyProperties(p, permissionGroupVo);
                permissionGroupVo.setCreateTime(DateUtil.localDateTime2Date(p.getCreateTime()));
                return permissionGroupVo;
            }).collect(Collectors.toList()));

            categoryPermissionGroupsVoList.add(categoryPermissionGroupsVo);
        }
        return categoryPermissionGroupsVoList;
    }

    @Override
    public List<Long> getPermissionGroupIdsByRoleId(Long roleId) {
        QueryWrapper<RolePermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        List<Long> permissionGroupIds = rolePermissionGroupMapper.selectList(queryWrapper).stream().map(RolePermissionGroup::getPermissionGroupId).collect(Collectors.toList());
        if (permissionGroupIds == null || permissionGroupIds.isEmpty()) {
            return null;
        }
        QueryWrapper<PermissionGroup> permissionGroupQueryWrapper = new QueryWrapper<>();
        permissionGroupQueryWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
        permissionGroupQueryWrapper.in(CollectionUtil.isNotEmpty(permissionGroupIds), "id", permissionGroupIds);
        List<Long> idList = permissionGroupMapper.selectList(permissionGroupQueryWrapper).stream().map(r -> r.getId()).collect(Collectors.toList());
        log.info("getPermissionGroupIdsByRoleId selectPermissionGroupIdsByRoleId Result Count={}", idList.size());
        return idList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermissionGroup(PermissionGroupAddVo vo) {
        //查找该分类下是否有重名的权限组
        Assert.isTrue(!StringUtil.isEmpty(vo.getName()), BusinessExceptionEnum.PARAM_INVALID);
        QueryWrapper<PermissionGroup> permissionGroupQueryWrapper = new QueryWrapper<>();
        permissionGroupQueryWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
        //permissionGroupQueryWrapper.eq("category_id", vo.getCategoryId());
        permissionGroupQueryWrapper.eq("name", vo.getName());
        Long pgs = permissionGroupMapper.selectCount(permissionGroupQueryWrapper);
        Assert.isTrue(pgs == 0, BusinessExceptionEnum.PERMISSION_GROUP_EXISTED);
        PermissionGroup permissionGroup = new PermissionGroup();
        permissionGroup.setName(vo.getName());
        permissionGroup.setDescription(vo.getDescription());
        permissionGroup.setCreateBy(vo.getCreateBy());
        permissionGroup.setStatus(vo.getStatus().name());
        permissionGroup.setReadonly(1);
        permissionGroup.setSource(1);
        permissionGroup.setCreateTime(LocalDateTime.now());
        permissionGroup.setUpdateTime(LocalDateTime.now());
        int count = permissionGroupMapper.insert(permissionGroup);
        vo.setId(permissionGroup.getId());
        grantPermissionGroupPrivilegeIds(vo);
        log.info("savePermissionGroup Parameter ={} ,Result Count = {}", JSON.toJSONString(vo), count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermissionGroup(PermissionGroupDeleteVo ids) {
        /*
        删除权限组时把角色绑定关系也删除
        1,删除此权限组中的所有权限点绑定的角色关系，条件：此权限点与角色的关系不在其他权限组与角色的关系链中
        2，删除此权限组与角色的绑定关系
        3，删除此权限组与权限点的关系(3,4两步并未真正删除)
        4，删除权限组
         */
        for (Long permissionGroupId : ids.getIds()) {

            PermissionGroup permissionGroup = permissionGroupMapper.selectById(permissionGroupId);
            Assert.isTrue(!Objects.equals(permissionGroup.getSource(), PermissionGroupSourceEnum.INNER.getCode()), BusinessExceptionEnum.PERMISSION_GROUP_NOT_DELETE);


            unbindPermissionGroupPrivilegesRole(permissionGroupId);
        }
        if (CollectionUtil.isNotEmpty(ids.getIds())) {
            QueryWrapper<RolePermissionGroup> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("permission_group_id", ids.getIds());
            int deleteCount = rolePermissionGroupMapper.delete(queryWrapper);
            log.info("deleteRolePermissionGroupByPGIds ,Result Count = {}", deleteCount);

//            PermissionGroup permissionGroup = new PermissionGroup();
//            permissionGroup.setStatus(PermissionStatusEnum.DELETED.name());
//            permissionGroup.setUpdateTime(LocalDateTime.now());
            UpdateWrapper<PermissionGroup> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids.getIds());
//            int count = permissionGroupMapper.update(permissionGroup, updateWrapper);
            int count = permissionGroupMapper.delete(updateWrapper);
            log.info("deletePermissionGroup Parameter ={} ,Result Count = {}", ids, count);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePermissionGroup(PermissionGroupAddVo vo) {
        //查找该分类下是否有重名的权限组
        Assert.isTrue(!StringUtil.isEmpty(vo.getName()), BusinessExceptionEnum.PARAM_INVALID);
//        Assert.isTrue(vo.getCategoryId() != null, BusinessExceptionEnum.PARAM_INVALID);
        QueryWrapper<PermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("id", vo.getId());
        queryWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
        queryWrapper.eq("category_id", vo.getCategoryId());
        queryWrapper.eq("name", vo.getName());
        Long pgs = permissionGroupMapper.selectCount(queryWrapper);
        Assert.isTrue(pgs == 0, BusinessExceptionEnum.PERMISSION_GROUP_EXISTED);
        PermissionGroup permissionGroup = permissionGroupMapper.selectById(vo.getId());
        permissionGroup.setName(vo.getName());
//        permissionGroup.setCategoryId(vo.getCategoryId());
        if (!StringUtil.isEmpty(vo.getDescription())) {
            permissionGroup.setDescription(vo.getDescription());
        }
        permissionGroup.setUpdateTime(LocalDateTime.now());
        UpdateWrapper<PermissionGroup> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", vo.getId());
        updateWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
        int count = permissionGroupMapper.update(permissionGroup, updateWrapper);
        if (count == 1) {
            /*
            1，解绑权限组的权限点与角色的关系，条件：此权限点与角色的关系不在其他权限组与角色的关系链中
            2，授权 权限组与权限点关系，a,删除旧的权限组权限点关系，b,插入新的权限组权限点关系
            3，授权 权限组的权限点与角色的关系
             */
            unbindPermissionGroupPrivilegesRole(vo.getId());

            grantPermissionGroupPrivilegeIds(vo);

            Set<Long> permissionGroupIds = new HashSet<>();
            permissionGroupIds.add(vo.getId());
            Set<Long> roleIds = getPermissionGroupRoleIds(permissionGroupIds);
            for (Long roleId : roleIds) {
                bindRolePrivileges(roleId, vo.getPrivilegeIds());
            }
        } else {
            Assert.notNull(vo.getId(), BusinessExceptionEnum.PRIVILEGE_CAN_NOT_OPERATE);
        }
        log.info("updatePermissionGroup Parameter ={} ,Result Count = {}", JSON.toJSONString(vo), count);
    }

    @Override
    public void openOrClose(PermissionGroupOpenCloseVo vo) {
        PermissionGroup permissionGroup = permissionGroupMapper.selectById(vo.getId());
        Assert.isTrue(permissionGroup != null,"权限组信息不存在");
        permissionGroup.setStatus(vo.getStatus());
        permissionGroupMapper.updateById(permissionGroup);
    }

    @Override
    public PermissionGroupVo getPermissionGroupsById(Long permissionGroupId) {
        PermissionGroup permissionGroup = permissionGroupMapper.selectById(permissionGroupId);
        QueryWrapper<PrivilegePermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_group_id", permissionGroupId);
        List<Long> privilegeIds = privilegePermissionGroupMapper.selectList(queryWrapper).stream().map(p -> p.getPrivilegeId()).collect(Collectors.toList());
        log.info("getPermissionGroupsById selectPrivilegeIdsByPermissionGroupId Result Count={}", privilegeIds.size());
        PermissionGroupVo permissionGroupVo = new PermissionGroupVo();
        BeanUtils.copyProperties(permissionGroup, permissionGroupVo);
        permissionGroupVo.setPrivilegeIds(new HashSet<>(privilegeIds));
        permissionGroupVo.setCreateTime(DateUtil.localDateTime2Date(permissionGroup.getCreateTime()));
        return permissionGroupVo;
    }

    @Override
    public List<Category> getCategoryList() {
        return categoryMapper.selectList(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCategory(CategoryAddVo vo) {
        Assert.isTrue(!StringUtil.isEmpty(vo.getName()), BusinessExceptionEnum.PARAM_INVALID);
        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", vo.getName());
        Long count = categoryMapper.selectCount(queryWrapper);
        Assert.isTrue(count == 0, BusinessExceptionEnum.CATEGORY_EXISTED);

        Category category = new Category();
        category.setName(vo.getName());
        category.setDescription(vo.getDescription());
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        int insertCount = categoryMapper.insert(category);
        log.info("saveCategory Parameter Name ={},Result ={}", vo.getName(), insertCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCategory(CategoryAddVo vo) {
        Assert.isTrue(!StringUtil.isEmpty(vo.getName()), BusinessExceptionEnum.PARAM_INVALID);
        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("id", vo.getId());
        queryWrapper.eq("name", vo.getName());
        Long count = categoryMapper.selectCount(queryWrapper);
        Assert.isTrue(count == 0, BusinessExceptionEnum.CATEGORY_EXISTED);

        Category category = new Category();
        category.setName(vo.getName());
        if (!StringUtil.isEmpty(vo.getDescription())) {
            category.setDescription(vo.getDescription());
        }
        category.setUpdateTime(LocalDateTime.now());
        UpdateWrapper<Category> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", vo.getId());
        int updateCount = categoryMapper.update(category, updateWrapper);
        log.info("updateCategory Parameter Id ={},Result ={}", vo.getId(), updateCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(CategoryVo vo) {
        QueryWrapper<PermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", PermissionStatusEnum.NORMAL.name());
        queryWrapper.eq("category_id", vo.getId());
        Long count = permissionGroupMapper.selectCount(queryWrapper);
        //若该分类下无权限组则删除，否则不能删除
        Assert.isTrue(count == 0, BusinessExceptionEnum.PERMISSION_GROUP_CAN_NOT_BE_OPERATING);

        int result = categoryMapper.deleteById(vo.getId());
        log.info("deleteCategory Parameter Id ={},Result ={}", vo.getId(), result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBindRolePermissionGroup(GrantPermissionGroupVo vo) {

        Assert.isTrue(vo.getRoleId() != null, BusinessExceptionEnum.PARAM_INVALID);
        /*
        绑定角色和权限组
        1，解绑此角色和旧的权限组中的权限点的关系
        2，解绑角色和旧的权限组
        3，绑定角色和新的权限组
        4，绑定角色和新的权限组中的权限点的关系
         */
        unbindRolePermissionGroupPrivileges(vo.getRoleId());
        unbindRolePermissionGroups(vo.getRoleId(), null);
        bindRolePermissionGroups(vo.getRoleId(), vo.getPermissionGroupIds());
        bindRolePermissionGroupsPrivileges(vo.getRoleId(), vo.getPermissionGroupIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBindRolePermissionGroup(GrantPermissionGroupVo vo) {
        //通过权限组类别解绑
        if (vo.getCategoryIds() != null && vo.getCategoryIds().size() > 0) {
            unbindCategoryPermissionGroupPrivileges(vo.getRoleId(), vo.getCategoryIds());
        }
        //通过权限组解绑
        if (vo.getPermissionGroupIds() != null && vo.getPermissionGroupIds().size() > 0) {
            unbindPermissionGroupPrivileges(vo.getRoleId(), vo.getPermissionGroupIds());
        }
    }


    /**
     * 通过权限分类解绑角色和权限组、权限点的关系
     */
    private void unbindCategoryPermissionGroupPrivileges(Long roleId, Set<Long> categoryIds) {
        // 1、找出权限分类下的权限组
        Set<Long> permissionGroupIds = getCategoryPermissionGroupIds(categoryIds);
        // 2、解绑权限组和权限点的关系
        unbindPermissionGroupPrivileges(roleId, permissionGroupIds);
    }

    /**
     * 解绑角色和指定权限组及权限点的关系
     */
    private void unbindPermissionGroupPrivileges(Long roleId, Set<Long> permissionGroupIds) {
        // 1、查询权限组下的所有的权限点
        Set<Long> privilegeIdList = getPermissionGroupPrivilegeIds(permissionGroupIds);
        // 2、排除同时处于其它权限组的权限点
        if (CollectionUtil.isNotEmpty(privilegeIdList)) {
            LambdaQueryWrapper<PrivilegePermissionGroup> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(PrivilegePermissionGroup::getPrivilegeId, privilegeIdList);
            queryWrapper.notIn(PrivilegePermissionGroup::getPermissionGroupId, permissionGroupIds);
            List<PrivilegePermissionGroup> privilegePermissionGroupList = privilegePermissionGroupMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(privilegePermissionGroupList)) {
                Set<Long> filterPrivilegeIdList = privilegePermissionGroupList.stream().map(ppg -> ppg.getPrivilegeId()).distinct().collect(Collectors.toSet());
                privilegeIdList.removeAll(filterPrivilegeIdList);
            }
        }
        // 3、删除权限点和角色的关系
        unbindRolePrivileges(roleId, privilegeIdList);
        // 4、解绑角色和权限组的关系
        unbindRolePermissionGroups(roleId, permissionGroupIds);
    }

    /**
     * 绑定角色和权限组下的所有权限点的关系
     */
    private void bindRolePermissionGroupsPrivileges(Long roleId, Set<Long> permissionGroupIds) {
        //绑定角色和新的权限组中的权限点的关系
        Set<Long> privilegeIdList = getPermissionGroupPrivilegeIds(permissionGroupIds);
        bindRolePrivileges(roleId, privilegeIdList);
    }

    /**
     * 绑定角色和权限点
     */
    private void bindRolePrivileges(Long roleId, Set<Long> privilegeIdList) {
        int count = 0;
        if (CollectionUtil.isNotEmpty(privilegeIdList)) {
            for (Long privilegeId : privilegeIdList) {
                LambdaQueryWrapper<RolePrivilege> rolePrivilegeQueryWrapper = Wrappers.lambdaQuery();
                rolePrivilegeQueryWrapper.eq(RolePrivilege::getRoleId, roleId);
                rolePrivilegeQueryWrapper.eq(RolePrivilege::getPrivilegeId, privilegeId);
                List<RolePrivilege> rolePrivilegeList = rolePrivilegeMapper.selectList(rolePrivilegeQueryWrapper);
                //此角色与权限点的关系不存在才新建
                if (rolePrivilegeList == null || rolePrivilegeList.isEmpty()) {
                    RolePrivilege rolePrivilege = new RolePrivilege();
                    rolePrivilege.setRoleId(roleId);
                    rolePrivilege.setPrivilegeId(privilegeId);
                    rolePrivilege.setCreateTime(LocalDateTime.now());
                    rolePrivilegeMapper.insert(rolePrivilege);
                    count++;
                }
            }
        }
        log.info("bindRolePrivilege roleId ={},Result={}", roleId, count);
    }

    /**
     * 绑定角色和权限组
     */
    private void bindRolePermissionGroups(Long roleId, Set<Long> permissionGroupIds) {
        int count = 0;
        if (CollectionUtil.isNotEmpty(permissionGroupIds)) {
            for (Long permissionGroupId : permissionGroupIds) {
                RolePermissionGroup rolePermissionGroup = new RolePermissionGroup();
                rolePermissionGroup.setRoleId(roleId);
                rolePermissionGroup.setPermissionGroupId(permissionGroupId);
                rolePermissionGroup.setCreateTime(LocalDateTime.now());
                rolePermissionGroupMapper.insert(rolePermissionGroup);
                count++;
            }
        }
        log.info("bindRolePermissionGroup roleId ={},Result={}", roleId, count);
    }

    /**
     * 解绑此角色 原有的旧的权限组中的权限点的关系
     */
    private void unbindRolePermissionGroupPrivileges(Long roleId) {
        //查询此角色的所有权限组
        Set<Long> roleIdList = new HashSet<>();
        roleIdList.add(roleId);
        Set<Long> permissionGroupIdList = getRolePermissionGroupIds(roleIdList);
        //查询权限组下的所有的权限点
        Set<Long> privilegeIdList = getPermissionGroupPrivilegeIds(permissionGroupIdList);
        //删除权限点和角色的关系
        unbindRolePrivileges(roleId, privilegeIdList);
    }

    /**
     * 解绑角色与权限点的关系
     */
    private void unbindRolePrivileges(Long roleId, Set<Long> privilegeIdList) {
        if (CollectionUtil.isNotEmpty(privilegeIdList)) {
            LambdaQueryWrapper<RolePrivilege> rolePrivilegeQueryWrapper = Wrappers.lambdaQuery();
            rolePrivilegeQueryWrapper.eq(RolePrivilege::getRoleId, roleId);
            rolePrivilegeQueryWrapper.in(RolePrivilege::getPrivilegeId, privilegeIdList);
            int deleteCount = rolePrivilegeMapper.delete(rolePrivilegeQueryWrapper);
            log.info("unbindRolePermissionGroups unbindRolePermissionGroupPrivileges roleId ={}Result={}", roleId, deleteCount);
        }
    }

    /**
     * 获取权限组的所有权限点
     */
    private Set<Long> getPermissionGroupPrivilegeIds(Set<Long> permissionGroupIdList) {
        if (permissionGroupIdList == null || permissionGroupIdList.isEmpty()) {
            return Sets.newHashSet();
        }
        LambdaQueryWrapper<PrivilegePermissionGroup> privilegePermissionGroupQueryWrapper = Wrappers.lambdaQuery();
        privilegePermissionGroupQueryWrapper.in(PrivilegePermissionGroup::getPermissionGroupId, permissionGroupIdList);
        List<PrivilegePermissionGroup> privilegePermissionGroupList = privilegePermissionGroupMapper.selectList(privilegePermissionGroupQueryWrapper);
        Set<Long> privilegeIdList = privilegePermissionGroupList.stream().map(ppg -> ppg.getPrivilegeId()).distinct().collect(Collectors.toSet());
        return privilegeIdList;
    }

    /**
     * 获取角色绑定的所有权限组
     */
    private Set<Long> getRolePermissionGroupIds(Set<Long> roleIdList) {
        if (roleIdList == null || roleIdList.isEmpty()) {
            return new HashSet<>();
        }
        QueryWrapper<RolePermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIdList);
        List<RolePermissionGroup> rolePermissionGroupList = rolePermissionGroupMapper.selectList(queryWrapper);
        Set<Long> permissionGroupIdList = rolePermissionGroupList.stream().map(rgp -> rgp.getPermissionGroupId()).distinct().collect(Collectors.toSet());
        return permissionGroupIdList;
    }

    /**
     * 获取权限分类对应的权限组
     */
    private Set<Long> getCategoryPermissionGroupIds(Set<Long> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return Sets.newHashSet();
        }
        QueryWrapper<PermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("category_id", categoryIds);
        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectList(queryWrapper);
        Set<Long> permissionGroupIdList = permissionGroups.stream().map(pg -> pg.getId()).distinct().collect(Collectors.toSet());
        return permissionGroupIdList;
    }

    /**
     * 解绑角色的权限组
     */
    private void unbindRolePermissionGroups(Long roleId, Set<Long> permissionGroupIdList) {
        LambdaQueryWrapper<RolePermissionGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RolePermissionGroup::getRoleId, roleId);
        queryWrapper.in(CollectionUtil.isNotEmpty(permissionGroupIdList), RolePermissionGroup::getPermissionGroupId, permissionGroupIdList);
        int deleteCount = rolePermissionGroupMapper.delete(queryWrapper);
        log.info("unbindRolePermissionGroup roleId ={}Result={}", roleId, deleteCount);
    }

    /**
     * 解绑权限组下权限点与此权限组所绑定的角色之间的关系
     * 条件：此权限点与角色的关系不在其他权限组与角色的关系链中
     */
    private void unbindPermissionGroupPrivilegesRole(Long permissionGroupId) {
        //获得此权限组的所有权限点A
        Set<Long> permissionGroupIds = new HashSet<>();
        permissionGroupIds.add(permissionGroupId);
        Set<Long> pgPrivilegeIds = getPermissionGroupPrivilegeIds(permissionGroupIds);
        //获得此权限组的所有绑定的角色R
        Set<Long> pgRoleIds = getPermissionGroupRoleIds(permissionGroupIds);
        //获得绑定的角色的所有绑定的权限组
        Set<Long> rolesPermissionGroupIds = getRolePermissionGroupIds(pgRoleIds);
        //除了待删除权限组外，逐个查询所有绑定的权限组中的权限点，将所有的权限点放在一起B
        rolesPermissionGroupIds.removeAll(permissionGroupIds);
        Set<Long> rolePermissionGroupPrivilegeIds = getPermissionGroupPrivilegeIds(rolesPermissionGroupIds);
        //在A中不在B中的，就是需要删除的权限点与角色的关系。
        pgPrivilegeIds.removeAll(rolePermissionGroupPrivilegeIds);

        for (Long roleId : pgRoleIds) {
            unbindRolePrivileges(roleId, pgPrivilegeIds);
        }
    }

    /**
     * 获取与权限组绑定的所有角色
     */
    private Set<Long> getPermissionGroupRoleIds(Set<Long> permissionGroupIdList) {
        if (permissionGroupIdList == null || permissionGroupIdList.isEmpty()) {
            return null;
        }
        QueryWrapper<RolePermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("permission_group_id", permissionGroupIdList);
        List<RolePermissionGroup> rolePermissionGroupList = rolePermissionGroupMapper.selectList(queryWrapper);
        Set<Long> roleIdList = rolePermissionGroupList.stream().map(rolePermissionGroup -> rolePermissionGroup.getRoleId()).collect(Collectors.toSet());
        return roleIdList;
    }

    /**
     * 解绑 权限与权限分组关系
     */
    private void unbindPermissionGroupPrivileges(Long permissionGroupId) {
        QueryWrapper<PrivilegePermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_group_id", permissionGroupId);
        int count = privilegePermissionGroupMapper.delete(queryWrapper);
        log.info("unBindPermissionGroupPrivileges Parameter permissionGroupId = {},Result Count ={}", permissionGroupId, count);
    }

    /**
     * 绑定权限与权限分组关系
     */
    private void bindPermissionGroupPrivileges(Long permissionGroupId, Set<Long> privilegeIds) {
        for (Long privilegeId : privilegeIds) {
            PrivilegePermissionGroup privilegePermissionGroup = new PrivilegePermissionGroup();
            privilegePermissionGroup.setPrivilegeId(privilegeId);
            privilegePermissionGroup.setPermissionGroupId(permissionGroupId);
            privilegePermissionGroup.setCreateTime(LocalDateTime.now());
            int count = privilegePermissionGroupMapper.insert(privilegePermissionGroup);
            log.info("bindPermissionGroupPrivileges Parameter Id = {}, privilegeIds={},Result Count ={}", permissionGroupId, privilegeIds, count);
        }
    }

    /**
     * 授权 权限组与权限点关系
     */
    private void grantPermissionGroupPrivilegeIds(PermissionGroupAddVo vo) {
        unbindPermissionGroupPrivileges(vo.getId());
        if (vo.getPrivilegeIds().size() > 0) {
            bindPermissionGroupPrivileges(vo.getId(), vo.getPrivilegeIds());
        }
        log.info("grantPermissionGroupPrivilegeIds Parameter ={}, Success", JSON.toJSONString(vo));
    }

    @Override
    public void receive(MultipartFile file, PrivilegeFileReceiveEventListener.PrivilegeScope scope) throws IOException {
        String fileName = file.getOriginalFilename();
        String extension = fileName.substring(fileName.lastIndexOf("."));
        Assert.isTrue(StringUtils.equalsIgnoreCase(extension, ".xls") || StringUtils.equalsIgnoreCase(extension, ".xlsx") || StringUtils.equalsIgnoreCase(extension, ".x"), BusinessExceptionEnum.FILE_TYPE_INVALID);
        int model = 1;
        applicationEventPublisher.publishEvent(new PrivilegeFileReceiveEvent(file.getInputStream(), fileName, model, scope));
    }

    @Override
    public String exportPermissionGroup(PrivilegeFileReceiveEventListener.PrivilegeScope scope) {
        String fileName = "privilege-" + System.currentTimeMillis() + ".xlsx";
        applicationEventPublisher.publishEvent(new PrivilegeDataExportEvent(fileName, scope));
        return fileName;
    }

    @Override
    public List getAllPrivileges() {
        return permissionGroupMapper.selectAllPrivileges();
    }

    @Override
    public List<CategoryVo> getCategoryPermissionGroups(Long roleId) {
        log.info("getCategoryPermissionGroups Parameter={}", roleId);

        QueryWrapper<RolePermissionGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        Set<Long> permissionGroupIds = rolePermissionGroupMapper.selectList(queryWrapper).stream()
                .map(RolePermissionGroup::getPermissionGroupId).collect(Collectors.toSet());
        log.info("getCategoryPermissionGroups permissionGroupIds Result={} ", permissionGroupIds);
        List<CategoryVo> results = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(permissionGroupIds)) {
            QueryWrapper<PermissionGroup> permissionGroupQueryWrapper = new QueryWrapper<>();
            permissionGroupQueryWrapper.in("id", permissionGroupIds);
            List<PermissionGroup> permissionGroups = permissionGroupMapper.selectList(permissionGroupQueryWrapper);
            if (CollectionUtil.isNotEmpty(permissionGroups)) {
                Set<Long> categoryIds = permissionGroups.stream().map(PermissionGroup::getCategoryId).collect(Collectors.toSet());
                log.info("getCategoryPermissionGroups Result permissionGroups Size={}, categoryIds={}", permissionGroupIds.size(), categoryIds);
                if (CollectionUtil.isNotEmpty(categoryIds)) {
                    QueryWrapper<Category> categoryQueryWrapper = new QueryWrapper<>();
                    categoryQueryWrapper.in("id", categoryIds);
                    List<Category> categories = categoryMapper.selectList(categoryQueryWrapper);
                    if (CollectionUtil.isNotEmpty(categories)) {
                        categories.forEach(category -> {
                            CategoryVo categoryVo = new CategoryVo();
                            BeanUtils.copyProperties(category, categoryVo);
                            categoryVo.setPermissionGroups(buildCategoryPermissionGroupMap(permissionGroups, categoryIds).get(category.getId()));
                            results.add(categoryVo);
                        });
                    }
                }
            }
            return results;
        }


        return results;
    }

    private List<PrivilegeExcelRowDto> aggregate(List<PrivilegeExcelDto> list) {
        List<PrivilegeExcelRowDto> excelDTOList = Lists.newArrayList();
        List<AppAksk> appAksks = appAkskMapper.selectList(null);
        Map<String, String> akMap = Maps.newHashMap();
        for (AppAksk appAksk : appAksks) {
            akMap.put(appAksk.getAk(), appAksk.getDescription());
        }
        Map<PrivilegeExcelDto, List<PrivilegeExcelDto>> collect = list.stream().collect(Collectors.groupingBy(PrivilegeExcelDto::getCode))
                .entrySet().stream().collect(Collectors.toMap(kv -> kv.getValue().get(0), kv -> kv.getValue()));

        for (Map.Entry<PrivilegeExcelDto, List<PrivilegeExcelDto>> kv : collect.entrySet()) {
            PrivilegeExcelDto excelDTO = kv.getKey();
            PrivilegeExcelRowDto dto = new PrivilegeExcelRowDto();
            BeanUtils.copyProperties(excelDTO, dto);
            List<PrivilegeExcelDto> value = kv.getValue();
            if (value.size() > 1) {
                dto.setGroupName(StringUtils.join(value.stream().map(PrivilegeExcelDto::getGroupName).collect(Collectors.toSet()), ","));
            }
            Set<String> position = Sets.newHashSet();
            Set<String> hide = Sets.newHashSet();
            Set<String> defaultMenu = Sets.newHashSet();
            Set<String> net = Sets.newHashSet();
            Set<String> service = Sets.newHashSet();
            for (PrivilegeExcelDto privilegeExcelData : value) {
                for (PrivilegeProps privilegeProp : privilegeExcelData.getPrivilegeProps()) {
                    if (privilegeProp.getPropKey().equals("isIndex")
                            || privilegeProp.getPropKey().equals("positionDown")
                            || privilegeProp.getPropKey().equals("jumpOutAlone")) {
                        PositionConvertStrategy.PositionEnum positionEnum = PositionConvertStrategy.PositionEnum.keyOf(privilegeProp.getPropKey());
                        if (positionEnum != null) {
                            position.add(positionEnum.getLabel());
                        }
                    }
                    if (HideConvertStrategy.PROP_KEY.equals(privilegeProp.getPropKey())) {
                        HideConvertStrategy.HideEnum hideEnum = HideConvertStrategy.HideEnum.valOf(privilegeProp.getPropValue());
                        if (hideEnum != null) {
                            hide.add(hideEnum.getLabel());
                        }
                    }
                    if (ServiceConvertStrategy.PROP_KEY.equals(privilegeProp.getPropKey())) {
                        ServiceConvertStrategy.ServiceEnum serviceEnum = ServiceConvertStrategy.ServiceEnum.valOf(privilegeProp.getPropValue());
                        if (serviceEnum != null) {
                            service.add(serviceEnum.getLabel());
                        }
                    }
                    if (privilegeProp.getPropKey().equals("isDefault")) {
                        DefaultMenuConvertStrategy.DefaultMenuEnum defaultMenuEnum = DefaultMenuConvertStrategy.DefaultMenuEnum.valOf(privilegeProp.getPropValue());
                        if (defaultMenuEnum != null) {
                            defaultMenu.add(defaultMenuEnum.getLabel());
                        }
                    }
                    if (privilegeProp.getPropKey().equals("OFFICE_NETWORK")
                            || privilegeProp.getPropKey().equals("PROD_CLOUD")
                            || privilegeProp.getPropKey().equals("SOM")
                            || privilegeProp.getPropKey().equals("PUBLIC")
                            || privilegeProp.getPropKey().equals("VPC_JUMP")
                            || privilegeProp.getPropKey().equals("DEV_TEST_CLOUD")) {
                        NetConvertStrategy.NetEnum netEnum = NetConvertStrategy.NetEnum.keyOf(privilegeProp.getPropKey());
                        if (netEnum != null) {
                            net.add(netEnum.getLabel());
                        }
                    }
                }
            }

            dto.setAk(akMap.get(excelDTO.getAk()));
            dto.setHideProp(StringUtils.join(hide, ","));
            dto.setServiceProp(StringUtils.join(service, ","));
            dto.setNetProp(StringUtils.join(net, ","));
            dto.setDefaultProp(StringUtils.join(defaultMenu, ","));
            dto.setPositionProp(StringUtils.join(position, ","));
            dto.setType(TypeConvertStrategy.TypeEnum.codeOf(Integer.parseInt(dto.getType())).getLabel());
            excelDTOList.add(dto);
        }
        return excelDTOList;
    }

    private Map<Long, List<PermissionGroup>> buildCategoryPermissionGroupMap(List<PermissionGroup> permissionGroups, Set<Long> categoryIds) {
        Map<Long, List<PermissionGroup>> map = new HashMap<>();
        categoryIds.forEach(categoryId -> {
            List<PermissionGroup> list = new ArrayList<>();
            for (PermissionGroup permissionGroup : permissionGroups) {
                if (Objects.equals(permissionGroup.getCategoryId(), categoryId)) {
                    list.add(permissionGroup);
                }
            }
            map.put(categoryId, list);
        });
        log.debug("buildCategoryPermissionGroupMap map Result={}", JSON.toJSONString(map));
        return map;
    }

}
