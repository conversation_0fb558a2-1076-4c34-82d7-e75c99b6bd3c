package com.ksyun.auth.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.vo.*;
import com.ksyun.common.entity.UserTenant;

import java.util.List;

public interface TenantService {
    /**
     * 添加租户
     */
    void add(KcdeTenantAddVo parameter);

    /**
     * 修改租户信息
     */
    void update(KcdeTenantUpdateVo parameter);

    /**
     * 删除租户信息
     */
    void delete(Long parameter);

    /**
     * 查询租户列表
     */
    List<KcdeTenantVo> list(KcdeTenantQueryVo parameter);

    /**
     * 查询租户列表
     */
    Page<KcdeTenantVo> page(KcdeTenantQueryVo parameter);

    /**
     * 根据租户Id查询租户
     */
    KcdeTenantVo getTenantById(Long tenantId);

    /**
     * 根据租户名称查询租户
     */
    KcdeTenantVo getTenantByName(String tenantName);

    /**
     * 根据指定租户下以绑定的用户
     */
    List<UserTenant> getTenantUsers(Long tenantId);

    /**
     * 添加租户用户
     */
    void addTenantUser(Long tenantId, Long userId);

    /**
     * 删除租户用户
     */
    void deleteTenantUser(Long tenantId, Long userId);

    /**
     * 批量删除租户用户
     */
    void batchDeleteTenantUser(KcdeTenantUserDeleteVo parameter);

    /**
     * 租户用户分页查询
     */
    Page<BaseUser> tenantUserPage(KcdeTenantUserQueryVo parameter);

    /**
     * 租户用户全量，不带分页
     */
    List<BaseUser> tenantUsers(KcdeTenantUserQueryVo parameter);
}