/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.ksyun.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020
 */
@Component(Constants.PRIVILEGE_OVERWRITE_STRATEGY)
@Slf4j
public class PrivilegeOverwriteStrategyPrivilege extends AbstractPrivilegeImportStrategy {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImport(List list) {
        log.info("OverwriteStrategy...");
    }

}
