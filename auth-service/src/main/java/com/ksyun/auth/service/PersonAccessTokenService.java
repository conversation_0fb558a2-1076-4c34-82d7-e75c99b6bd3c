package com.ksyun.auth.service;

import com.ksyun.common.entity.PersonAccessToken;

import java.util.List;

/**
 * 个人访问令牌服务接口
 */
public interface PersonAccessTokenService {
    
    /**
     * 创建个人访问令牌
     * 如果已存在则先删除再创建新的
     *
     * @param issueUrl 认证服务地址
     * @param userId   用户ID
     * @param username 用户名
     * @return 创建的令牌
     */
    PersonAccessToken createToken(String issueUrl, Long userId, String username);

    /**
     * 获取用户的访问令牌列表
     *
     * @param userId 用户ID
     * @return 令牌列表
     */
    List<PersonAccessToken> getTokens(Long userId);

    /**
     * 按照userId获取PAT
     * @param userId
     * @return
     */
    PersonAccessToken getTokenByUserId(Long userId);

    /**
     * 验证个人访问令牌的有效性
     *
     * @param token JWT格式的个人访问令牌
     * @return 如果token有效返回true，否则返回false
     */
    boolean validateToken(String token);
}
