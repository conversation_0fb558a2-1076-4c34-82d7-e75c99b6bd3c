package com.ksyun.auth.service.impl;

import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.dao.TokenMapper;
import com.ksyun.auth.service.TokenService;
import com.ksyun.common.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
public class TokenServiceImpl implements TokenService {
    private static final Logger RUN_LOG = LoggerFactory.getLogger(TokenServiceImpl.class);
    private static final AtomicBoolean SKIP_THE_FIRST_GENERATE_ON_APP_START = new AtomicBoolean(true);

    @Resource
    private TokenMapper tokenMapper;

    @Override
    public String getDsk() {
        return tokenMapper.lookupLatestDsk(1).iterator().next();
    }

    @Override
    public String getTenantAkSk(String tenant) {
        return tokenMapper.lookupTenantAkSk(tenant);
    }

    @Override
    public Collection<String> getAppTokens(String ak) {
        Collection<String> lastTwoDynamicKeys = tokenMapper.lookupLatestDsk(2);
        String sk = tokenMapper.lookupAppSk(ak);
        return StringUtils.isEmpty(sk) ? Collections.emptySet() : encodeTokens(sk, lastTwoDynamicKeys);
    }

    @Override
    public Collection<String> getTenantTokens(String tenant) {
        Collection<String> lastTwoDynamicKeys = tokenMapper.lookupLatestDsk(2);
        String aksk = tokenMapper.lookupTenantAkSk(tenant);
        String sk = StringUtils.isEmpty(aksk) ? null : aksk.split(",")[1];
        return StringUtils.isEmpty(sk) ? Collections.emptySet() : encodeTokens(sk, lastTwoDynamicKeys);
    }

    @Override
    public void addDynamicKeyOnPeriod() {
        if (SKIP_THE_FIRST_GENERATE_ON_APP_START.get()) {
            SKIP_THE_FIRST_GENERATE_ON_APP_START.compareAndSet(true, false);
        } else {
            generateAndSaveDynamicKey();
        }
    }

    private void generateAndSaveDynamicKey() {
        String dsk = DateUtil.getDateByFormat(null);
        int saved = tokenMapper.saveDsy(Long.valueOf(dsk), dsk);
        if (saved == 1) {
            RUN_LOG.info("Saved Dynamic Key, period={}, dsk={}", dsk, dsk);
        } else {
            RUN_LOG.info("Skipped Generate Dynamic Key, period={}, dsk={}", dsk, dsk);
        }
    }

    private Collection<String> encodeTokens(String sk, Collection<String> dsks) {
        return dsks.stream().map(dsk -> CommonUtils.encodeByMd5(sk, dsk)).collect(Collectors.toSet());
    }
}
