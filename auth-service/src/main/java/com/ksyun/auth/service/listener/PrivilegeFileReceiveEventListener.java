/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ksyun.auth.dao.*;
import com.ksyun.auth.dto.PrivilegeExcelDto;
import com.ksyun.auth.dto.PrivilegeExcelRowDto;
import com.ksyun.auth.dto.RoleExcelDto;
import com.ksyun.auth.dto.RolePermissionGroupExcelDto;
import com.ksyun.auth.service.event.PrivilegeFileReceiveEvent;
import com.ksyun.auth.service.strategy.ImportStrategy;
import com.ksyun.auth.service.strategy.PrivilegeImportStrategyEnum;
import com.ksyun.auth.service.strategy.privilege.ConvertStrategy;
import com.ksyun.common.annotation.Column;
import com.ksyun.common.entity.PrivilegeProps;
import com.ksyun.common.entity.Role;
import com.ksyun.common.entity.RolePermissionGroup;
import com.ksyun.common.enums.SystemTypeEnum;
import com.ksyun.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component
public class PrivilegeFileReceiveEventListener implements ApplicationListener<PrivilegeFileReceiveEvent> {

    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";

    private static final String UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_GROUP_NAME_SQL = "UPDATE role_permission_group, permission_group SET role_permission_group.permission_group_name = permission_group.name WHERE role_permission_group.permission_group_id= permission_group.id";

    private static final String UPDATE_ROLE_PRIVILEGE_PRIVILEGE_CODE_SQL = "UPDATE role_privilege, privilege SET role_privilege.privilege_code = privilege.code WHERE role_privilege.privilege_id= privilege.id";

    private static final String UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_GROUP_ID_SQL = "UPDATE role_permission_group, permission_group SET role_permission_group.permission_group_id = permission_group.id, role_permission_group.create_time = now() WHERE role_permission_group.permission_group_name = permission_group.name";

    private static final String UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_ROLE_ID_SQL = "UPDATE role_permission_group, role SET role_permission_group.role_id = role.id WHERE role_permission_group.role_name = role.name";

    private static final String UPDATE_ROLE_PRIVILEGE_PRIVILEGE_ID_SQL = "UPDATE role_privilege, privilege SET role_privilege.privilege_id = privilege.id, role_privilege.create_time = now() WHERE role_privilege.privilege_code = privilege.code";

    private static final String UPDATE_BASIC_TAGS_SQL = "INSERT INTO basic_tags(`id`, `name`, `key`, `group`, `role_type_apply`) " +
            "SELECT `id`, `name`, `code`, " +
            "CASE WHEN `type` =  '1' THEN '平台权限' " +
            "WHEN `type` =  '2' THEN '项目权限' " +
            "ELSE '' " +
            "END, `type` FROM `role` WHERE `default` = '1'";

    private static final String DELETE_ROLE_PERMISSION_GROUP_SQL = "DELETE role_permission_group FROM role INNER JOIN role_permission_group ON role_permission_group.role_id = role.id WHERE role.default = '1'";

    private static final String UPDATE_ROLE_PERMISSION_GROUP_SQL = "INSERT INTO role_permission_group(role_id, permission_group_id, permission_group_name, create_time) " +
            "SELECT distinct role.id, permission_group.id, permission_group.name, NOW() " +
            "FROM role INNER JOIN permission_group ON (permission_group.name like CONCAT('%_', role.name))";

    private static final String DELETE_ROLE_PRIVILEGE_SQL = "DELETE role_privilege FROM role INNER JOIN role_privilege ON role_privilege.role_id = role.id WHERE role.default = '1'";

    private static final String UPDATE_ROLE_PRIVILEGE_SQL =  "INSERT INTO role_privilege(role_id, privilege_id, privilege_code, create_time) " +
            "SELECT role_permission_group.role_id, privilege_permission_group.privilege_id, privilege.code, now() " +
            "FROM role_permission_group " +
            "INNER JOIN privilege_permission_group ON(role_permission_group.permission_group_id = privilege_permission_group.permission_group_id) " +
            "INNER JOIN privilege ON(privilege.id = privilege_permission_group.privilege_id) " +
            "GROUP BY role_permission_group.role_id, privilege_permission_group.privilege_id, privilege.code";

    @Autowired
    private Map<String, ConvertStrategy> convertStrategies;
    @Autowired
    private Map<String, ImportStrategy> importStrategies;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private PrivilegeMapper privilegeMapper;
    @Autowired
    private PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    private PrivilegePropsMapper privilegePropsMapper;
    @Autowired
    private PermissionGroupMapper permissionGroupMapper;
    @Autowired
    private BasicTagsMapper basicTagsMapper;
    @Autowired
    private RolePermissionGroupMapper rolePermissionGroupMapper;

    public enum PrivilegeScope {
        Internal(0),
        External(1);
        private int val;

        PrivilegeScope(int val) {
            this.val = val;
        }

        public int getVal() {
            return val;
        }

        public void setVal(int val) {
            this.val = val;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(PrivilegeFileReceiveEvent privilegeFileReceiveEvent) {
        String filePath = privilegeFileReceiveEvent.getFilePath();
        int model = privilegeFileReceiveEvent.getModel();
        Triple triple = readExcel(filePath, privilegeFileReceiveEvent.getIn());

        List<PrivilegeExcelDto> privilegeExcelData = (List<PrivilegeExcelDto>)triple.getLeft();
        if (privilegeExcelData == null || privilegeExcelData.size() == 0) {
            throw new RuntimeException("导入失败：平台权限数据不存在");
        }

        if (privilegeFileReceiveEvent.getScope() == PrivilegeScope.Internal) {
            List<RoleExcelDto> roleExcelData = (List<RoleExcelDto>)triple.getMiddle();
            log.info("平台角色数据条数为：{}",roleExcelData.size());

            jdbcTemplate.batchUpdate(
                    UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_GROUP_NAME_SQL,
                    UPDATE_ROLE_PRIVILEGE_PRIVILEGE_CODE_SQL,
                    UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_GROUP_ID_SQL,
                    UPDATE_ROLE_PRIVILEGE_PRIVILEGE_ID_SQL);
            if (model == 1) {
                privilegeMapper.delete(new QueryWrapper<>());
                privilegePermissionGroupMapper.delete(new QueryWrapper<>());
                privilegePropsMapper.delete(new QueryWrapper<>());
                permissionGroupMapper.delete(new QueryWrapper<>());
            }
            //1、处理权限点
            Map<PrivilegeImportStrategyEnum, List<PrivilegeExcelDto>> privilegeExcelDataMap = privilegeExcelData.stream().collect(Collectors.groupingBy(PrivilegeExcelDto::getOperation));
            for (Map.Entry<PrivilegeImportStrategyEnum, List<PrivilegeExcelDto>> entry : privilegeExcelDataMap.entrySet()) {
                if (Objects.isNull(entry.getKey()) || Objects.isNull(entry.getValue()) || entry.getValue().isEmpty()) {
                    continue;
                }
                String operationType = entry.getKey().getType();
                if (model == 1) {
                    if (PrivilegeImportStrategyEnum.UPDATE.getType().equals(operationType)) {
                        operationType = PrivilegeImportStrategyEnum.IGNORE.getType();
                    }
                }
                importStrategies.get(operationType).doImport(entry.getValue());
            }

            //2、处理角色
            if (roleExcelData != null && roleExcelData.size() > 0) {
                List<Role> roles = roleMapper.getAllDefaultRoles();
                Map<String, Role> rolesCodeMap = roles.stream().collect(Collectors.toMap(Role::getCode, Role -> Role));

                // 只处理默认角色，自定义角色不影响
                // 不能删除已经存在的role，只能更新， 因为在别的表中还存了role的id
                roleExcelData.forEach(p -> {
                    try{
                        Role role = new Role();
                        role.setCode(p.getCode());
                        role.setName(p.getName());
                        role.setDescription(p.getDescription());
                        role.setType(Integer.parseInt(p.getType()));
                        role.setCreateTime(LocalDateTime.now());
                        role.setCreateBy("系统");
                        role.setUpdateTime(LocalDateTime.now());
                        role.setDefaultRole("1");
                        role.setSource(SystemTypeEnum.BIGDATA.getCode());
                        role.setPlatform(p.getPlatform());
                        Long id = rolesCodeMap.get(p.getCode()) == null ? null : rolesCodeMap.get(p.getCode()).getId();
                        if (id == null) {
                            roleMapper.insert(role);
                        } else {
                            role.setId(id);
                            roleMapper.updateById(role);
                        }
                        rolesCodeMap.remove(p.getCode());
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                });

                rolesCodeMap.keySet().forEach(
                    p -> {
                        roleMapper.delete(new QueryWrapper<Role>().eq("code", p).eq("`default`", "1"));
                    }
                );

                basicTagsMapper.delete(new QueryWrapper<>());
                jdbcTemplate.update(UPDATE_BASIC_TAGS_SQL);

                //3、处理角色和权限组
                List<RolePermissionGroupExcelDto> rolePermissionGroupExcelData = (List<RolePermissionGroupExcelDto>)triple.getRight();
                log.info("权限组-角色关系数据条数为：{}",rolePermissionGroupExcelData.size());
                for(RolePermissionGroupExcelDto rpgDto : rolePermissionGroupExcelData){
                    String permissionGroupName = rpgDto.getPermissionGroupName();
                    if(StringUtils.isNotEmpty(permissionGroupName)){
                        String[] permissionGroupNameArr = permissionGroupName.replaceAll("\\n","").replaceAll("\\r\n","").split(",");
                        for(String groupName : permissionGroupNameArr){
                            RolePermissionGroup rolePermissionGroup = new RolePermissionGroup();
                            rolePermissionGroup.setRoleId(0L);
                            rolePermissionGroup.setPermissionGroupId(0L);
                            rolePermissionGroup.setRoleName(rpgDto.getRoleName());
                            rolePermissionGroup.setPermissionGroupName(groupName);
                            rolePermissionGroup.setCreateTime(LocalDateTime.now());
                            rolePermissionGroupMapper.insert(rolePermissionGroup);
                        }
                    }
                }
                // role_permission_group
                jdbcTemplate.update(DELETE_ROLE_PERMISSION_GROUP_SQL);
                jdbcTemplate.update(UPDATE_ROLE_PERMISSION_GROUP_SQL);

                // role_privilege
                jdbcTemplate.update(DELETE_ROLE_PRIVILEGE_SQL);
                jdbcTemplate.update(UPDATE_ROLE_PRIVILEGE_SQL);
            }

            jdbcTemplate.batchUpdate(
                    UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_ROLE_ID_SQL,
                    UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_GROUP_NAME_SQL,
                    UPDATE_ROLE_PRIVILEGE_PRIVILEGE_CODE_SQL,
                    UPDATE_ROLE_PERMISSION_GROUP_PERMISSION_GROUP_ID_SQL,
                    UPDATE_ROLE_PRIVILEGE_PRIVILEGE_ID_SQL);
        } else {
            importStrategies.get(PrivilegeImportStrategyEnum.EXTERNAL.getType()).doImport(privilegeExcelData);
        }

    }

    public Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase(XLS)) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase(XLSX)) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    public Workbook getWorkbook(File file) throws Exception {
        Workbook workbook = WorkbookFactory.create(file);
        return workbook;
    }

    private Triple readExcel(String fileName, InputStream inputStream) {
        Workbook workbook = null;
        try {
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
            workbook = getWorkbook(inputStream, fileType);
            return new ImmutableTriple(
                    parsePrivilegeExcelData(workbook),
                    parseRoleExcelData(workbook),
                    parseRolePermissionGroupExcelData(workbook)
            );
        } catch (Exception e) {
            log.error("解析Excel失败，文件名：" + fileName, e);
            throw new BusinessException("解析Excel失败", e.getMessage());
        } finally {

            if (null != workbook) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.warn("关闭数据流出错！错误信息：", e);
                    return null;
                }
            }

            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.warn("关闭数据流出错！错误信息：", e);
                    return null;
                }
            }
        }
    }

    private List<PrivilegeExcelDto> parsePrivilegeExcelData(Workbook workbook) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException, InstantiationException {
        List<PrivilegeExcelDto> resultDataList = new ArrayList<>();
        // 解析sheet
        // 兼容旧版本，没有 平台权限 工作表，默认取第一个
        Sheet sheet = workbook.getSheet("KCDE权限点");
        if (sheet == null) {
            sheet = workbook.getSheetAt(0);
        }

        // 获取第一行数据：第一行数据为标题，没有任何数据
        int firstRowNum = 2;
        // 解析每一行的数据，构造数据对象，跳过表头
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            PrivilegeExcelDto resultData = convert(bind(row));
            if (null == resultData) {
                continue;
            }
            if (resultData.getGroupName().contains(",")) {
                String[] groupNames = resultData.getGroupName().split(",", -1);

                for (int i = 0; i < groupNames.length; i++) {
                    String group = StringUtils.trimToEmpty(groupNames[i]);
                    //过滤空组
                    if (StringUtils.isEmpty(group)) {
                        continue;
                    }
                    PrivilegeExcelDto rd = (PrivilegeExcelDto) BeanUtils.cloneBean(resultData);
                    rd.setGroupName(group);
                    resultDataList.add(rd);
                }
            } else {
                resultDataList.add(resultData);
            }
        }

        return resultDataList;
    }

    private List<RoleExcelDto> parseRoleExcelData(Workbook workbook) {
        Sheet sheet = workbook.getSheet("内置角色");
        if (sheet == null) {
            return Lists.newArrayList();
        }
        List<RoleExcelDto> resultDataList = Lists.newArrayList();
        // 获取第一行数据：第一行数据为标题，没有任何数据
        int firstRowNum = 1;
        // 解析每一行的数据，构造数据对象，跳过表头
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            RoleExcelDto resultData = new RoleExcelDto();
            // 角色编码
            Cell cell = row.getCell(0, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            String code = convertToString(cell);
            if (StringUtils.isEmpty(code)) {
                continue;
            }
            resultData.setCode(code);

            // 角色名称
            cell = row.getCell(1, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            resultData.setName(convertToString(cell));

            // 角色类型(1 平台级别 2项目级别)
            cell = row.getCell(2, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            String type = convertToString(cell);
            if (StringUtils.isEmpty(type)) {
                continue;
            }
            resultData.setType(type);

            // 角色描述
            cell = row.getCell(3, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            resultData.setDescription(convertToString(cell));

            cell = row.getCell(4, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            resultData.setPlatform(convertToString(cell));

            resultDataList.add(resultData);
        }

        return resultDataList;


    }

    private List<RolePermissionGroupExcelDto> parseRolePermissionGroupExcelData(Workbook workbook) {
        Sheet sheet = workbook.getSheet("权限组-角色关系");
        if (sheet == null) {
            return Lists.newArrayList();
        }
        List<RolePermissionGroupExcelDto> resultDataList = Lists.newArrayList();
        // 获取第一行数据：第一行数据为标题，没有任何数据
        int firstRowNum = 0;
        // 解析每一行的数据，构造数据对象，跳过表头
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            RolePermissionGroupExcelDto resultData = new RolePermissionGroupExcelDto();

            // 权限组名称
            Cell cell = row.getCell(0, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            String permissionGroupName = convertToString(cell);
            if (StringUtils.isEmpty(permissionGroupName)) {
                continue;
            }
            resultData.setPermissionGroupName(permissionGroupName);

            // 角色
            cell = row.getCell(1, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            String roleName = convertToString(cell);
            if (StringUtils.isEmpty(roleName)) {
                continue;
            }
            resultData.setRoleName(roleName);
            resultDataList.add(resultData);
        }
        return resultDataList;
    }

    /*public List<PrivilegeExcelDto> readExcel(String fileName) {
        Workbook workbook = null;
        FileInputStream inputStream = null;
        try {
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            File excelFile = new File(fileName);
            if (!excelFile.exists()) {
                log.warn("指定的Excel文件不存在！");
                throw new BusinessException("文件不存在:" + fileName);
            }
            inputStream = new FileInputStream(excelFile);
            workbook = getWorkbook(inputStream, fileType);
//            workbook = getWorkbook(excelFile);
            List<PrivilegeExcelDto> resultDataList = parseExcel(workbook);
            return resultDataList;
        } catch (Exception e) {
            log.warn("解析Excel失败，文件名：" + fileName + " 错误信息：" + e.getMessage());
            throw new BusinessException("解析Excel失败", e.getMessage());
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.warn("关闭数据流出错！错误信息：" + e.getMessage());
                return null;
            }
        }
    }*/

    /*private List<PrivilegeExcelDto> parseExcel(Workbook workbook) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException, InstantiationException {
        List<PrivilegeExcelDto> resultDataList = new ArrayList<>();
        // 解析sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 获取第一行数据
        int firstRowNum = 1;
        Row firstRow = sheet.getRow(firstRowNum);
        if (null == firstRow) {
            log.warn("解析Excel失败，在第一行没有读取到任何数据！");
        }
        // 解析每一行的数据，构造数据对象，跳过表头
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            PrivilegeExcelDto resultData = convert(bind(row));
            if (null == resultData) {
                log.warn("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                continue;
            }
            if (resultData.getGroupName().contains(",")) {
                for (String group : resultData.getGroupName().split(",", -1)) {
                    //过滤空组
                    if (org.apache.commons.lang3.StringUtils.isBlank(group)) {
                        continue;
                    }
                    PrivilegeExcelDto rd = (PrivilegeExcelDto) BeanUtils.cloneBean(resultData);
                    rd.setGroupName(group);
                    resultDataList.add(rd);
                }
            } else {
                resultDataList.add(resultData);
            }
        }

        return resultDataList;
    }*/

    private String convertToString(Cell cell) {
        if (cell == null) {
            return "";
        }
        return getValue(cell);
    }

    /**
     * 单元格取值处理
     *
     * @param cell
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/8/19 17:30
     **/
    private String getValue(Cell cell) {
        if (cell.getCellType() == HSSFCell.CELL_TYPE_BOOLEAN) {
            // 返回布尔类型的值
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            double doubleVal = cell.getNumericCellValue();
            Object inputValue = null;
            BigDecimal bdVal = new BigDecimal(doubleVal);
            if ((bdVal + ".0").equals(Double.toString(doubleVal))) {
                //判断是否含有小数位.0
                inputValue = bdVal;
            } else {
                inputValue = doubleVal;
            }
            return String.valueOf(inputValue);
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_STRING) {
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        } else {
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        }
    }

    private PrivilegeExcelRowDto bind(Row row) throws InvocationTargetException, IllegalAccessException {
        PrivilegeExcelRowDto privilegeExcelRow = new PrivilegeExcelRowDto();
        Field[] fields = privilegeExcelRow.getClass().getDeclaredFields();
        boolean check = true;
        for (Field field : fields) {
            Column annotation = field.getDeclaredAnnotation(Column.class);
            int index = annotation.index();
            String s = convertToString(row.getCell(index));
            s = s.replace("，", ",");
            int length = annotation.length();
            if (length > 0 && s.length() > length) {
                log.warn("第{}行{}列期望长度小于等于{}，实际长度是{}",
                        row.getRowNum(), annotation.index(), length, s.length());
                check = false;
                break;
            }
            boolean b = annotation.notNull();
            if (b && StringUtils.isEmpty(s)) {
                log.warn("第{}行{}列期望值不为空，实际值为空",
                        row.getRowNum(), annotation.index());
                check = false;
                break;
            }
            BeanUtils.setProperty(privilegeExcelRow, field.getName(), s);
        }
        if (!check) {
            return null;
        }
        return privilegeExcelRow;
    }

    public PrivilegeExcelDto convert(PrivilegeExcelRowDto row) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        if (row == null) {
            return null;
        }
        PrivilegeExcelDto privilegeExcelData = new PrivilegeExcelDto();
        List<PrivilegeProps> list = Lists.newArrayList();
        Field[] fields = row.getClass().getDeclaredFields();
        for (Field field : fields) {
            Column annotation = field.getDeclaredAnnotation(Column.class);
            String s = BeanUtils.getProperty(row, field.getName());

            if (annotation.strategy() == Column.Strategy.NONE) {
                BeanUtils.setProperty(privilegeExcelData, field.getName(), s);
            } else {
                if (annotation.type() == Column.Type.MAJOR) {
                    BeanUtils.setProperty(privilegeExcelData, field.getName(), convertStrategies.get(annotation.strategy().name()).fromExcel(s));
                } else {
                    list.addAll((List<PrivilegeProps>) convertStrategies.get(annotation.strategy().name()).fromExcel(s));
                }
            }
        }
        privilegeExcelData.setPrivilegeProps(list);
        return privilegeExcelData;
    }

}
