/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.listener;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ksyun.auth.service.event.BackupEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component
public class BackupEventListener implements ApplicationListener<BackupEvent> {

    static Map<String, List<String>> headerMap = Maps.newHashMap();

    static {
        headerMap.put("category", Lists.newArrayList("id", "name", "description", "create_time", "update_time"));
        headerMap.put("permission_group", Lists.newArrayList("id", "name", "description", "create_time", "update_time"));
        headerMap.put("privilege_permission_group", Lists.newArrayList("privilege_id", "permission_group_id", "create_time"));
        headerMap.put("privilege", Lists.newArrayList("id", "code", "name", "type", "icon", "order", "parent_id", "url", "business", "ak", "create_time"));
        headerMap.put("privilege_props", Lists.newArrayList("privilege_id", "prop_key", "prop_value"));
    }

    @Override
    public void onApplicationEvent(BackupEvent backupEvent) {
        backup(backupEvent.getMap());
    }


    private void backup(Map<String, List> map) {
        /*
            待有具体需求时加
         */
    }

}
