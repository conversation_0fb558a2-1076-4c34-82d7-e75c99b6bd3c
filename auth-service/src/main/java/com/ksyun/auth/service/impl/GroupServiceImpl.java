package com.ksyun.auth.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dao.GroupMapper;
import com.ksyun.auth.dao.UserMapper;
import com.ksyun.auth.dao.UserPropsMapper;
import com.ksyun.auth.dao.UserRoleMapper;
import com.ksyun.auth.dto.GroupSimpleDto;
import com.ksyun.auth.dto.UserSimpleDto;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.utils.DateUtil;
import com.ksyun.auth.vo.UserGroupBindVo;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.entity.Group;
import com.ksyun.common.entity.GroupRole;
import com.ksyun.common.entity.GroupUser;
import com.ksyun.common.entity.Role;
import com.ksyun.common.entity.User;
import com.ksyun.common.entity.UserRole;
import com.ksyun.auth.dao.GroupRoleMapper;
import com.ksyun.auth.dao.GroupUserMapper;
import com.ksyun.auth.dao.RoleMapper;
import com.ksyun.common.exception.Assert;
import com.ksyun.auth.service.GroupService;
import com.ksyun.auth.vo.GroupAddVo;
import com.ksyun.auth.vo.GroupQueryVo;
import com.ksyun.auth.vo.GroupUpdateVo;
import com.ksyun.auth.vo.GroupVo;
import com.ksyun.auth.vo.RoleVo;
import com.ksyun.auth.vo.UserGroupPairVo;
import com.ksyun.auth.vo.UserVo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class GroupServiceImpl implements GroupService {

    @Resource
    private UserMapper userMapper;
    @Resource
    private GroupMapper groupMapper;
    @Resource
    private GroupRoleMapper groupRoleMapper;
    @Resource
    private GroupUserMapper groupUserMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    RoleMapper roleMapper;

    @Resource
    UserPropsMapper userPropsMapper;

    /**
     * 通过用户Id获取角色互斥状态，状态为ENABLE时无法进行多角色赋值
     *
     * @param userId
     * @return
     */
    private boolean getRoleRoleMutexStatus(Long userId) {
        return false;
//        String roleMutexStatus = userPropsMapper.getRoleRoleMutexStatusByUserId(userId);
//        log.info("roleMutexStatus={}", roleMutexStatus);
//        if ("ENABLE".equals(roleMutexStatus)) {
//            return true;
//        }
//        return false;
    }


    /**
     * 当前用户选择多个组时，告知只能选择一个组
     */
    private void getUserOnlyOneGroup(UserGroupBindVo parameter) {

        Set<String> roleNames = new HashSet<>();

        if (getRoleRoleMutexStatus(parameter.getTenantId())) {
            if (parameter.getGroupIds().size() > 1) {
                Assert.moreThanOneRole(true, "只能选择一个群组");
            } else {

                //当前用户选择一个组时，判断当前用户是否有角色
                QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("user_id", parameter.getUserIds().iterator().next());
                List<UserRole> userRoles = userRoleMapper.selectList(queryWrapper);
                for (UserRole userRole : userRoles) {
                    Role role = roleMapper.selectById(userRole.getRoleId());
                    roleNames.add(role.getName());
                }
            }

            if (roleNames.size() > 0) {
                Assert.moreThanOneRole(true, "一个用户只能属于一个角色或者一个群组");
            }
        }
    }

    /**
     * 当前组添加多个用户，如果用户包含其它组中，告知
     */
    private Set<String> getUserOnlyOneGroupByUserId(boolean roleMutexStatus, Set<Long> userIds, Long groupId) {
        //用户名称
        Set<String> userNames = new HashSet<>();
        Set<String> roleNames = new HashSet<>();
        if (roleMutexStatus) {
            for (Long userId : userIds) {
                //当前组选择一个用户时，判断当前用户是否有角色
                QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("user_id", userId);
                List<UserRole> userRoles = userRoleMapper.selectList(queryWrapper);
                for (UserRole userRole : userRoles) {
                    Role role = roleMapper.selectById(userRole.getRoleId());
                    roleNames.add(role.getName());
                }
                if (roleNames.size() > 0) {
                    Assert.moreThanOneRole(true, "一个用户只能属于一个角色或者一个群组");
                }
                List<GroupVo> groupList = groupUserMapper.getGroupListByUserId(userId);
                if (groupList.size() > 0) {
                    for (GroupVo group : groupList) {
                        //去除当前组中的其他用户
                        if (!groupId.equals(group.getId())) {
                            userNames.add(userMapper.lookupUserByUserId(userId).getName() == null ? "" : userMapper.lookupUserByUserId(userId).getName());
                        }
                    }
                }
            }
        }
        return userNames;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGroup(GroupAddVo parameter) {

        Group group = getGroupByTenantIdAndName(parameter.getTenantId(), parameter.getName());
        Assert.isTrue(group == null, BusinessExceptionEnum.GROUP_EXISTED);
        group = new Group();
        group.setName(parameter.getName());
        group.setDescription(parameter.getDescription());
        group.setTenantId(parameter.getTenantId());
        group.setCreateTime(LocalDateTime.now());
        group.setUpdateTime(LocalDateTime.now());
        group.setCreateBy(parameter.getCreateBy());
        group.setUpdateBy(parameter.getUpdateBy());
        groupMapper.insert(group);
    }

    private Group getGroupByTenantIdAndName(Long tenantId, String name) {
        QueryWrapper<Group> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.eq("name", name);
        return groupMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(GroupUpdateVo parameter) {

        Group group = getGroupByTenantIdAndGroupId(parameter.getTenantId(), parameter.getId());
        Assert.isTrue(group != null, BusinessExceptionEnum.GROUP_NOT_FOUND);
        if (!Objects.equals(group.getName(), parameter.getName())) {
            Group groupRepeat = getGroupByTenantIdAndName(parameter.getTenantId(), parameter.getName());
            Assert.isTrue(groupRepeat == null, BusinessExceptionEnum.GROUP_EXISTED);
        }

        if (!StringUtil.isEmpty(parameter.getName())) {
            group.setName(parameter.getName());
        }
        if (!StringUtil.isEmpty(parameter.getDescription())) {
            group.setDescription(parameter.getDescription());
        }
        group.setUpdateTime(LocalDateTime.now());
        group.setUpdateBy(parameter.getUpdateBy());
        UpdateWrapper<Group> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", parameter.getId());
        groupMapper.update(group, updateWrapper);
    }

    private Group getGroupByTenantIdAndGroupId(Long tenantId, Long groupId) {
        QueryWrapper<Group> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.eq("id", groupId);
        return groupMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long tenantId, Long groupId) {
        Group group = getGroupByTenantIdAndGroupId(tenantId, groupId);
        Assert.isTrue(group != null, BusinessExceptionEnum.GROUP_NOT_FOUND);

        // 分组与角色解绑
        QueryWrapper<GroupRole> groupRoleQueryWrapper = new QueryWrapper<>();
        groupRoleQueryWrapper.eq("group_id", groupId);
        int deletedBindings = groupRoleMapper.delete(groupRoleQueryWrapper);
        log.info("Deleted Group Role Bindings For Delete Group: deleted={}", deletedBindings);

        // 分组与用户解绑
        QueryWrapper<GroupUser> groupUserQueryWrapper = new QueryWrapper<>();
        groupUserQueryWrapper.eq("group_id", groupId);
        deletedBindings = groupUserMapper.delete(groupUserQueryWrapper);
        log.info("Deleted Group User Bindings For Delete Group: deleted={}", deletedBindings);

        // 删除分组
        int deletedGroups = groupMapper.deleteById(groupId);
        log.info("Deleted Group: groupId={}, deletedCount={}", groupId, deletedGroups);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBind(UserGroupBindVo parameter) {

        /**
         * 绑定用户组的时候，组多个的时候提示多个组
         */
        getUserOnlyOneGroup(parameter);

        int insertCount = 0;
        if (CollectionUtil.isNotEmpty(parameter.getUserIds()) && CollectionUtil.isNotEmpty(parameter.getGroupIds())) {
            // 检查分组是否存在
            parameter.getGroupIds().forEach(groupId -> {
                Group group = getGroupByTenantIdAndGroupId(parameter.getTenantId(), groupId);
                Assert.isTrue(group != null, BusinessExceptionEnum.GROUP_NOT_FOUND);
            });

            //检查用户是否存在
            Long userCount = userMapper.selectCount(new QueryWrapper<User>().in("id", parameter.getUserIds()).eq("tenant_id", parameter.getTenantId()));
            Assert.isTrue(userCount == parameter.getUserIds().size(), BusinessExceptionEnum.USER_NOT_FOUND);

            //解绑旧的用户群组交集
            Set<Long> intersectionGroupIds = getGroupIdsByUserIds(parameter.getUserIds());
            int deleteCount = 0;
            if (CollectionUtil.isNotEmpty(intersectionGroupIds)) {
                List<UserGroupPairVo> deleteOldPairs = UserGroupBindVo.newUserGroupPairs(parameter.getUserIds(), intersectionGroupIds);
                if (CollectionUtil.isNotEmpty(deleteOldPairs)) {
                    for (UserGroupPairVo pair : deleteOldPairs) {
                        deleteCount += deleteGroupUser(pair.getUserId(), pair.getGroupId());
                    }
                }
            }
            log.info("unBind User Groups, users={}, groups={}, size={}", JSON.toJSONString(parameter.getUserIds()), JSON.toJSONString(intersectionGroupIds), deleteCount);
            //插入新的用户群组交集
            List<UserGroupPairVo> pairs = UserGroupBindVo.newUserGroupPairs(parameter.getUserIds(), parameter.getGroupIds());
            if (CollectionUtil.isNotEmpty(pairs)) {
                for (UserGroupPairVo pair : pairs) {
                    insertCount += insertGroupUser(pair.getUserId(), pair.getGroupId());
                }
            }
        }
        log.info("Bind User Groups, users={}, groups={}, size={}", JSON.toJSONString(parameter.getUserIds()), JSON.toJSONString(parameter.getGroupIds()), insertCount);
    }

    private int deleteGroupUser(Long userId, Long groupId) {
        if (userId != null && groupId != null) {
            QueryWrapper<GroupUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("group_id", groupId);
            return groupUserMapper.delete(queryWrapper);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserBind(Long tenantId, Long groupId, Set<Long> userIds) {
        if (tenantId != null && groupId != null && CollectionUtil.isNotEmpty(userIds)) {

            Set<String> userNames = getUserOnlyOneGroupByUserId(getRoleRoleMutexStatus(tenantId), userIds, groupId);
            if (userNames.size() > 0) {
                Assert.moreThanOneRole(true, "一个用户只能选择一个群组。" + userNames + "已经包含在其他群组中，不允许进行添加。");
            }

            //检查用户是否存在
            Long userCount = userMapper.selectCount(new QueryWrapper<User>().in("id", userIds).eq("tenant_id", tenantId));
            Assert.isTrue(userCount == userIds.size(), BusinessExceptionEnum.USER_NOT_FOUND);

            // 检查分组是否存在
            QueryWrapper<Group> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.in("id", groupId);
            Group group = groupMapper.selectOne(queryWrapper);
            if (group != null) {
                int delete = groupUserMapper.delete(new QueryWrapper<GroupUser>().eq("group_id", groupId));
                log.info("groupId [{}] unbind {} users [{}]", groupId, delete, userIds);
                int insert = groupUserMapper.batchInsertUser(group.getId(), userIds);
                log.info("groups [{}] bind {} users [{}]", group.getId(), insert, userIds);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGroupBind(Long tenantId, Long userId, Set<Long> groupIds) {
        if (tenantId != null && userId != null && CollectionUtil.isNotEmpty(groupIds)) {

            //检查分组是否存在
            Long groupCount = groupMapper.selectCount(new QueryWrapper<Group>().in("id", groupIds));
            Assert.isTrue(groupCount == groupIds.size(), BusinessExceptionEnum.GROUP_NOT_FOUND);

            // 检查用户是否存在
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.in("id", userId);
            User user = userMapper.selectOne(queryWrapper);
            if (user != null) {
                int delete = groupUserMapper.delete(new QueryWrapper<GroupUser>().eq("user_id", userId));
                log.info("groupId [{}] unbind {} users [{}]", groupIds, delete, userId);
                int insert = groupUserMapper.batchInsertGroup(userId, groupIds);
                log.info("groups [{}] bind {} users [{}]", groupIds, insert, userId);
            }
        }
    }

    private int insertGroupUser(Long userId, Long groupId) {
        if (userId != null && groupId != null) {
            GroupUser groupUser = new GroupUser();
            groupUser.setUserId(userId);
            groupUser.setGroupId(groupId);
            groupUser.setCreateTime(LocalDateTime.now());
            try {
                groupUserMapper.insert(groupUser);
                return 1;
            } catch (DuplicateKeyException e) {
                log.debug("insertGroupUser catch DuplicateKeyException userId={},groupId={}", userId, groupId);
            }
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBind(UserGroupBindVo parameter) {
        int deleteCount = 0;
        if (CollectionUtil.isNotEmpty(parameter.getUserIds()) && CollectionUtil.isNotEmpty(parameter.getGroupIds())) {
            //检查用户与分组进行绑定
            QueryWrapper<GroupUser> qw = new QueryWrapper<>();
            qw.in("group_id",parameter.getGroupIds());
            List<Long> gus = groupUserMapper.selectList(qw).stream().map(GroupUser::getUserId).collect(Collectors.toList());
            parameter.getUserIds().forEach(userId -> {
                boolean isInGroup = gus.contains(userId);
                Assert.isTrue(isInGroup, BusinessExceptionEnum.GROUP_NOT_BINDING);
            });
            //解绑用户和分组
            QueryWrapper<GroupUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("user_id", parameter.getUserIds());
            queryWrapper.in("group_id", parameter.getGroupIds());
            deleteCount = groupUserMapper.delete(queryWrapper);
        }
        log.info("Unbind User Groups, users={}, groups={}, size={}", parameter.getUserIds(), parameter.getGroupIds(), deleteCount);
    }


    @Override
    public Page<GroupVo> getForPage(GroupQueryVo parameter) {
        QueryWrapper<Group> queryWrapper = buildQuery(parameter);
        log.info("group all parameter ={}, queryWrapper={}", JSON.toJSONString(parameter), JSON.toJSONString(queryWrapper));
        Page<Group> groupPage = groupMapper.selectPage(new Page<>(parameter.getPageNo(), parameter.getPageSize()), queryWrapper);
        List<GroupVo> collect = groupPage.getRecords().stream().map(g -> {
            GroupVo groupVo = new GroupVo();
            BeanUtils.copyProperties(g, groupVo);
            groupVo.setCreateTime(DateUtil.toDefaultString(g.getCreateTime()));
            groupVo.setUpdateTime(DateUtil.toDefaultString(g.getUpdateTime()));
            LambdaQueryWrapper<GroupRole> groupRoleLambdaQueryWrapper = Wrappers.lambdaQuery();
            groupRoleLambdaQueryWrapper.eq(GroupRole::getGroupId, groupVo.getId());
            List<GroupRole> groupRoles = groupRoleMapper.selectList(groupRoleLambdaQueryWrapper);
            Set<Long> roleIds = groupRoles.stream().map(GroupRole::getRoleId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(roleIds)) {
                for (Long roleId : roleIds) {
                    Role role = roleMapper.get(roleId);
                    RoleVo roleVo = new RoleVo();
                    BeanUtils.copyProperties(role, roleVo);
                    roleVo.setCreateTime(DateUtil.toDefaultString(role.getCreateTime()));
                    roleVo.setUpdateTime(DateUtil.toDefaultString(role.getUpdateTime()));
                    groupVo.getRoles().add(roleVo);
                }
            }
            List<GroupUser> groupUsers = groupUserMapper.selectList(new QueryWrapper<GroupUser>().eq("group_id", groupVo.getId()).select("user_id"));
            if (CollectionUtil.isNotEmpty(groupUsers)) {
                groupVo.setUserIds(groupUsers.stream().map(GroupUser::getUserId).collect(Collectors.toSet()));
            }
            return groupVo;
        }).collect(Collectors.toList());
        log.info("queryForPage MiddleResult groupList={},groupVoList={}", JSON.toJSONString(groupPage.getRecords()), JSON.toJSONString(collect));
        Page<GroupVo> page1 = new Page<>();
        page1.setTotal(groupPage.getTotal());
        page1.setCurrent(groupPage.getCurrent());
        page1.setSize(groupPage.getSize());
        page1.setPages(groupPage.getPages());
        page1.setRecords(collect);
        return page1;
    }

    private QueryWrapper<Group> buildQuery(GroupQueryVo parameter) {
        QueryWrapper<Group> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", parameter.getTenantId());
        queryWrapper.in(parameter.getGroupIds() != null && !parameter.getGroupIds().isEmpty(), "id", parameter.getGroupIds());
        queryWrapper.eq(parameter.getId() != null, "id", parameter.getId());
        //若查询条件为空或者为空格，则当做无效条件
        queryWrapper.apply(StringUtil.isNotEmpty(parameter.getName()) && StringUtil.isNotBlank(parameter.getName()), "instr(name, '" + parameter.getName() + "')");
        queryWrapper.apply(StringUtil.isNotEmpty(parameter.getDescription()) && StringUtil.isNotBlank(parameter.getDescription()), "instr(description, '" + parameter.getDescription() + "')");
        if (CollectionUtil.isNotEmpty(parameter.getRoleIds())) {
            if (parameter.isRoleBindGroup()) {
                queryWrapper.apply("id in (select distinct group_id from group_role where role_id in (" + StringUtils.join(parameter.getRoleIds(), ",") + "))");
            } else {
                queryWrapper.apply("id not in (select distinct group_id from group_role where role_id in (" + StringUtils.join(parameter.getRoleIds(), ",") + "))");
            }
        }
        queryWrapper.orderByDesc("id");
        return queryWrapper;
    }

    @Override
    public List<GroupSimpleDto> getAllGroupSimple(GroupQueryVo parameter) {
        QueryWrapper<Group> queryWrapper = buildQuery(parameter).select("id", "name", "description");
        log.info("group all parameter ={}, queryWrapper={}", JSON.toJSONString(parameter), JSON.toJSONString(queryWrapper));
        return groupMapper.selectList(queryWrapper)
                .stream()
                .map(g -> {
                    GroupSimpleDto groupSimple = new GroupSimpleDto();
                    BeanUtils.copyProperties(g, groupSimple);
                    return groupSimple;
                })
                .collect(Collectors.toList());
    }

    @Override
    public long getGroupCount(long tenantId) {
        QueryWrapper<Group> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        return groupMapper.selectCount(queryWrapper);
    }

    @Override
    public List<Long> getGroupRoleIdsByUserId(Long userId) {
        List<Long> groupIds = groupUserMapper.selectList(new QueryWrapper<GroupUser>().eq("user_id", userId))
                .stream().map(GroupUser::getGroupId).collect(Collectors.toList());

        List<Long> groupRoleIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(groupIds)) {
            groupRoleIds = groupRoleMapper.selectList(new QueryWrapper<GroupRole>().in("group_id", groupIds))
                    .stream().map(GroupRole::getRoleId).collect(Collectors.toList());
        }
        log.info("lookupGroupRoleIdsByUserId Parameter userId={},groupIds={},groupRoleIds={}", userId, JSON.toJSONString(groupIds), JSON.toJSONString(groupRoleIds));
        return groupRoleIds;
    }

    @Override
    public List<GroupSimpleDto> getGroupsByUserIds(Set<Long> userIds) {
        Set<Long> intersectionGroupIds = getGroupIdsByUserIds(userIds);
        List<GroupSimpleDto> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(intersectionGroupIds)) {
            result = groupMapper.selectList(new QueryWrapper<Group>().in("id", intersectionGroupIds))
                    .stream()
                    .map(g -> {
                        GroupSimpleDto groupSimple = new GroupSimpleDto();
                        BeanUtils.copyProperties(g, groupSimple);
                        return groupSimple;
                    })
                    .collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public Map<String, List<UserSimpleDto>> getUserBound(long groupId, AuthUser authUser) {
        Map<String, List<UserSimpleDto>> map = new HashMap<>();
        map.put("all", Lists.newArrayList());
        map.put("bound", Lists.newArrayList());
        List<User> users = userMapper.selectList(new QueryWrapper<User>()
                .eq("tenant_id", authUser.getTenant().getId())
//                .eq("status", UserVo.Status.NORMAL)
                .in("status", UserVo.Status.NORMAL, UserVo.Status.SLEEP, UserVo.Status.LOGOUT)
                .apply("tenant_id != id"));
        if (CollectionUtil.isNotEmpty(users)) {
            List<UserSimpleDto> userSimples = users.stream().map(u -> {
                UserSimpleDto userSimple = new UserSimpleDto();
                userSimple.setId(u.getId());
                userSimple.setName(u.getName());
                return userSimple;
            }).collect(Collectors.toList());
            map.get("all").addAll(userSimples);
            List<GroupUser> groupUsers = groupUserMapper.selectList(new QueryWrapper<GroupUser>().eq("group_id", groupId));
            if (null != groupUsers && groupUsers.size() > 0) {
                Set<Long> userIds = groupUsers.stream().map(GroupUser::getUserId).collect(Collectors.toSet());
                List<User> bound = userMapper.selectList(new QueryWrapper<User>().in("id", userIds));
                if (CollectionUtil.isNotEmpty(bound)) {
                    List<UserSimpleDto> boundUserSimples = bound.stream().map(u -> {
                        UserSimpleDto userSimple = new UserSimpleDto();
                        userSimple.setId(u.getId());
                        userSimple.setName(u.getName());
                        return userSimple;
                    }).collect(Collectors.toList());
                    map.get("bound").addAll(boundUserSimples);
                }
                log.info("userBound boundUserIds ={}", userIds);
            }
        }
        log.info("userBound parameter groupId={},tenantId={},Result={}", groupId, authUser.getTenant().getId(), JSON.toJSONString(map));
        return map;
    }

    /**
     * 获取所有用户所属群组的交集
     */
    private Set<Long> getGroupIdsByUserIds(Set<Long> userIds) {
        Set<Long> intersectionGroupIds = new HashSet<>();
        if (CollectionUtil.isNotEmpty(userIds)) {
            List<Long> allUserGroupIds = groupUserMapper.selectList(new QueryWrapper<GroupUser>().in("user_id", userIds))
                    .stream().map(GroupUser::getGroupId).collect(Collectors.toList());
            //将List转换为Set
            Set<Long> uniqueSet = new HashSet(allUserGroupIds);
            //判断每一个groupId 出现的次数，出现次数与用户数量相同则说明此groupid为交集元素
            for (Long groupId : uniqueSet) {
                if (Objects.equals(Collections.frequency(allUserGroupIds, groupId), userIds.size())) {
                    intersectionGroupIds.add(groupId);
                }
            }
        }
        log.info("getGroupsByUserIds userIds={},Intersection groupIds={}", JSON.toJSONString(userIds), JSON.toJSONString(intersectionGroupIds));
        return intersectionGroupIds;
    }

    private List<Long> lookupUserRoleIdsByUserId(Long userId) {
        List<Long> userRoleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().eq("user_id", userId).select("role_id"))
                .stream().map(UserRole::getRoleId).collect(Collectors.toList());
        log.info("lookupUserRolesByUserId userId={},userRoleIds={}", userId, userRoleIds);
        return userRoleIds;
    }

}
