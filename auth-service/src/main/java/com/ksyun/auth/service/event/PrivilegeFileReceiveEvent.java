/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.event;

import com.ksyun.auth.service.listener.PrivilegeFileReceiveEventListener;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2020
 */
@Getter
@Setter
public class PrivilegeFileReceiveEvent extends ApplicationEvent {
    private InputStream in;
    private String filePath;
    /** 导入方式（1：清空原有权限点，其他：仅做增加不做覆盖） */
    private int model;
    private PrivilegeFileReceiveEventListener.PrivilegeScope scope;

    public PrivilegeFileReceiveEvent(InputStream in, String filePath, int model, PrivilegeFileReceiveEventListener.PrivilegeScope scope) {
        super(filePath);
        this.in = in;
        this.filePath = filePath;
        this.model = model;
        this.scope = scope;
    }
}
