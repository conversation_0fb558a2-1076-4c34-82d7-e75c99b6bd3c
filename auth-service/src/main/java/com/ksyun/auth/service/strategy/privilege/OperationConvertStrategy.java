package com.ksyun.auth.service.strategy.privilege;

import com.ksyun.auth.service.strategy.PrivilegeImportStrategyEnum;
import com.ksyun.common.constant.Constants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 操作转换策略
 */
@Slf4j
@Component(Constants.OPERATION)
public class OperationConvertStrategy implements ConvertStrategy<String, PrivilegeImportStrategyEnum, String, String> {

    @Override
    public PrivilegeImportStrategyEnum fromExcel(String s) {
        OperationEnum operationEnum = OperationEnum.nameOf(s);
        if (Objects.isNull(operationEnum)) {
            return PrivilegeImportStrategyEnum.IGNORE;
        }
        return operationEnum.getPrivilegeImportStrategyEnum();
    }

    @Override
    public String toExcel(String s) {
        return "新增";
    }

    @Getter
    public enum OperationEnum {
        ADD("ADD", "新增", PrivilegeImportStrategyEnum.IGNORE),
        UPDATE("UPDATE", "修改", PrivilegeImportStrategyEnum.UPDATE),
        DELETE("DELETE", "删除", PrivilegeImportStrategyEnum.DELETE);
        private final String code;
        private final String label;
        private final PrivilegeImportStrategyEnum privilegeImportStrategyEnum;

        OperationEnum(String code, String label, PrivilegeImportStrategyEnum privilegeImportStrategyEnum) {
            this.code = code;
            this.label = label;
            this.privilegeImportStrategyEnum = privilegeImportStrategyEnum;
        }

        public static OperationEnum nameOf(String label) {
            for (OperationEnum t : OperationEnum.values()) {
                if (Objects.equals(label, t.label)) {
                    return t;
                }
            }
            return null;
        }

        public static OperationEnum codeOf(String code) {
            for (OperationEnum t : OperationEnum.values()) {
                if (t.code.equals(code)) {
                    return t;
                }
            }
            return null;
        }
    }
}
