package com.ksyun.auth.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.vo.*;
import com.ksyun.common.entity.User;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface UserService {
    /**
     * 注册用户到本地
     */
    void addUser(UserAddVo parameter);

    /**
     * 注册用户
     * @param userAddOidcVo
     */
    Map<String,Object> addUser(UserAddOidcVo userAddOidcVo);

//    /**
//     * 注册租户
//     */
//    void addTenant(TenantAddVo parameter);

    /**
     * 修改用户信息
     */
    void update(UserVo parameter);

    /**
     * 删除用户信息
     */
    void delete(UserDeleteVo userDeleteParameter);

    /**
     * 用户分页查询
     */
    Page<BaseUser> page(UserQueryVo parameter);

    /**
     * 根据用户Id查询用户
     */
    Optional<UserVo> getUserById(Long userId);

    /**
     * 检查用户是否存在
     */
    void getUserExist(Set<Long> userIds);

    /**
     * 根据用户名查询用户
     */
    Optional<List<UserVo>> getUserByName(String userName);

    /**
     * 查询租户信息
     *
     * @param tenantName
     * @return
     */
    //UserVo getTenantByName(String tenantName);

    /**
     * 修改密码
     */
    void updatePassword(UserPasswordVo parameter, boolean isRestPasswordFlag);

    /**
     * 子账号首次登录时重置密码
     */
    void subAccountResetPasswordFirstLogin(SubAccountPasswordResetVo parameter);


    /**
     * 重置密码
     * @param parameter
     */
    void resetPassword(SubAccountPasswordResetVo parameter);

    /**
     * 设置用户的密级等级
     */
    void updateUserSecretLevel(SecretLevelSetVo parameter);

    AuthUser getUserByLoginVo(String userNameVo, String passwordVo,String loginType, String rsaPrivateKey, String source);

    AuthUser getUserBySourceAndOidcId(String source,String oidcId);

    int updateCustomRoleStatus(UpdateCustomRoleStatusVo updateCustomRoleStatusVo);

    //boolean getTenantCustomRoleFlag(Long tenantId);

    Map<String, Object> checkLogin(LoginVo parameter, String header);

    /**
     * 根据userId 与projectId 获取当前用户是否有访问url 权限
     * 用户签权和项目签权， 当传递参数projectId不为空时，进行项目签权
     * add by xiawg 2020-05-28
     */
    CheckPrivilegeResultVo getCurrentUserIsPrivilege(String ak, Long projectId, Long userId, String url, String network);

    Optional<List<UserVo>> getUsersByRole(Long roleId);
}