/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ksyun.auth.dao.PrivilegeMapper;
import com.ksyun.auth.dao.PrivilegePermissionGroupMapper;
import com.ksyun.auth.dto.PrivilegeExcelDto;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.AppAksk;
import com.ksyun.common.entity.Privilege;
import com.ksyun.common.entity.PrivilegeProps;
import com.ksyun.common.entity.Category;
import com.ksyun.common.entity.PermissionGroup;
import com.ksyun.common.entity.PrivilegePermissionGroup;
import com.ksyun.auth.dao.AppAkskMapper;
import com.ksyun.auth.dao.CategoryMapper;
import com.ksyun.auth.dao.PermissionGroupMapper;
import com.ksyun.auth.dao.PrivilegePropsMapper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020
 */
@Component(Constants.PRIVILEGE_IGNORE_STRATEGY)
@Slf4j
public class PrivilegeIgnoreStrategyPrivilege extends AbstractPrivilegeImportStrategy {

    @Autowired
    AppAkskMapper appAkskMapper;
    @Autowired
    PrivilegePropsMapper privilegePropsMapper;
    @Autowired
    PermissionGroupMapper permissionGroupMapper;
    @Autowired
    PrivilegeMapper privilegeMapper;
    @Autowired
    CategoryMapper categoryMapper;
    @Autowired
    PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    ApplicationEventPublisher publisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImport(List list) {
        log.info("IgnoreStrategy...");
        if (!CollectionUtil.isNotEmpty(list)) {
            return;
        }
        Map<String, String> akMap = getAkMap();
        Map<String, Tuple> cache = Maps.newConcurrentMap();
        Set<String> importCodeSet = (Set<String>) list.stream().map(p -> ((PrivilegeExcelDto) p).getCode()).collect(Collectors.toSet());
        List<Privilege> privileges = privilegeMapper.selectList(null);
        Set<String> existedCodeSet = privileges.stream().map(p -> p.getCode()).collect(Collectors.toSet());
        Sets.SetView<String> difference = Sets.difference(importCodeSet, existedCodeSet);
        List<PrivilegeExcelDto> collect = (List<PrivilegeExcelDto>) list.stream().filter(p -> difference.contains(((PrivilegeExcelDto) p).getCode())).collect(Collectors.toList());
        if (!CollectionUtil.isNotEmpty(collect)) {
            return;
        }
        Map<String, Category> categoryMap = getCategoryMap();
        Map<String, PermissionGroup> permissionGroupMap = getPermissionGroup();
        Map<String, Privilege> privilegeMap = getPrivilegeMap(privileges);
        for (PrivilegeExcelDto p : collect) {
            String categoryName = p.getCategoryName();
            String groupName = p.getGroupName();
            Category category = categoryMap.get(categoryName);
            if (category == null) {
                category = insertCategory(categoryName);
                categoryMap.put(categoryName, category);
            }
            PermissionGroup permissionGroup = permissionGroupMap.get(groupName + "^" + category.getId());
            if (permissionGroup == null) {
                permissionGroup = insertPermissionGroup(category, groupName, p.getGroupDesc());
                permissionGroupMap.put(groupName + "^" + category.getId(), permissionGroup);
            }
            Privilege privilege = privilegeMap.get(p.getCode());
            if (privilege == null) {
                privilege = insertPrivilege(p,akMap);
                privilegeMap.put(privilege.getCode(), privilege);
                cache.put(privilege.getCode(), new Tuple(p.getParentCode(), privilege));
                insertPrivilegeProps(p, privilege);
            }
            insertPrivilegePermissionGroup(permissionGroup, privilege);
        }
        updateParentId(cache, privilegeMap);
        updatePrivilegeOrders(cache.keySet());
    }

    private Map<String, Privilege> getPrivilegeMap(List<Privilege> privileges) {
        Map<String, Privilege> map = Maps.newConcurrentMap();
        privileges.forEach(p -> {
            map.putIfAbsent(p.getCode(), p);
        });
        return map;
    }

    private void insertPrivilegeProps(PrivilegeExcelDto p, Privilege privilege) {
        for (PrivilegeProps prop : p.getPrivilegeProps()) {
            prop.setPrivilegeId(privilege.getId());
            privilegePropsMapper.insert(prop);
        }
    }

    private void updatePrivilegeOrders(Set<String> codes) {
        //权限点导入完毕后，将所有权限点的id值赋给Order。
        int updateOderCount = privilegeMapper.updateAllMenusOrder(codes);
        log.info("upload privilege, update menus order, count={}", updateOderCount);
    }

    private Map<String, PermissionGroup> getPermissionGroup() {
        Map<String, PermissionGroup> map = Maps.newConcurrentMap();
        LambdaQueryWrapper<PermissionGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PermissionGroup::getStatus, "NORMAL");
        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectList(queryWrapper);
        permissionGroups.forEach(p -> {
            map.putIfAbsent(p.getName() + "^" + p.getCategoryId(), p);
        });
        return map;
    }

    private Map<String, Category> getCategoryMap() {
        List<Category> categories = categoryMapper.selectList(null);
        Map<String, Category> categoryMap = Maps.newHashMap();
        categories.forEach(c -> {
            categoryMap.putIfAbsent(c.getName(), c);
        });
        return categoryMap;
    }

    private Map<String, String> getAkMap() {
        List<AppAksk> appAksks = appAkskMapper.selectList(null);
        Map<String, String> akMap = Maps.newHashMap();
        for (AppAksk appAksk : appAksks) {
            akMap.put(appAksk.getAk(), appAksk.getAk());
        }
        return akMap;
    }

    private void updateParentId(Map<String, Tuple> cache, Map<String, Privilege> privilegeMap) {
        for (Map.Entry<String, Tuple> kv : cache.entrySet()) {
            Privilege privilege = kv.getValue().privilege;
            String parentCode = kv.getValue().key;
            if (StringUtil.isEmpty(parentCode)) {
                privilege.setParentId(0L);
            } else {
                Privilege parent = privilegeMap.get(parentCode);
                if (null != parent) {
                    privilege.setParentId(parent.getId());
                }
            }
            privilegeMapper.updateById(privilege);
        }
    }

    private Privilege insertPrivilege(PrivilegeExcelDto p, Map<String, String> akskMap) {
        Privilege privilege = new Privilege();
        privilege.setCode(p.getCode());
        privilege.setAk(akskMap.get(p.getAk()));
        privilege.setName(p.getName());
        privilege.setType(p.getType());
        privilege.setIcon(p.getIcon());
        privilege.setParentId(null);
        privilege.setUrl(p.getUrl());
        privilege.setCreateTime(LocalDateTime.now());
        privilegeMapper.insert(privilege);
        return privilege;
    }

    private PermissionGroup insertPermissionGroup(Category category, String groupName, String description) {
        PermissionGroup permissionGroup = new PermissionGroup();
        permissionGroup.setName(groupName);
        permissionGroup.setDescription(description);
        permissionGroup.setCategoryId(category.getId());
        permissionGroup.setCreateTime(LocalDateTime.now());
        permissionGroup.setUpdateTime(LocalDateTime.now());
        permissionGroup.setCreateBy("系统");
        permissionGroup.setSource(0);
        permissionGroupMapper.insert(permissionGroup);
        return permissionGroup;
    }

    private Category insertCategory(String categoryName) {
        Category category = new Category();
        category.setName(categoryName);
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        categoryMapper.insert(category);
        return category;
    }

    private PrivilegePermissionGroup insertPrivilegePermissionGroup(PermissionGroup permissionGroup, Privilege privilege) {
        PrivilegePermissionGroup privilegePermissionGroup = new PrivilegePermissionGroup();
        privilegePermissionGroup.setPermissionGroupId(permissionGroup.getId());
        privilegePermissionGroup.setPrivilegeId(privilege.getId());
        privilegePermissionGroup.setCreateTime(LocalDateTime.now());
        privilegePermissionGroupMapper.insert(privilegePermissionGroup);
        return privilegePermissionGroup;
    }

    class Tuple {
        private String key;
        private Privilege privilege;

        public Tuple(String key, Privilege privilege) {
            this.key = key;
            this.privilege = privilege;
        }
    }

}
