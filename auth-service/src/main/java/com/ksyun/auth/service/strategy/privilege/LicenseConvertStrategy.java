/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component(Constants.LICENSE)
public class LicenseConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {

    public static final String PROP_KEY = "license";
    public static final String TRUE = "true";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (TRUE.equals(s)) {
            PrivilegeProps props = new PrivilegeProps();
            props.setPropKey(PROP_KEY);
            props.setPropValue(TRUE);
            list.add(props);
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        String hideProp = "";
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (kv.length == 2 && PROP_KEY.equals(kv[0]) && TRUE.equals(kv[1])) {
                        hideProp = TRUE;
                    }
                }
            }
        }
        return TupleVo.of("licenseProp", hideProp);
    }
}
