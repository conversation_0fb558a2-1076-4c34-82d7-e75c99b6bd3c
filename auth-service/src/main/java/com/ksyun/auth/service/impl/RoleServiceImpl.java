package com.ksyun.auth.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.dao.*;
import com.ksyun.auth.dto.GroupSimpleDto;
import com.ksyun.auth.dto.RoleExcelDto;
import com.ksyun.auth.dto.UserSimpleDto;
import com.ksyun.auth.service.GroupService;
import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.service.strategy.ImportStrategy;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.*;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.enums.SystemTypeEnum;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.exception.BusinessException;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.utils.DateUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色管理服务类
 */
@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";
    @Autowired
    private BasicTagsMapper basicTagsMapper;
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private GroupMapper groupMapper;
    @Resource
    private GroupRoleMapper groupRoleMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private GroupUserMapper groupUserMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private RolePermissionGroupMapper rolePermissionGroupMapper;
    @Resource
    private RolePrivilegeMapper rolePrivilegeMapper;
    @Resource
    private PermissionGroupMapper permissionGroupMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private Map<String, ImportStrategy> strategies;

    private Map<Long, String> tagMap;
    @Resource
    UserPropsMapper userPropsMapper;

    /**
     * 通过用户Id获取角色互斥状态，状态为ENABLE时无法进行多角色赋值
     * kcde 中都是平台级的，且用户可以属于多个角色
     *
     * @param userId
     * @return
     */
    private boolean getRoleRoleMutexStatus(Long userId) {
        return false;
//        String roleMutexStatus = userPropsMapper.getRoleRoleMutexStatusByUserId(userId);
//        log.info("roleMutexStatus:{}", roleMutexStatus);
//        if ("ENABLE".equals(roleMutexStatus)) {
//            return true;
//        }
//        return false;
    }

    /**
     * 当前用户选择多个角色时，告知只能选择个角色
     */
    private void getUserOnlyOneRole(Long userId, Set<Long> parameterRoleIds) {

        log.info("getUserOnlyOneRole:userId={}", userId);

        //查询当前用户的租户id
        User user = userMapper.lookupUserByUserId(userId);

        if (getRoleRoleMutexStatus(user.getId())) {
            if (parameterRoleIds.size() > 1) {
                Assert.moreThanOneRole(true, "只能选择一个角色");
            }
            //当前用户有组时，提示一个用户只能属于一个角色或一个群组
            List<GroupVo> groupList = groupUserMapper.getGroupListByUserId(userId);
            if (!groupList.isEmpty()) {
                Assert.moreThanOneRole(true, "一个用户只能属于一个角色或者一个群组");
            }
        }
    }

    /**
     * 当前组选择多个角色时，告知只能选择一个角色
     */
    private Set<String> getGroupOnlyOneRole(boolean roleRoleMutexStatus, Set<Long> roleIds, Set<Long> groupIds) {

        log.info("getGroupOnlyOneRole:{}", roleRoleMutexStatus);

        //组名称
        Set<String> groupNames = new HashSet<>();
        if (roleRoleMutexStatus) {
            if (groupIds.size() > 1) {
                Assert.moreThanOneRole(true, "只能选择一个群组");
            }
            if (roleIds.size() > 1) {
                Assert.moreThanOneRole(true, "只能选择一个角色");
            }
            for (Long groupId : groupIds) {
                List<RoleVo> roleList = groupRoleMapper.getRoleListByGroupId(groupId);
                if (!roleList.isEmpty()) {
                    groupNames.add(groupMapper.selectById(groupId).getName());
                }
            }
        }
        return groupNames;
    }

    /**
     * 当选择多个用户时给出提示一个用户只能选择一个角色。U1,U2,U3已经包含在其他角色中，不允许进行添加。
     *
     * @param userIds
     * @param roleIds
     * @return
     */
    private Set<String> getUserOnlyOneRoleByUserId(Set<Long> userIds, Set<Long> roleIds) {
        //用户名称
        Set<String> userNames = new HashSet<>();
        User user;

        for (Long userId : userIds) {
            user = userMapper.lookupUserByUserId(userId);
            if (user != null) {
                if (getRoleRoleMutexStatus(user.getId())) {
                    List<RoleVo> roleList = getRolesByUserId(userId);
                    if (!roleList.isEmpty()) {
                        //去除当前角色中已经存在的用户
                        for (RoleVo role : roleList) {
                            if (!roleIds.iterator().next().equals(role.getId())) {
                                userNames.add(user.getName());
                            }
                        }
                    }
                }
            }
        }
        return userNames;
    }

    private Set<String> getUserOnlyOneRoleByUserId(Long userId, Long roleId) {
        //用户名称
        Set<String> userNames = new HashSet<>();
        User user = userMapper.lookupUserByUserId(userId);
        if (user != null) {
            if (getRoleRoleMutexStatus(user.getId())) {
                List<RoleVo> roleList = getRolesByUserId(userId);
                if (!roleList.isEmpty()) {
                    //去除当前角色中已经存在的用户
                    for (RoleVo role : roleList) {
                        if (!roleId.equals(role.getId())) {
                            userNames.add(user.getName());
                        }
                    }
                }
            }
        }
        return userNames;
    }


    @Override
    public RoleVo getRoleByCode(String code, String source) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        queryWrapper.eq("source", source);
        Role role = roleMapper.selectOne(queryWrapper);
        if (null == role) {
            return null;
        }
        RoleVo roleVo = new RoleVo();
        BeanUtils.copyProperties(role, roleVo);
        roleVo.setCreateTime(DateUtil.toDefaultString(role.getCreateTime()));
        return roleVo;
    }

    @Override
    public Role get(Long id) {
        Role role = roleMapper.selectById(id);
//        RoleVo roleVo = new RoleVo();
//        BeanUtils.copyProperties(role, roleVo);
//        roleVo.setCreateTime(DateUtil.toDefaultString(role.getCreateTime()));
        return role;
    }

    @Override
    public List<RoleTagsVo> getAllTags() {
        List<BasicTags> basicTags = basicTagsMapper.selectList(new LambdaQueryWrapper<>());
        return basicTags.stream().map(b -> {
            RoleTagsVo roleTag = new RoleTagsVo();
            BeanUtils.copyProperties(b, roleTag);
            return roleTag;
        }).collect(Collectors.toList());
    }


    @Override
    public List<Role> getRoles(RoleQueryVo parameter, Authentication authentication) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<Role>();
        queryWrapper.eq(!StringUtil.isEmpty(parameter.getCode()), "code", parameter.getCode());
        queryWrapper.in(CollectionUtil.isNotEmpty(parameter.getRoleIds()), "id", parameter.getRoleIds());
        queryWrapper.orderByDesc("id");
        List<Role> roleList = roleMapper.selectList(queryWrapper);
        return roleList;
    }

    public void buildRoleTags(Collection<RoleVo> roles) {
        for (RoleVo role : roles) {
            role.setTags(getTagKeys(role.getTagIds()));
        }
    }

    public List<String> getTagKeys(String tagIds) {
        long[] ids = RoleVo.deserializeTagIds(tagIds);
        return Arrays.stream(ids).mapToObj(tagMap::get).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<RoleVo> getRolesByUserId(Long userId) {
        Set<Long> userRoleIds = Sets.newHashSet();
        User user = userMapper.selectById(userId);
        Assert.notNull(user, BusinessExceptionEnum.USER_NOT_FOUND);
//        注释前的逻辑应该是当前用户是租户的话，返回所有的角色
//        if (Objects.equals(user.getTenantId(), user.getId())) {
//            buildRoleVoByRoleList(new QueryWrapper<>());
//        } else {
        Set<Long> groupIds = getUserRoleFromUserGroup(userId);
        Set<Long> groupRoleIds = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(groupIds)) {
            QueryWrapper<GroupRole> groupRoleQueryWrapper = new QueryWrapper<>();
            groupRoleQueryWrapper.in("group_id", groupIds);
            groupRoleIds = groupRoleMapper.selectList(groupRoleQueryWrapper).stream().map(GroupRole::getRoleId).collect(Collectors.toSet());
        }
        // 查询用户本身的角色
        QueryWrapper<UserRole> userRoleQueryWrapper = new QueryWrapper<>();
        userRoleQueryWrapper.eq("user_id", userId);
        userRoleIds = userRoleMapper.selectList(userRoleQueryWrapper).stream().map(UserRole::getRoleId).collect(Collectors.toSet());
        // 整合用户的角色
        userRoleIds.addAll(groupRoleIds);
        // 用户没有任何角色的时候，返回空的集合
        if (userRoleIds.isEmpty()) {
            return Collections.emptyList();
        }
//        }
        return getRoleDetailByRoleIds(userRoleIds);
    }

    /**
     * 查询用户所在分组的角色
     *
     * @param userId 用户id
     * @return
     */
    private Set<Long> getUserRoleFromUserGroup(Long userId) {
        QueryWrapper<GroupUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return groupUserMapper.selectList(queryWrapper).stream().map(GroupUser::getGroupId).collect(Collectors.toSet());
    }

    private List<RoleVo> getRoleDetailByRoleIds(Set<Long> userRoleIds) {
        // 根据角色Id查询角色详细信息
        RoleQueryVo parameter = new RoleQueryVo();
        parameter.setRoleIds(userRoleIds);
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(!StringUtil.isEmpty(parameter.getCode()), "code", parameter.getCode());
        queryWrapper.in(CollectionUtil.isNotEmpty(parameter.getRoleIds()), "id", parameter.getRoleIds());
        queryWrapper.orderByAsc("id");
        return buildRoleVoByRoleList(queryWrapper);
    }

    /**
     * 根据查询条件 将List<Role> 转变为 List<RoleVo>
     *
     * @param queryWrapper 查询条件
     * @return
     */
    private List<RoleVo> buildRoleVoByRoleList(QueryWrapper<Role> queryWrapper) {
        return roleMapper.selectList(queryWrapper).stream().map(r -> {
            RoleVo roleVo = new RoleVo();
            BeanUtils.copyProperties(r, roleVo);
            roleVo.setCreateTime(DateUtil.toDefaultString(r.getCreateTime()));
            return roleVo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRoleByBigData(RoleUpdateVo vo, AuthUser user) {
        log.info("updateRoleByBigData parameter={}", JSON.toJSONString(vo));
        Role roleVo = roleMapper.get(vo.getRoleId());
//        Assert.notNull(roleVo, "未找到角色");
        RoleVo existedRole = getRoleByCode(vo.getCode(), SystemTypeEnum.BIGDATA.getCode());
        log.debug("checkUpdateRoleExist Parameter code={},Result={}", vo.getCode(), JSON.toJSONString(existedRole));
        if (existedRole != null) {
            Role tenantRole = get(existedRole.getId());
            Assert.isTrue(tenantRole == null || tenantRole.getId().equals(existedRole.getId()), "角色已存在");
        }
        Assert.isTrue(existedRole == null || existedRole.getId().equals(roleVo.getId()), "角色已存在");

        if (null != roleVo.getId()) {
            Role role = new Role();
            BeanUtils.copyProperties(vo, role);
            role.setTagIds(RoleVo.serializeTagIds(vo.getTagIds()));
            role.setUpdateTime(LocalDateTime.now());
            UpdateWrapper<Role> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", roleVo.getId());
            roleMapper.update(role, updateWrapper);
        }
    }

    @Override
    @Transactional
    public void addBindOrDeleteBindUserAndRoles(Set<Long> userIds, Set<Long> parameterRoleIds) {
        checkRoleByRoleIds(parameterRoleIds);
        Assert.isTrue(userIds.size() == 1, "用户id参数集合只能是一个");

        getUserOnlyOneRole(userIds.iterator().next(), parameterRoleIds);
        List<Long> groupRoleIds = groupService.getGroupRoleIdsByUserId(userIds.iterator().next());

        Set<Long> roleIds = getRolesByUserId(userIds.iterator().next()).stream().map(RoleVo::getId).collect(Collectors.toSet());
        log.info("getRolesByUserId roleIds={}", roleIds);

        //需要解绑的用户角色
        roleIds.removeAll(parameterRoleIds);
        log.info("updateUserRoles roleIds={},parameterRoleIds={}", roleIds, parameterRoleIds);

        if (CollectionUtil.isNotEmpty(userIds)) {
            // 解绑用户和角色
            LambdaQueryWrapper<UserRole> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(UserRole::getUserId, userIds);
            userRoleMapper.delete(queryWrapper);

            // 用户与角色进行绑定，只绑定用户自身的角色，不绑定群组的角色。
            if (groupRoleIds != null) {
                parameterRoleIds.removeAll(groupRoleIds);
            }

            // 绑定用户和角色
            if (CollectionUtil.isNotEmpty(parameterRoleIds)) {
                userIds.forEach(id ->
                        parameterRoleIds.forEach(roleId -> {
                            UserRole userRole = new UserRole();
                            userRole.setUserId(id);
                            userRole.setRoleId(roleId);
                            userRole.setCreateTime(LocalDateTime.now());
                            userRoleMapper.insert(userRole);
                        })
                );
            }
        }

        if (groupRoleIds != null) {
            roleIds.removeAll(groupRoleIds);
        }
    }

    @Override
    @Transactional
    public void addBindUserAndRoles(Set<Long> userIds, Set<Long> roleIds) {
        Set<String> userNames = getUserOnlyOneRoleByUserId(userIds, roleIds);
        if (!userNames.isEmpty()) {
            Assert.moreThanOneRole(true, "一个用户只能选择一个角色。" + userNames + "已经包含在其他角色中，不允许进行添加。");
        }

        if (CollectionUtil.isNotEmpty(roleIds) && CollectionUtil.isNotEmpty(userIds)) {
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(UserRole::getRoleId, roleIds);
            queryWrapper.in(UserRole::getUserId, userIds);
            int delete = userRoleMapper.delete(queryWrapper);
            log.info("roles [{}] unbind {} users [{}].", roleIds, delete, userIds);
            // 绑定用户和角色
            for (Long roleId : roleIds) {
                int insert = userRoleMapper.batchInsert(roleId, userIds);
                log.info("roles [{}] bind {} users [{}].", roleId, insert, userIds);
            }
        }
    }

    @Override
    @Transactional
    public void addBindUserAndRoles(Long userId, Long roleId) {
        Role role = roleMapper.get(roleId);
        Assert.isTrue(role != null,"无效的角色id");

        Set<String> userNames = getUserOnlyOneRoleByUserId(userId, roleId);
        if (!userNames.isEmpty()) {
            Assert.moreThanOneRole(true, "一个用户只能选择一个角色。" + userNames + "已经包含在其他角色中，不允许进行添加。");
        }

        if (userId != null && roleId != null) {
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRole::getRoleId, roleId);
            queryWrapper.eq(UserRole::getUserId, userId);
            int delete = userRoleMapper.delete(queryWrapper);
            log.info("roles [{}] unbind {} users [{}].", roleId, delete, userId);
            // 绑定用户和角色
            Set<Long> userIds = new HashSet<>();
            userIds.add(userId);
            int insert = userRoleMapper.batchInsert(roleId, userIds);
            log.info("roles [{}] bind {} users [{}].", roleId, insert, userId);
        }
    }

    @Override
    public void addBindUserAndRoles(Long userId, String roleName) {
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getName,roleName);
        Role role = roleMapper.selectOne(wrapper);
        Assert.isTrue(role != null,"无效的角色id");

        Set<String> userNames = getUserOnlyOneRoleByUserId(userId, role.getId());
        if (!userNames.isEmpty()) {
            Assert.moreThanOneRole(true, "一个用户只能选择一个角色。" + userNames + "已经包含在其他角色中，不允许进行添加。");
        }
        Long roleId = role.getId();
        if (userId != null && roleId != null) {
            LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRole::getRoleId, roleId);
            queryWrapper.eq(UserRole::getUserId, userId);
            int delete = userRoleMapper.delete(queryWrapper);
            log.info("roles [{}] unbind {} users [{}].", roleId, delete, userId);
            // 绑定用户和角色
            Set<Long> userIds = new HashSet<>();
            userIds.add(userId);
            int insert = userRoleMapper.batchInsert(roleId, userIds);
            log.info("roles [{}] bind {} users [{}].", roleId, insert, userId);
        }
    }

    @Override
    @Transactional
    public void deleteBindUserAndRoles(Set<Long> parameterUserIds, Set<Long> parameterRoleIds) {
        checkUserByUserIds(parameterUserIds);
        checkRoleByRoleIds(parameterRoleIds);

        if (CollectionUtil.isNotEmpty(parameterRoleIds) && CollectionUtil.isNotEmpty(parameterUserIds)) {
            Set<Long> roleIdsVo = new HashSet<>(parameterRoleIds);
            parameterUserIds.forEach(userId -> {
                //用户所在群组的角色,不解除项目绑定关系。
                List<Long> groupRoleIds = groupService.getGroupRoleIdsByUserId(userId);
                if (groupRoleIds != null) {
                    parameterRoleIds.removeAll(groupRoleIds);
                }
            });
            // 解绑用户和角色，使用前端传入的原始角色数据，无需去除用户所属群组的角色。
            if (CollectionUtil.isNotEmpty(parameterUserIds) && CollectionUtil.isNotEmpty(roleIdsVo)) {
                LambdaQueryWrapper<UserRole> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.in(UserRole::getUserId, parameterUserIds);
                queryWrapper.in(UserRole::getRoleId, roleIdsVo);
                userRoleMapper.delete(queryWrapper);
            }
        }
    }


    @Override
    @Transactional
    public void updateBindAndUnbindGroupAndRoles(Set<Long> groupIds, Set<Long> roleIds, AuthUser authUser) {
        //当前组选择多个角色时，告知只能选择一个角色
        getGroupOnlyOneRole(getRoleRoleMutexStatus(authUser.getId()), roleIds, groupIds);

        if (CollectionUtil.isNotEmpty(groupIds)) {
            // 解绑分组和角色
            LambdaQueryWrapper<GroupRole> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(GroupRole::getGroupId, groupIds);
            groupRoleMapper.delete(queryWrapper);

            if (CollectionUtil.isNotEmpty(roleIds)) {
                groupIds.forEach(id ->
                        roleIds.forEach(roleId -> {
                            LambdaQueryWrapper<GroupRole> lambdaQueryWrapper = Wrappers.lambdaQuery();
                            lambdaQueryWrapper.eq(GroupRole::getGroupId, id);
                            lambdaQueryWrapper.eq(GroupRole::getRoleId, roleId);
                            GroupRole groupRole = groupRoleMapper.selectOne(lambdaQueryWrapper);
                            if (groupRole == null) {
                                groupRole = new GroupRole();
                                groupRole.setGroupId(id);
                                groupRole.setRoleId(roleId);
                                groupRole.setCreateTime(LocalDateTime.now());
                                groupRoleMapper.insert(groupRole);
                            }
                        })
                );
            }
        }
    }

    @Override
    @Transactional
    public void addBindGroupAndRoles(Set<Long> groupIds, Set<Long> roleIds, AuthUser authUser) {

        Set<String> groupNames = getGroupOnlyOneRole(getRoleRoleMutexStatus(authUser.getId()), roleIds, groupIds);
        if (!groupNames.isEmpty()) {
            Assert.moreThanOneRole(true, "一个角色只能选择一个群组。" + groupNames + "已经包含在其他角色中，不允许进行添加。");
        }

        if (CollectionUtil.isNotEmpty(roleIds) && CollectionUtil.isNotEmpty(groupIds)) {
            if (roleIds.size() == 1) {
                LambdaQueryWrapper<GroupRole> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.in(GroupRole::getRoleId, roleIds);
                int delete = groupRoleMapper.delete(lambdaQueryWrapper);
                log.info("roles [{}] unbind {} groups [{}]", roleIds, delete, groupIds);
            }

            for (Long roleId : roleIds) {
                int insert = groupRoleMapper.batchInsert(roleId, groupIds);
                log.info("roles [{}] bind {} groups [{}]", roleId, insert, groupIds);
            }
        }
    }

    @Override
    @Transactional
    public void addBindGroupsAndRole(Set<Long> groupIds, Long roleId) {
        if (roleId != null && CollectionUtil.isNotEmpty(groupIds)) {
            int delete = groupRoleMapper.delete(new QueryWrapper<GroupRole>().eq("role_id", roleId));
            log.info("roles [{}] unbind {} groups [{}]", roleId, delete, groupIds);
            int insert = groupRoleMapper.batchInsert(roleId, groupIds);
            log.info("roles [{}] bind {} groups [{}]", roleId, insert, groupIds);
        }
    }


    @Override
    @Transactional
    public void deleteBindGroupAndRoles(Set<Long> groupIds, Set<Long> roleIds) {
        if (CollectionUtil.isNotEmpty(groupIds) && CollectionUtil.isNotEmpty(roleIds)) {
            // 解绑分组和角色
            LambdaQueryWrapper<GroupRole> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(GroupRole::getGroupId, groupIds);
            queryWrapper.in(GroupRole::getRoleId, roleIds);
            groupRoleMapper.delete(queryWrapper);
        }
    }


    @Override
    @Transactional
    public void deleteRoleById(Long roleId) {
        roleMapper.deleteById(roleId);
        groupRoleMapper.delete(new QueryWrapper<GroupRole>().eq("role_id", roleId));
        userRoleMapper.delete(new QueryWrapper<UserRole>().eq("role_id", roleId));
        rolePermissionGroupMapper.delete(new QueryWrapper<RolePermissionGroup>().eq("role_id", roleId));
        rolePrivilegeMapper.delete(new QueryWrapper<RolePrivilege>().eq("role_id", roleId));
    }

    @Override
    public Long getRolesCountByIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0L;
        }
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        return roleMapper.selectCount(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importRole(List<RoleExcelDto> list, String strategy) {
        strategies.get(strategy).doImport(list);
    }


    @Override
    @Transactional
    public void addRole(RoleVo roleVo, AuthUser user, String envVersion) {
        // 目前租户控制台 添加角色检查逻辑为 当前租户下code name 是否唯一  存在当前角色体系下名为role_code  自建角色也为role_code  可以根据source 与 create_by进行区分
        roleVo.setSource(SystemTypeEnum.BIGDATA.getCode());
        checkExistRole(roleVo);

        log.info("addBigDataRole parameter={}", JSON.toJSONString(roleVo));
        Assert.notNull(roleVo, "角色不能为空");
        Role role = new Role();
        BeanUtils.copyProperties(roleVo, role);
        role.setDefaultRole("0");
        role.setUpdateTime(LocalDateTime.now());
        role.setCreateTime(LocalDateTime.now());
        roleMapper.insert(role);
        roleVo.setId(role.getId());

    }

    @Override
    public Page<Role> page(RoleQueryVo parameter, Authentication authentication) {
        Page<Role> page = new Page<>(parameter.getPageNo(), parameter.getPageSize());
//        if (authentication instanceof AuthUser) {
//            parameter.setTenantId(((AuthUser) authentication).getTenant().getId());
//        }
//        Assert.notNull(parameter.getTenantId(), "租户id不能为空");
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(parameter.getCode()), Role::getCode, parameter.getCode());
        queryWrapper.like(StringUtils.isNotEmpty(parameter.getName()), Role::getName, parameter.getName());
        queryWrapper.eq(StringUtils.isNotEmpty(parameter.getPlatform()), Role::getPlatform, parameter.getPlatform());

        Page<Role> rolePage = roleMapper.selectPage(page, queryWrapper);
        log.info("query role page Result RoleSize={}", rolePage.getTotal());
        rolePage.getRecords().forEach(e -> {
            LambdaQueryWrapper<UserRole> useRoleWrapper = new LambdaQueryWrapper<>();
            useRoleWrapper.eq(UserRole::getRoleId,e.getId());
            Long count = userRoleMapper.selectCount(useRoleWrapper);
            e.setUserCount(count);
        });

        return rolePage;
    }

    @Override
    public Role getRoleByCreateBy(Long roleId) {
        Role role = roleMapper.selectOne(new QueryWrapper<Role>().eq("id", roleId).eq("source", SystemTypeEnum.BIGDATA.getCode()));
//        RoleVo roleVo = new RoleVo();
//        BeanUtils.copyProperties(role, roleVo);
//        roleVo.setCreateTime(DateUtil.toDefaultString(role.getCreateTime()));
        log.info("getRoleByCreateBy parameter roleId={},Result={}", roleId, JSON.toJSONString(role));
        return role;
    }

    private void checkExistRole(RoleVo roleVo) {
        checkExistTenantRoleByCode(roleVo);
        checkExistTenantRoleByName(roleVo);
    }

    private void checkExistTenantRoleByName(RoleVo roleVo) {
        String name = roleVo.getName();
        Role existRoleByName = roleMapper.selectOne(new QueryWrapper<Role>().eq("name", name)
                .eq("source", SystemTypeEnum.BIGDATA.getCode()));
        Assert.isNull(existRoleByName, BusinessExceptionEnum.ROLE_EXISTED);
    }

    private void checkExistTenantRoleByCode(RoleVo roleVo) {
        String code = roleVo.getCode();
        Role existRoleByCode = roleMapper.selectOne(new QueryWrapper<Role>().eq("code", code)
                .eq("source", SystemTypeEnum.BIGDATA.getCode()));
        Assert.isNull(existRoleByCode, BusinessExceptionEnum.ROLE_EXISTED);
    }

    private void checkRoleByRoleIds(Set<Long> parameterRoleIds) {
        if (CollectionUtil.isNotEmpty(parameterRoleIds)) {
            Long roleIds = roleMapper.selectCount(new QueryWrapper<Role>().in("id", parameterRoleIds));
            Assert.isTrue(roleIds == parameterRoleIds.size(), BusinessExceptionEnum.ROLE_NOT_FOUND);
        }
    }

    private void checkUserByUserIds(Set<Long> parameterUserIds) {
        if (CollectionUtil.isNotEmpty(parameterUserIds)) {
            Long userIds = userMapper.selectCount(new QueryWrapper<User>().in("id", parameterUserIds));
            Assert.isTrue(userIds == parameterUserIds.size(), BusinessExceptionEnum.USER_INVALID);
        }
    }

    @Override
    public Map<String, List<GroupSimpleDto>> addGroupBound(Long roleId, AuthUser authUser) {
        Map<String, List<GroupSimpleDto>> map = new HashMap<>();
        map.put("all", Lists.newArrayList());
        map.put("bound", Lists.newArrayList());
        List<Group> groups = groupMapper.selectList(new QueryWrapper<>());

        if (CollectionUtil.isNotEmpty(groups)) {
            List<GroupSimpleDto> groupSimples = groups.stream().map(u -> {
                GroupSimpleDto groupSimple = new GroupSimpleDto();
                groupSimple.setId(u.getId());
                groupSimple.setName(u.getName());
                return groupSimple;
            }).collect(Collectors.toList());
            map.get("all").addAll(groupSimples);
            List<GroupRole> groupRoles = groupRoleMapper.selectList(new QueryWrapper<GroupRole>().eq("role_id", roleId));
            if (CollectionUtil.isNotEmpty(groupRoles)) {
                // 当前用户下 所有群组的id
                Set<Long> groupIds = groupRoles.stream().map(GroupRole::getGroupId).collect(Collectors.toSet());
                List<Group> bound = groupMapper.selectList(new QueryWrapper<Group>().in("id", groupIds));
                if (CollectionUtil.isNotEmpty(bound)) {
                    List<GroupSimpleDto> boundGroupSimples = bound.stream().map(u -> {
                        GroupSimpleDto groupSimple = new GroupSimpleDto();
                        groupSimple.setId(u.getId());
                        groupSimple.setName(u.getName());
                        return groupSimple;
                    }).collect(Collectors.toList());
                    map.get("bound").addAll(boundGroupSimples);
                }
                log.info("groupBound boundGroupIds ={}", groupIds);
            }
        }
        log.info("groupBound parameter roleId={}, Result={}", roleId, JSON.toJSONString(map));
        return map;
    }

    @Override
    public Map<String, List<UserSimpleDto>> addUserBound(long roleId, AuthUser authUser) {
        Map<String, List<UserSimpleDto>> map = new HashMap<>();
        map.put("all", Lists.newArrayList());
        map.put("bound", Lists.newArrayList());
        List<User> users = userMapper.selectList(new QueryWrapper<User>()
                //.eq("tenant_id", authUser.getTenant().getId())
                .in("status", UserVo.Status.NORMAL, UserVo.Status.SLEEP, UserVo.Status.LOGOUT));
                //.apply("tenant_id != id"));
        if (CollectionUtil.isNotEmpty(users)) {
            List<UserSimpleDto> userSimples = users.stream().map(u -> {
                UserSimpleDto userSimple = new UserSimpleDto();
                userSimple.setId(u.getId());
                userSimple.setName(u.getName());
                return userSimple;
            }).collect(Collectors.toList());
            map.get("all").addAll(userSimples);
            List<UserRole> userRoles = userRoleMapper.selectList(new QueryWrapper<UserRole>().eq("role_id", roleId));
            if (CollectionUtil.isNotEmpty(userRoles)) {
                Set<Long> userIds = userRoles.stream().map(UserRole::getUserId).collect(Collectors.toSet());
                List<User> bound = userMapper.selectList(new QueryWrapper<User>().in("id", userIds));
                if (CollectionUtil.isNotEmpty(bound)) {
                    List<UserSimpleDto> boundUserSimples = bound.stream().map(u -> {
                        UserSimpleDto userSimple = new UserSimpleDto();
                        userSimple.setId(u.getId());
                        userSimple.setName(u.getName());
                        return userSimple;
                    }).collect(Collectors.toList());
                    map.get("bound").addAll(boundUserSimples);
                }
                log.info("userBound boundUserIds ={}", userIds);
            }
        }
        log.info("userBound parameter roleId={}, Result={}", roleId, JSON.toJSONString(map));
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBindRolePermissionGroup(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        int size = (int) file.getSize();
        if (size > 1024 * 1024 * 10) {
            // 文件超过大小处理
        }
        String extension = fileName.substring(fileName.lastIndexOf("."));
        Assert.isTrue(Objects.equals(extension, ".xlsx"), BusinessExceptionEnum.FILE_TYPE_INVALID);
        Set<Map<String, String>> excelMapList = readExcel(file);
        //查询角色
        List<Role> roles = roleMapper.selectList(Wrappers.lambdaQuery());
        Map<String, Long> roleMap = roles.stream().collect(Collectors.toMap(Role::getName, Role::getId));
        //查询权限组
        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectList(Wrappers.lambdaQuery());
        Map<String, Long> permissionGroupMap = permissionGroups.stream()
                .collect(Collectors.toMap(PermissionGroup::getName, PermissionGroup::getId));
        //已存在的角色和权限组关联关系
        List<RolePermissionGroup> existRolePermissionGroups = rolePermissionGroupMapper.selectList(Wrappers.lambdaQuery());
        //组装RolePermissionGroup对象
        Set<RolePermissionGroup> rolePermissionGroups = Sets.newHashSet();
        RolePermissionGroup rolePermissionGroup;
        LocalDateTime now = LocalDateTime.now();
        for (Map<String, String> excelMap : excelMapList) {
            Long permissionGroupId = permissionGroupMap.get(excelMap.get("permissionGroup"));
            Long roleId = roleMap.get(excelMap.get("role"));
            if (Objects.isNull(permissionGroupId) || Objects.isNull(roleId)) {
                log.warn("权限组名称[{}]或角色[{}]不存在，忽略该条数据", excelMap.get("permissionGroup"), excelMap.get("role"));
                continue;
            }
            //过滤已经存在的关联关系
            boolean exist = false;
            for (RolePermissionGroup existRolePermissionGroup : existRolePermissionGroups) {
                if (roleId.equals(existRolePermissionGroup.getRoleId()) &&
                        permissionGroupId.equals(existRolePermissionGroup.getPermissionGroupId())) {
                    exist = true;
                    break;
                }
            }
            if (exist) {
                continue;
            }

            rolePermissionGroup = new RolePermissionGroup();
            rolePermissionGroup.setRoleId(roleId);
            rolePermissionGroup.setPermissionGroupId(permissionGroupId);
            rolePermissionGroup.setCreateTime(now);
            rolePermissionGroups.add(rolePermissionGroup);
        }
        //添加角色和权限组关联关系
        for (RolePermissionGroup data : rolePermissionGroups) {
            rolePermissionGroupMapper.insert(data);
        }
    }

    private Set<Map<String, String>> readExcel(MultipartFile file) {
        Workbook workbook = null;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            workbook = getWorkbook(inputStream, XLSX);
            return parseExcel(workbook);
        } catch (Exception e) {
            log.warn("解析Excel失败，文件名：" + file.getOriginalFilename() + " 错误信息：" + e.getMessage());
            throw new BusinessException("解析Excel失败", e.getMessage());
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.warn("关闭数据流出错！错误信息：" + e.getMessage());
            }
        }
    }

    private Set<Map<String, String>> parseExcel(Workbook workbook) {
        Set<Map<String, String>> resultDataSet = Sets.newHashSet();
        // 解析sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 获取第一行数据
        int firstRowNum = 0;
        Row firstRow = sheet.getRow(firstRowNum);
        if (null == firstRow) {
            log.warn("解析Excel失败，在第一行没有读取到任何数据！");
        }
        //解析每一行的数据，构造数据对象，跳过表头
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            String permissionGroup = convertToString(row.getCell(0));
            String role = convertToString(row.getCell(1));
            if (org.apache.commons.lang3.StringUtils.isBlank(permissionGroup) ||
                    org.apache.commons.lang3.StringUtils.isBlank(role)) {
                log.warn("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                continue;
            }
            Map<String, String> map = Maps.newHashMap();
            map.put("permissionGroup", permissionGroup);
            map.put("role", role);
            resultDataSet.add(map);
        }
        return resultDataSet;
    }

    private Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase(XLS)) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase(XLSX)) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    private String convertToString(Cell cell) {
        if (cell == null) {
            return "";
        }
        return getValue(cell);
    }

    /**
     * 单元格取值处理
     *
     * @param cell
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/8/19 17:30
     **/
    private String getValue(Cell cell) {
        if (cell.getCellType() == HSSFCell.CELL_TYPE_BOOLEAN) {
            // 返回布尔类型的值
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            double doubleVal = cell.getNumericCellValue();
            Object inputValue = null;
            BigDecimal bdVal = new BigDecimal(doubleVal);
            if ((bdVal + ".0").equals(Double.toString(doubleVal))) {
                //判断是否含有小数位.0
                inputValue = bdVal;
            } else {
                inputValue = doubleVal;
            }
            return String.valueOf(inputValue);
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_STRING) {
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        } else {
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        }
    }
}
