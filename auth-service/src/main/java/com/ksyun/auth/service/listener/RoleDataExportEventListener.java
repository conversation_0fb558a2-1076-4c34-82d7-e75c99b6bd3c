/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.listener;

import com.google.common.collect.Lists;
import com.ksyun.auth.dto.RoleExcelDto;
import com.ksyun.auth.dto.RoleExcelRowDto;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.exception.BusinessException;
import com.ksyun.auth.service.event.RoleDataExportEvent;
import com.ksyun.common.annotation.Column;
import com.ksyun.auth.service.strategy.privilege.ConvertStrategy;
import jodd.io.FileUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component
public class RoleDataExportEventListener implements ApplicationListener<RoleDataExportEvent> {

    @Value("${upload.tmp.dir}")
    String tmpDir;

    @Autowired
    Map<String, ConvertStrategy> strategies;

    @SneakyThrows
    @Override
    public void onApplicationEvent(RoleDataExportEvent roleDataExportEvent) {
        writeExcel(roleDataExportEvent.getFileName(), convert(roleDataExportEvent.getList()));
    }

    private List<RoleExcelRowDto> convert(List<RoleExcelDto> list) throws InvocationTargetException, IllegalAccessException {
        List<RoleExcelRowDto> roleExcelRowList = Lists.newArrayList();
        for (RoleExcelDto roleExcelDTO : list) {
            RoleExcelRowDto roleExcelRow = new RoleExcelRowDto();
            BeanUtils.copyProperties(roleExcelRow, roleExcelDTO);
            roleExcelRowList.add(roleExcelRow);
        }
        return roleExcelRowList;
    }

    public Workbook getWorkbook(String file) throws IOException {
        return new XSSFWorkbook(new FileInputStream(file));
    }

    public void writeExcel(String fileName, List<RoleExcelRowDto> list) throws IOException {
        Workbook workbook = null;
        FileOutputStream outputStream = null;
        String exportFilePath = tmpDir + File.separator + fileName;
        String templateFilePath = tmpDir + File.separator + Constants.ROLE_TEMPLATE_FILE_NAME;
        FileUtil.copy(templateFilePath, exportFilePath);
        try {
            workbook = getWorkbook(exportFilePath);
            Sheet sheet = workbook.getSheet("Sheet1");
            int rowNum = 2;
            for (Iterator<RoleExcelRowDto> it = list.iterator(); it.hasNext(); ) {
                RoleExcelRowDto data = it.next();
                if (data == null) {
                    continue;
                }
                convertToRow(sheet, rowNum++, data);
            }
            outputStream = new FileOutputStream(exportFilePath);
            workbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.warn("写Excel失败，文件名：{}，错误信息：", exportFilePath, e);
            throw new BusinessException("写Excel失败", e.getMessage());
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
                if (null != outputStream) {
                    outputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭数据流出错！错误信息：", e);
            }
        }
    }

    private void convertToRow(Sheet sheet, int rowNum, RoleExcelRowDto data) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        //输出行数据
        Row row = sheet.createRow(rowNum);
        Field[] fields = data.getClass().getDeclaredFields();
        for (Field field : fields) {
            Column annotation = field.getDeclaredAnnotation(Column.class);
            int index = annotation.index();
            String property = org.apache.commons.beanutils.BeanUtils.getProperty(data, field.getName());
            Cell cell = row.createCell(index, Cell.CELL_TYPE_STRING);
            cell.setCellValue(null == property ? "" : property);
        }
    }


}
