package com.ksyun.auth.service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.service.listener.PrivilegeFileReceiveEventListener;
import com.ksyun.auth.vo.*;
import com.ksyun.common.entity.Category;
import com.ksyun.common.entity.PermissionGroup;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Set;

public interface PermissionGroupService {

    Page<PermissionGroup> getPermissionGroups(PermissionGroupQueryVo parameter);

    List<CategoryVo> getPermissionGroupsAll(PermissionGroupQueryVo vo);

    void addPermissionGroup(PermissionGroupAddVo vo);

    void deletePermissionGroup(PermissionGroupDeleteVo ids);

    void updatePermissionGroup(PermissionGroupAddVo vo);

    void openOrClose(PermissionGroupOpenCloseVo vo);

    PermissionGroupVo getPermissionGroupsById(Long id);

    List<Long> getPermissionGroupIdsByRoleId(Long roleid);

    void addCategory(CategoryAddVo vo);

    void updateCategory(CategoryAddVo vo);

    void deleteCategory(CategoryVo vo);

    List<Category> getCategoryList();

    void addBindRolePermissionGroup(GrantPermissionGroupVo vo);

    void deleteBindRolePermissionGroup(GrantPermissionGroupVo vo);

    void receive(MultipartFile file, PrivilegeFileReceiveEventListener.PrivilegeScope scope) throws IOException;

    List<CategoryVo> getCategoryPermissionGroups(Long roleId);

    String exportPermissionGroup(PrivilegeFileReceiveEventListener.PrivilegeScope scope);

    List getAllPrivileges();

}
