/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component(Constants.NET)
public class NetConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {

    private static final String PROP_VALUE = "true";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (!StringUtil.isEmpty(s)) {
            String[] splits = s.replace("，", ",").split(",", -1);
            for (String split : splits) {
                NetEnum netEnum = NetEnum.labelOf(split);
                if (null != netEnum) {
                    PrivilegeProps props = new PrivilegeProps();
                    props.setPropKey(netEnum.key);
                    props.setPropValue(PROP_VALUE);
                    list.add(props);
                }
            }
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        Set<String> netProp = Sets.newHashSet();
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (!StringUtil.isEmpty(kv[0])) {
                        if (NetEnum.OFFICE_NETWORK.getKey().equals(kv[0])
                                || NetEnum.PROD_CLOUD.getKey().equals(kv[0])
                                || NetEnum.SOM.getKey().equals(kv[0])
                                || NetEnum.PUBLIC.getKey().equals(kv[0])
                                || NetEnum.VPC_JUMP.getKey().equals(kv[0])
                                || NetEnum.DEV_TEST_CLOUD.getKey().equals(kv[0])) {
                            netProp.add(NetConvertStrategy.NetEnum.keyOf(kv[0]).getLabel());
                        }
                    }
                }
            }
        }
        return TupleVo.of("netProp", StringUtils.join(netProp, ","));
    }

    @Getter
    public enum NetEnum {
        OFFICE_NETWORK("办公网", "OFFICE_NETWORK"),
        PROD_CLOUD("生产云桌面", "PROD_CLOUD"),
        SOM("SOM", "SOM"),
        PUBLIC("公有侧", "PUBLIC"),
        VPC_JUMP("大数据云VPC堡垒机", "VPC_JUMP"),
        DEV_TEST_CLOUD("开发测试云环境", "DEV_TEST_CLOUD"),
        ;
        String label;
        String key;

        NetEnum(String label, String key) {
            this.label = label;
            this.key = key;
        }

        public static NetEnum labelOf(String label) {
            for (NetEnum positionEnum : NetEnum.values()) {
                if (positionEnum.label.equals(label)) {
                    return positionEnum;
                }
            }
            return null;
        }

        public static NetEnum keyOf(String key) {
            for (NetEnum positionEnum : NetEnum.values()) {
                if (positionEnum.key.equals(key)) {
                    return positionEnum;
                }
            }
            return null;
        }
    }
}
