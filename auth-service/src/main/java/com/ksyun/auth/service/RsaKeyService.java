package com.ksyun.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.common.entity.RsaKey;

public interface RsaKeyService extends IService<RsaKey> {
    /**
     * 生成RSA密钥对
     * @param name 密钥名称
     * @param keySize 密钥大小
     * @param userId 用户ID
     * @return 生成的密钥对信息
     */
    RsaKey generateKeyPair(String name, int keySize, String userId);
    
    /**
     * 上传公钥
     * @param name 密钥名称
     * @param publicKey 公钥内容
     * @param userId 用户ID
     * @return 保存的密钥信息
     */
    RsaKey uploadPublicKey(String name, String publicKey, String userId);


    /**
     * 验证公钥，不通过将返回false
     *
     * @param publicKey 公钥内容
     * @return
     */
    boolean validateSshPublicKey(String publicKey);
}
