/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.listener;

import com.ksyun.auth.dto.RoleExcelDto;
import com.ksyun.auth.dto.RoleExcelRowDto;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.exception.BusinessException;
import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.service.event.RoleFileReceiveEvent;
import com.ksyun.common.annotation.Column;
import jodd.io.FileUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component
public class RoleFileReceiveEventListener implements ApplicationListener<RoleFileReceiveEvent> {

    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";
    @Autowired
    RoleService roleService;

    @Override
    public void onApplicationEvent(RoleFileReceiveEvent fileReceiveEvent) {
        String filePath = fileReceiveEvent.getFilePath();
        List<RoleExcelDto> roleExcelData = readExcel(filePath);
        roleService.importRole(roleExcelData, fileReceiveEvent.getImportStrategyEnum().getType());
        //删除文件
        try {
            FileUtil.delete(filePath);
        } catch (IOException e) {
            log.warn("删除文件失败：{}", filePath);
        }
    }

    public Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase(XLS)) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase(XLSX)) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    public List<RoleExcelDto> readExcel(String fileName) {
        Workbook workbook = null;
        FileInputStream inputStream = null;
        try {
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            File excelFile = new File(fileName);
            if (!excelFile.exists()) {
                log.warn("指定的Excel文件不存在！");
                throw new BusinessException("文件不存在:" + fileName);
            }
            inputStream = new FileInputStream(excelFile);
            workbook = getWorkbook(inputStream, fileType);
            List<RoleExcelDto> resultDataList = parseExcel(workbook);
            return resultDataList;
        } catch (Exception e) {
            log.warn("解析Excel失败，文件名：" + fileName + " 错误信息：" + e.getMessage());
            throw new BusinessException("解析Excel失败，请检查文件或数据是否正确，错误信息："+ e.getMessage());
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.warn("关闭数据流出错！错误信息：" + e.getMessage());
                return null;
            }
        }
    }

    private List<RoleExcelDto> parseExcel(Workbook workbook) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        List<RoleExcelDto> resultDataList = new ArrayList<>();
        // 解析sheet
        Sheet sheet = workbook.getSheetAt(0);
            /*
                获取第一行数据
             */
        int firstRowNum = 1;
        Row firstRow = sheet.getRow(firstRowNum);
        if (null == firstRow) {
            log.warn("解析Excel失败，在第一行没有读取到任何数据！");
        }
            /*
                解析每一行的数据，构造数据对象，跳过表头
             */
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            RoleExcelRowDto roleExcelRow = bind(row);
            if (!checkObjFieldIsNotNull(roleExcelRow)) {
                log.warn("第 " + row.getRowNum() + "行数据为空，已忽略！");
                continue;
            }
            RoleExcelDto resultData = convert(roleExcelRow);
            if (null == resultData) {
                log.warn("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                continue;
            }
            resultDataList.add(resultData);
        }

        return resultDataList;
    }

    private boolean checkObjFieldIsNotNull(Object obj) throws IllegalAccessException {
        for (Field f : obj.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            if (f.get(obj) != null && !StringUtil.isEmpty(f.get(obj).toString())) {
                return true;
            }
        }
        return false;
    }

    private String convertToString(Cell cell) {
        if (cell == null) {
            return "";
        }
        return getValue(cell);
    }


    /**
     * 单元格取值处理
     * <AUTHOR>
     * @date 2020/8/19 17:30
     * @param cell
     * @return java.lang.String
     **/
    private  String getValue(Cell cell) {
        if (cell.getCellType() == HSSFCell.CELL_TYPE_BOOLEAN) {
            // 返回布尔类型的值
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            double doubleVal = cell.getNumericCellValue();
            Object inputValue = null;
            BigDecimal bdVal = new BigDecimal(doubleVal);
            if ((bdVal + ".0").equals(Double.toString(doubleVal))){
                //判断是否含有小数位.0
                inputValue = bdVal;
            }else{
                inputValue = doubleVal;
            }
            return String.valueOf(inputValue);
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_STRING){
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        } else {
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        }
    }

    private RoleExcelRowDto bind(Row row) throws InvocationTargetException, IllegalAccessException {
        RoleExcelRowDto privilegeExcelRow = new RoleExcelRowDto();
        Field[] fields = privilegeExcelRow.getClass().getDeclaredFields();
        for (Field field : fields) {
            Column annotation = field.getDeclaredAnnotation(Column.class);
            int index = annotation.index();
            String s = convertToString(row.getCell(index));
            s.replace(Constants.COMMA_CH, Constants.COMMA_EN);
            s.replace(" ", "");
            BeanUtils.setProperty(privilegeExcelRow, field.getName(), s);
        }
        System.out.println(privilegeExcelRow);
        return privilegeExcelRow;
    }

    public RoleExcelDto convert(RoleExcelRowDto row) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        RoleExcelDto roleExcelData = new RoleExcelDto();
        Field[] fields = row.getClass().getDeclaredFields();
        for (Field field : fields) {
            String s = BeanUtils.getProperty(row, field.getName());
            BeanUtils.setProperty(roleExcelData, field.getName(), s);
        }
        return roleExcelData;
    }

}
