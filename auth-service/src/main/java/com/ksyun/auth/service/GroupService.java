package com.ksyun.auth.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dto.GroupSimpleDto;
import com.ksyun.auth.dto.UserSimpleDto;
import com.ksyun.auth.vo.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GroupService {
    /**
     * 创建分组
     */
    void addGroup(GroupAddVo parameter);

    /**
     * 修改分组
     */
    void update(GroupUpdateVo parameter);

    /**
     * 删除分组
     */
    void delete(Long tenantId, Long groupId);

    /**
     * 绑定用户到分组
     */
    void addBind(UserGroupBindVo userGroupBindParameter);

    /**
     * 绑定用户到分组
     */
    void addUserBind(Long tenantId, Long groupId, Set<Long> userIds);

    void addGroupBind(Long tenantId, Long userId, Set<Long> roleIds);

    /**
     * 解绑用户和分组
     */
    void deleteBind(UserGroupBindVo parameter);

    /**
     * 查询分组，按分页
     */
    Page<GroupVo> getForPage(GroupQueryVo parameter);

    List<GroupSimpleDto> getAllGroupSimple(GroupQueryVo parameter);

    /**
     * 查询群组数量
     */
    long getGroupCount(long tenantId);

    /**
     * 根据用户Id获取当前用户所在群组角色Ids
     */
    List<Long> getGroupRoleIdsByUserId(Long userId);

    /**
     * 根据用户Id获取当前用户所在群组
     */
    List<GroupSimpleDto> getGroupsByUserIds(Set<Long> userIds);

    Map<String, List<UserSimpleDto>> getUserBound(long groupId, AuthUser authUser);
}
