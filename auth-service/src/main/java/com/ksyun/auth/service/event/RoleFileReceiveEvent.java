/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.event;

import com.ksyun.auth.service.strategy.RoleImportStrategyEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2020
 */
@Getter
@Setter
public class RoleFileReceiveEvent extends ApplicationEvent {
    private String filePath;
    private RoleImportStrategyEnum importStrategyEnum;

    public RoleFileReceiveEvent(String filePath, RoleImportStrategyEnum importStrategyEnum) {
        super(filePath);
        this.filePath = filePath;
        this.importStrategyEnum = importStrategyEnum;
    }
}
