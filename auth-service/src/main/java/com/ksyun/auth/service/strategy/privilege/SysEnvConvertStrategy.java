/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;


@Slf4j
@Component(Constants.SYS_ENV)
public class SysEnvConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {

    public static final String PROP_KEY = "sys_env";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (!StringUtil.isEmpty(s)) {
            String[] splits = s.replace("，", ",").split(",", -1);
            for (String split : splits) {
                SySEnvEnum hideEnum = SySEnvEnum.labelOf(split);
                if (null != hideEnum) {
                    PrivilegeProps props = new PrivilegeProps();
                    props.setPropKey(PROP_KEY);
                    props.setPropValue(hideEnum.val);
                    list.add(props);
                }
            }
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        Set<String> hideProp = Sets.newHashSet();
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (!StringUtil.isEmpty(kv[0])) {
                        if (PROP_KEY.equals(kv[0]) && kv.length > 1) {
                            hideProp.add(SysEnvConvertStrategy.SySEnvEnum.valOf(kv[1]).getLabel());
                        }
                    }
                }
            }
        }
        return TupleVo.of("sysEnvProp", StringUtils.join(hideProp, ","));
    }

    @Getter
    public enum SySEnvEnum {
        SINGLE_ENV("单环境", "single"),
        FULL_ENV("一体化环境", "full"),
        TEST_ENV("测试环境", "test"),
        PRD_ENV("生产环境", "prd");
        String label;
        String val;

        SySEnvEnum(String label, String val) {
            this.label = label;
            this.val = val;
        }

        public static SySEnvEnum labelOf(String label) {
            for (SySEnvEnum sySEnvEnum : SySEnvEnum.values()) {
                if (sySEnvEnum.label.equals(label)) {
                    return sySEnvEnum;
                }
            }
            return null;
        }

        public static SySEnvEnum valOf(String val) {
            for (SySEnvEnum sySEnvEnum : SySEnvEnum.values()) {
                if (sySEnvEnum.val.equals(val)) {
                    return sySEnvEnum;
                }
            }
            return null;
        }
    }
}
