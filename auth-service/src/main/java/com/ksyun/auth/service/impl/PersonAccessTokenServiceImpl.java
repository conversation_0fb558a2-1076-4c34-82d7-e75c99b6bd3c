package com.ksyun.auth.service.impl;

import com.ksyun.auth.dao.PersonAccessTokenMapper;
import com.ksyun.auth.service.PersonAccessTokenService;
import com.ksyun.auth.utils.PersonAccessTokenGenerator;
import com.ksyun.common.entity.PersonAccessToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 个人访问令牌服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonAccessTokenServiceImpl implements PersonAccessTokenService {

    private final PersonAccessTokenMapper personAccessTokenMapper;
    private final PersonAccessTokenGenerator personAccessTokenGenerator;

    /**
     * 默认令牌有效期（秒），30天
     */
    private static final long DEFAULT_LIFETIME_SECONDS = 30L * 24 * 60 * 60;

    @Override
    @Transactional
    public PersonAccessToken createToken(String issuerUrl, Long userId, String username) {
        log.info("开始创建个人访问令牌, userId={}, username={}", userId, username);
        
        // 先删除用户现有的token
        personAccessTokenMapper.deleteAllByUserId(userId);
        log.info("删除用户现有令牌, userId={}", userId);

        try {
            // 生成新的token
            String tokenValue = personAccessTokenGenerator.generateToken(issuerUrl, userId.toString(), username, DEFAULT_LIFETIME_SECONDS);
            log.info("生成新的令牌值, userId={}", userId);

            // 创建token记录
            PersonAccessToken token = new PersonAccessToken();
            token.setUserId(userId);
            token.setTokenValue(tokenValue);
            token.setLifetimeSeconds(DEFAULT_LIFETIME_SECONDS);
            token.setExpiresAt(new Date(System.currentTimeMillis() + DEFAULT_LIFETIME_SECONDS * 1000));
            token.setCreatedAt(new Date());

            // 保存到数据库
            personAccessTokenMapper.insert(token);
            log.info("个人访问令牌创建成功, userId={}, tokenId={}", userId, token.getId());
            
            return token;
        } catch (Exception e) {
            log.error("创建个人访问令牌失败, userId={}", userId, e);
            throw e;
        }
    }

    @Override
    public List<PersonAccessToken> getTokens(Long userId) {
        log.info("开始查询用户的访问令牌列表, userId={}", userId);
        try {
            List<PersonAccessToken> tokens = personAccessTokenMapper.findValidTokensByUserId(userId);
            log.info("查询用户的访问令牌列表成功, userId={}, tokenCount={}", userId, tokens.size());
            return tokens;
        } catch (Exception e) {
            log.error("查询用户的访问令牌列表失败, userId={}", userId, e);
            throw e;
        }
    }

    @Override
    public PersonAccessToken getTokenByUserId(Long userId) {
        log.info("开始查询用户的访问令牌, userId={}", userId);
        try {
            PersonAccessToken token = personAccessTokenMapper.selectByUserId(userId);
            if (token != null) {
                log.info("查询用户的访问令牌成功, userId={}, tokenId={}", userId, token.getId());
            } else {
                log.info("未找到用户的访问令牌, userId={}", userId);
            }
            return token;
        } catch (Exception e) {
            log.error("查询用户的访问令牌失败, userId={}", userId, e);
            throw e;
        }
    }

    @Override
    public boolean validateToken(String token) {
        try {
            // 使用PersonAccessTokenGenerator验证token并获取用户ID
            String userId = personAccessTokenGenerator.validateToken(token);
            if (userId == null) {
                return false;
            }

            // 从数据库验证token是否存在且匹配
            PersonAccessToken dbToken = getTokenByUserId(Long.parseLong(userId));
            return dbToken != null && dbToken.getTokenValue().equals(token);
        } catch (Exception e) {
            return false;
        }
    }
}
