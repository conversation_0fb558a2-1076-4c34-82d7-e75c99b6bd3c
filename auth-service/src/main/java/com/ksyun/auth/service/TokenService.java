package com.ksyun.auth.service;

import java.util.Collection;

public interface TokenService {

    /**
     * 获取DSK
     */
    String getDsk();

    /**
     * 获取指定 Tenant 的 SK
     */
    String getTenantAkSk(String tenant);

    /**
     * 获取指定APP的TOKEN，策略是获取该APP的SK以及最近两次DSK，生成两个TOKEN，一并返回
     */
    Collection<String> getAppTokens(String app);
    /**
     * 获取指定 Tenant 的TOKEN，策略是获取该 Tenant 的SK以及最近两次DSK，生成两个TOKEN，一并返回
     */
    Collection<String> getTenantTokens(String tenant);

    /**
     * 生成动态key
     */
    void addDynamicKeyOnPeriod();
}
