package com.ksyun.auth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.dao.TenantMapper;
import com.ksyun.auth.dao.UserMapper;
import com.ksyun.auth.dao.UserTenantMapper;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.dto.UserDto;
import com.ksyun.auth.service.TenantService;
import com.ksyun.auth.vo.*;
import com.ksyun.common.entity.Tenant;
import com.ksyun.common.entity.User;
import com.ksyun.common.entity.UserTenant;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.enums.TenantStatusEnum;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 租户服务类
 */
@Service
@Slf4j
public class TenantServiceImpl implements TenantService {
    @Autowired
    UserMapper userMapper;

    @Autowired
    TenantMapper tenantMapper;

    @Autowired
    UserTenantMapper userTenantMapper;

    @Value("${tenant.maxCount:10}")
    Integer maxTenantCount;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(KcdeTenantAddVo parameter) {
        // TODO lock表之后进行下列创建租户流程
        // 1. 验证是否有未被删除的同名租户
        LambdaQueryWrapper<Tenant> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Tenant::getName, parameter.getName());
        queryWrapper.in(Tenant::getStatus, TenantStatusEnum.NORMAL, TenantStatusEnum.DELETING);

        Long tenantCount = tenantMapper.selectCount(queryWrapper);
        Assert.isTrue(tenantCount == null || tenantCount < 1, BusinessExceptionEnum.TENANT_EXISTED);

        LambdaQueryWrapper<Tenant> countWrapper = Wrappers.lambdaQuery();
        countWrapper.in(Tenant::getStatus, TenantStatusEnum.NORMAL, TenantStatusEnum.DELETING);
        Long totalCount = tenantMapper.selectCount(countWrapper);
        if (totalCount != null && totalCount >= maxTenantCount) {
            throw new BusinessException("超过最大租户数量限额，最大数量为：" + maxTenantCount);
        }

        // 2. 验证参数中的用户是否存在
        List<Long> userIds = parameter.getUserIds();
        if (userIds != null && userIds.size() != 0) {
            LambdaQueryWrapper<User> userWrapper = Wrappers.lambdaQuery();
            userWrapper.in(User::getId, userIds);
            Long userCount = userMapper.selectCount(userWrapper);
            log.debug("Existing user ids {} {}", userIds, userCount);

            Assert.isTrue(userCount != null && userCount == userIds.size(), BusinessExceptionEnum.USER_NOT_EXISTED);
        }

        // 3. 计算生成新的租户id
        HashSet<Long> tenantIdsPoolSet = new HashSet<>();
        for (Long i = 1L; i <= 9999L; i++) {
            tenantIdsPoolSet.add(i);
        }

        queryWrapper = Wrappers.lambdaQuery();
        List<Tenant> tenants = tenantMapper.selectList(queryWrapper);

        tenants.forEach(tenant -> tenantIdsPoolSet.remove(tenant.getId()));
        ArrayList<Long> tenantIdsPool = new ArrayList<>(tenantIdsPoolSet);

        // kcde 0.6 需求，最多9999个租户，租户id会作为k8s资源名称使用，所以有长度限制
        if (tenantIdsPool.size() == 0) {
            throw new BusinessException("达到最大租户上限（9999），可尝试清理已删除租户");
        }

        Long random = Math.abs(new Random().nextLong());
        Long index = random % tenantIdsPool.size();
        log.debug("Tenant ids index and pool is  {} {}", index, tenantIdsPool);
        Long newTenantId = tenantIdsPool.get(index.intValue());

        LocalDateTime localDateTime = LocalDateTime.now();

        // 4. 创建租户entity
        Tenant tenant = new Tenant();
        tenant.setId(newTenantId);
        tenant.setName(parameter.getName());
        tenant.setRemark(parameter.getRemark());
        tenant.setDescription(parameter.getDescription());
        tenant.setCreatedBy(parameter.getCreatedBy());
        tenant.setStatus(TenantStatusEnum.NORMAL.name());
        tenant.setCreateTime(localDateTime);
        tenant.setUpdateTime(tenant.getCreateTime());
        if (tenantMapper.insert(tenant) == 0) {
            throw new BusinessException("创建租户失败");
        }

        if (userIds != null && userIds.size() != 0) {
            for (Long userId : userIds) {
                UserTenant userTenant = new UserTenant();
                userTenant.setUserId(userId);
                userTenant.setTenantId(tenant.getId());
                userTenant.setCreateTime(localDateTime);
                if (userTenantMapper.insert(userTenant) == 0) {
                    throw new BusinessException("绑定用户到租户失败");
                }
            }
        }

        log.debug("Saved Tenant: tenant={}, users={}", tenant.getName(), userIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(KcdeTenantUpdateVo parameter) {
        // 1. 校验用户存在
        LambdaQueryWrapper<Tenant> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Tenant::getId, parameter.getId());
        queryWrapper.in(Tenant::getStatus, TenantStatusEnum.NORMAL, TenantStatusEnum.DELETING);
        Tenant tenant = tenantMapper.selectOne(queryWrapper);

        Assert.isTrue(tenant != null, BusinessExceptionEnum.TENANT_NOT_FOUND);

        // 2. 如果传递了租户名称，校验当前是否已有同名租户
        if (parameter.getName() != null && !parameter.getName().equals("")) {
            queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(Tenant::getName, parameter.getName());
            Tenant newTenant = tenantMapper.selectOne(queryWrapper);

            Assert.isTrue(newTenant == null, BusinessExceptionEnum.TENANT_EXISTED);
        }

        // 3. 如果传递了状态，校验合法性
        if (parameter.getStatus() != null) {
            try {
                TenantStatusEnum status = TenantStatusEnum.valueOf(parameter.getStatus());
            } catch (IllegalArgumentException e) {
                throw new BusinessException("租户状态非法");
            }
            if (parameter.getId() == 0) {
                throw new BusinessException("禁止更新默认租户状态");
            }
        }

        // 4. 查询租户当前已经绑定的用户
        LambdaQueryWrapper<UserTenant> userTenantQueryWrapper = Wrappers.lambdaQuery();
        userTenantQueryWrapper.eq(UserTenant::getTenantId, tenant.getId());
        List<UserTenant> userTenants = userTenantMapper.selectList(userTenantQueryWrapper);

        // 5. 计算要解绑的用户和新增加的用户
        HashSet<Long> currentUsers = new HashSet<>();
        HashSet<Long> requestedUsers = new HashSet<>();
        HashSet<Long> usersToDelete = new HashSet<>();
        HashSet<Long> usersToAdd = new HashSet<>();

        userTenants.forEach(userTenant -> currentUsers.add(userTenant.getUserId()));
        if (parameter.getUserIds() != null) {
            requestedUsers.addAll(parameter.getUserIds());
        }

        for (Long cu : currentUsers) {
            if (!requestedUsers.contains(cu)) {
                usersToDelete.add(cu);
            }
        }

        for (Long cu : requestedUsers) {
            if (!currentUsers.contains(cu)) {
                usersToAdd.add(cu);
            }
        }

        log.debug("currentUsers {}, requestedUsers {}, usersToDelete {}, usersToAdd {}",
                currentUsers, requestedUsers, usersToDelete, usersToAdd);

        // 6. 验证新绑定用户是否存在
        if (usersToAdd.size() > 0) {
            LambdaQueryWrapper<User> userWrapper = Wrappers.lambdaQuery();
            userWrapper.in(User::getId, usersToAdd);
            Long userCount = userMapper.selectCount(userWrapper);

            Assert.isTrue(userCount != null && userCount == usersToAdd.size(), BusinessExceptionEnum.USER_NOT_EXISTED);
        }

        // 7. 同步到数据库中
        LambdaUpdateWrapper<Tenant> tenantUpdateWrapper = Wrappers.lambdaUpdate();
        tenantUpdateWrapper.eq(Tenant::getId, tenant.getId());
        Boolean updateTenant = false;
        if (parameter.getName() != null && !parameter.getName().equals("") && !tenant.getName().equals(parameter.getName())) {
            // 默认default租户忽略对名称的更新
            if (!tenant.getName().equals("default")) {
                tenant.setName(parameter.getName());
                updateTenant = true;
            }
        }
        if (parameter.getStatus() != null) {
            tenant.setStatus(parameter.getStatus());
            updateTenant = true;
        }
        if (parameter.getDescription() != null) {
            tenant.setDescription(parameter.getDescription());
            updateTenant = true;
        }
        if (updateTenant) {
            tenantMapper.update(tenant, tenantUpdateWrapper);
        }

        if (usersToDelete.size() > 0) {
            LambdaQueryWrapper<UserTenant> userTenantDeleteWrapper = Wrappers.lambdaQuery();
            userTenantDeleteWrapper.eq(UserTenant::getTenantId, tenant.getId());
            userTenantDeleteWrapper.in(UserTenant::getUserId, usersToDelete);
            userTenantMapper.delete(userTenantDeleteWrapper);
        }
        if (usersToAdd.size() > 0) {
            LocalDateTime localDateTime = LocalDateTime.now();
            for (Long uid : usersToAdd) {
                UserTenant userTenant = new UserTenant();
                userTenant.setTenantId(tenant.getId());
                userTenant.setUserId(uid);
                userTenant.setCreateTime(localDateTime);
                userTenantMapper.insert(userTenant);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long tenantId) {
        LambdaQueryWrapper<Tenant> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Tenant::getId, tenantId);
        queryWrapper.in(Tenant::getStatus, TenantStatusEnum.NORMAL, TenantStatusEnum.DELETING);
        Tenant tenant = tenantMapper.selectOne(queryWrapper);

        Assert.isTrue(tenant != null, BusinessExceptionEnum.TENANT_NOT_FOUND);

        if (tenant.getId() == 0) {
            throw new BusinessException("禁止删除默认租户");
        }

        tenant.setStatus(TenantStatusEnum.DELETED.name());

        LambdaUpdateWrapper<Tenant> tenantUpdateWrapper = Wrappers.lambdaUpdate();
        tenantUpdateWrapper.eq(Tenant::getId, tenant.getId());
        tenantMapper.update(tenant, tenantUpdateWrapper);

        LambdaQueryWrapper<UserTenant> userTenantDeleteWrapper = Wrappers.lambdaQuery();
        userTenantDeleteWrapper.eq(UserTenant::getTenantId, tenant.getId());
        userTenantMapper.delete(userTenantDeleteWrapper);
    }

    @Override
    @Transactional
    public List<KcdeTenantVo> list(KcdeTenantQueryVo parameter) {
        return tenantMapper.selectTenantByCondition(parameter);
    }

    @Override
    @Transactional
    public Page<KcdeTenantVo> page(KcdeTenantQueryVo parameter) {
        List<KcdeTenantVo> data = tenantMapper.selectTenantByCondition(parameter);
        int count = tenantMapper.selectTenantCountByCondition(parameter);
        Page<KcdeTenantVo> page = new Page<>();
        page.setRecords(data);
        page.setTotal(count);
        page.setSize(parameter.getPageSize());
        page.setCurrent(parameter.getPageNo());
        return page;
    }

    @Override
    public KcdeTenantVo getTenantById(Long tenantId) {
        KcdeTenantQueryVo parameter = new KcdeTenantQueryVo();
        parameter.setTenantId(tenantId);
        List<KcdeTenantVo> tenants = tenantMapper.selectTenantByCondition(parameter);
        Assert.isTrue(tenants.size() > 0, "用户不存在");
        return tenants.get(0);
    }

    @Override
    public KcdeTenantVo getTenantByName(String tenantName) {
        KcdeTenantQueryVo parameter = new KcdeTenantQueryVo();
        parameter.setTenantName(tenantName);
        List<KcdeTenantVo> tenants = tenantMapper.selectTenantByCondition(parameter);
        Assert.isTrue(tenants.size() > 0, "用户不存在");
        return tenants.get(0);
    }

    @Override
    public List<UserTenant> getTenantUsers(Long tenantId) {
        LambdaQueryWrapper<UserTenant> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserTenant::getTenantId, tenantId);
        List<UserTenant> tenantUsers = userTenantMapper.selectList(queryWrapper);
        log.debug("Tenant users: {}", tenantUsers);
        return tenantUsers;
    }

    @Override
    public void addTenantUser(Long tenantId, Long userId) {
        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(User::getId, userId);
        User user = userMapper.selectOne(userQuery);
        Assert.isTrue(user != null, BusinessExceptionEnum.USER_NOT_FOUND);

        LambdaQueryWrapper<Tenant> tenantQuery = new LambdaQueryWrapper<>();
        tenantQuery.eq(Tenant::getId, tenantId);
        Tenant tenant = tenantMapper.selectOne(tenantQuery);
        Assert.isTrue(tenant != null, BusinessExceptionEnum.USER_NOT_FOUND);

        UserTenant userTenant = new UserTenant();
        userTenant.setTenantId(tenant.getId());
        userTenant.setUserId(userId);
        userTenant.setCreateTime(LocalDateTime.now());
        userTenantMapper.insert(userTenant);
    }

    @Override
    public void deleteTenantUser(Long tenantId, Long userId) {
        LambdaQueryWrapper<UserTenant> query = new LambdaQueryWrapper<>();
        query.eq(UserTenant::getTenantId, tenantId);
        query.eq(UserTenant::getUserId, userId);
        userTenantMapper.delete(query);
    }

    @Override
    public void batchDeleteTenantUser(KcdeTenantUserDeleteVo parameter) {
        LambdaQueryWrapper<UserTenant> query = new LambdaQueryWrapper<>();
        query.eq(UserTenant::getTenantId, parameter.getTenantId());
        query.in(UserTenant::getUserId, parameter.getUserIds());
        userTenantMapper.delete(query);
    }

    @Override
    public List<BaseUser> tenantUsers(KcdeTenantUserQueryVo parameter) {
        return userTenantMapper.selectTenantUserByCondition(parameter);
    }

    @Override
    public Page<BaseUser> tenantUserPage(KcdeTenantUserQueryVo parameter) {
        List<BaseUser> data = userTenantMapper.selectTenantUserByCondition(parameter);
        int count = userTenantMapper.selectTenantUserCountByCondition(parameter);
        Page<BaseUser> page = new Page<>();
        page.setRecords(data);
        page.setTotal(count);
        page.setSize(parameter.getPageSize());
        page.setCurrent(parameter.getPageNo());
        return page;
    }
}
