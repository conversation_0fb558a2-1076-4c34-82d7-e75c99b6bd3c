/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.event;

import com.ksyun.auth.service.listener.PrivilegeFileReceiveEventListener;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2020
 */
@Setter
@Getter
public class PrivilegeDataExportEvent extends ApplicationEvent {
    private String fileName;
    private PrivilegeFileReceiveEventListener.PrivilegeScope scope;

    public PrivilegeDataExportEvent(String fileName, PrivilegeFileReceiveEventListener.PrivilegeScope scope) {
        super(fileName);
        this.fileName = fileName;
        this.scope = scope;
    }
}
