package com.ksyun.auth.service.strategy.privilege;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ksyun.auth.dao.PrivilegeMapper;
import com.ksyun.auth.dao.PrivilegePermissionGroupMapper;
import com.ksyun.auth.dto.PrivilegeExcelDto;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.Privilege;
import com.ksyun.common.entity.PrivilegeProps;
import com.ksyun.common.entity.PrivilegePermissionGroup;
import com.ksyun.auth.dao.PrivilegePropsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName PrivilegeDeleteStrategyPrivilege
 * @Desc: 权限导入(删除)策略
 * @Author: PANCHAO
 * @Email: <EMAIL>
 * @Date: 2021/4/25 17:32
 * @Company: 北京金山云网络技术有限公司
 * @Copyright: 2018 Ksyun All Rights Reserved Kingsoft Corp.
 * <p/>
 * ---------------------------------------------------------
 * Version    Author    Status    Date
 * V1.0      PANCHAO      C     2021/4/25
 */
@Component(Constants.PRIVILEGE_DELETE_STRATEGY)
@Slf4j
public class PrivilegeDeleteStrategy extends AbstractPrivilegeImportStrategy {
    @Autowired
    private PrivilegeMapper privilegeMapper;
    @Autowired
    private PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    private PrivilegePropsMapper privilegePropsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImport(List list) {
        log.info("(PrivilegeDeleteStrategy)Privilege import...");
        if (!CollectionUtil.isNotEmpty(list)) {
            return;
        }
        log.info("(PrivilegeDeleteStrategy)Privilege list size is {}", list.size());
        Set<String> privilegeCodes = (Set<String>) list.stream()
                .map(o -> ((PrivilegeExcelDto) o).getCode())
                .collect(Collectors.toSet());
        LambdaQueryWrapper<Privilege> privilegeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        privilegeLambdaQueryWrapper.in(!privilegeCodes.isEmpty(), Privilege::getCode, privilegeCodes);
        List<Privilege> privileges = privilegeMapper.selectList(privilegeLambdaQueryWrapper);
        if (privileges.isEmpty()) {
            return;
        }
        Set<Long> privilegeIds = privileges.stream().map(Privilege::getId).collect(Collectors.toSet());
        //删除权限组和权限关联关系表
        LambdaQueryWrapper<PrivilegePermissionGroup> privilegePermissionGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
        privilegePermissionGroupLambdaQueryWrapper.in(!privilegeIds.isEmpty(), PrivilegePermissionGroup::getPrivilegeId, privilegeIds);
        int privilegePermissionGroupCount = privilegePermissionGroupMapper.delete(privilegePermissionGroupLambdaQueryWrapper);
        log.info("Delete privilegePermissionGroup count is {}", privilegePermissionGroupCount);
        //删除权限扩展表
        LambdaQueryWrapper<PrivilegeProps> privilegePropsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        privilegePropsLambdaQueryWrapper.in(!privilegeIds.isEmpty(), PrivilegeProps::getPrivilegeId, privilegeIds);
        int privilegePropsCount = privilegePropsMapper.delete(privilegePropsLambdaQueryWrapper);
        log.info("Delete privilegeProps count is {}", privilegePropsCount);
        //删除权限表
        int privilegeCount = privilegeMapper.deleteBatchIds(privilegeIds);
        log.info("Delete privilege count is {}", privilegeCount);
    }
}
