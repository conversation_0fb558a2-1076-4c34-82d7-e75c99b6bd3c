package com.ksyun.auth.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.ksyun.auth.dto.UserDto;
import com.ksyun.common.constant.Constants;
import com.ksyun.auth.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 本地缓存服务类
 */
@Service
@ConditionalOnProperty(value = "token.cache.type", havingValue = "local")
@Slf4j
public class LocalCacheServiceImpl implements CacheService<UserDto>, InitializingBean {
    private static final Cache<String, UserDto> GUAVA_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(Constants.SESSION_EXPIRE_SECONDS, TimeUnit.SECONDS)
            .removalListener(new DefaultRemovalListener()).build();

    @Override
    public void set(String key, UserDto value, long expire) {
        GUAVA_CACHE.put(key, value);
    }

    @Override
    public UserDto get(String key) {
        return GUAVA_CACHE.getIfPresent(key);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Loaded CacheService: {}", getClass().getCanonicalName());
    }

    /**
     * 缓存移除通知
     */
    static class DefaultRemovalListener implements RemovalListener<String, UserDto> {

        @Override
        public void onRemoval(RemovalNotification<String, UserDto> notification) {
            log.info("Remove Guava Cache: key={}, value={}, cause={}", notification.getKey(), notification.getValue(), notification.getCause());
        }
    }
}
