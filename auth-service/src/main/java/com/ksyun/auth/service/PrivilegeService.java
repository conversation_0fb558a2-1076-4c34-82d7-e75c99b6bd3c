package com.ksyun.auth.service;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dto.PrivilegePropsDto;
import com.ksyun.auth.vo.*;
import com.ksyun.common.entity.Privilege;
import com.ksyun.auth.dto.PrivilegeQueryDto;
import jodd.http.Cookie;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PrivilegeService {
    /**
     * 保存权限
     */
    void add(PrivilegeUpdateVo parameter, Integer level);

    /**
     * 更新权限
     */
    void update(PrivilegeUpdateVo parameter, Integer level);

    /**
     * 删除权限
     */
    void delete(PrivilegeDeleteVo parameter);

    /**
     * 按ID查询权限
     */
    PrivilegeVo get(int id);

    /**
     * 获取所有权限
     */
    List<PrivilegeVo> getAll();

    /**
     * 根据应用获取所有权限
     * @param ak
     * @return
     */
    List<PrivilegeVo> getAllByAK(String ak);

    /**
     * 绑定角色和权限
     */
    void addBindPrivilegeRole(RolePrivilegeVo parameters);

    /**
     * 获取权限列表
     */
    List<PrivilegeVo> getPrivileges(PrivilegeQueryDto queryDto);

    void addGrantPrivilege(GrantPrivilegeVo grantPrivilegeVo);

    Set<Long> getRolePrivileges(Set<Long> roleIds);

    List<PrivilegePropsDto> getPrivilegeProps(Set<Long> privilegeIds, List<String> propKey);

    List<MenuPropsTreeItemVo> getIndexMenus(Cookie cookie, AuthUser user, HttpServletRequest request, String menuType);

    List<String> getPrivilegeByUser(PrivilegeQueryVo parameter, AuthUser authentication, HttpServletRequest request);

    /**
     * 向上移动节点
     *
     * @param privilege
     * @return
     */
    boolean moveUpPrivilege(PrivilegeVo privilege);

    /**
     * 置顶权限节点
     *
     * @param privilegeEntity
     * @return
     */
    boolean setTopPrivilege(PrivilegeVo privilegeEntity);

    /**
     * 至底权限节点
     *
     * @param privilegeEntity
     * @return
     */
    boolean setButtomPrivilege(PrivilegeVo privilegeEntity);

    /**
     * 向下移动权限节点
     *
     * @param privilegeEntity
     * @return
     */
    Boolean moveDownPrivilege(PrivilegeVo privilegeEntity);

    PrivilegeVo getSmallerPrivilege(Long parentId, Long order);

    PrivilegeVo getLargerPrivilege(Long parentId, Long order);

    int updateOrder(Long privilegeId, Long order);

    PrivilegeVo getFirstMinPrivilege(PrivilegeVo privilegeEntity);

    PrivilegeVo getLastMaxPrivilege(PrivilegeVo privilegeEntity);

    int updatePrePrivilegeOrders(Long parentId, Long currOrder);

    int updateAfterPrivilegeOrders(Long parentId, Long currOrder);

    List<Privilege> getPrivilegeList(AuthUser user,String from);

    /**
     * 获取应用集合
     */
    List<BasicAppVo> lookupAppList();
}
