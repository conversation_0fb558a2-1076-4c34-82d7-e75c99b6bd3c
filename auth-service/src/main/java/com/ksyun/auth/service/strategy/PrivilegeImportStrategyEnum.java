/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy;

/**
 * <AUTHOR>
 * @since 2020
 */
public enum PrivilegeImportStrategyEnum {

    IGNORE(1, "privilegeIgnoreStrategy"),
    OVERWRITE(2, "privilegeOverwriteStrategy"),
    EXTERNAL(3, "externalStrategy"),
    UPDATE(4, "privilegeUpdateStrategy"),
    DELETE(5, "privilegeDeleteStrategy"),
    ;
    int code;
    String type;

    PrivilegeImportStrategyEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public static PrivilegeImportStrategyEnum codeOf(int code) {
        for (PrivilegeImportStrategyEnum importStrategyEnum : PrivilegeImportStrategyEnum.values()) {
            if (importStrategyEnum.code == code) {
                return importStrategyEnum;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
