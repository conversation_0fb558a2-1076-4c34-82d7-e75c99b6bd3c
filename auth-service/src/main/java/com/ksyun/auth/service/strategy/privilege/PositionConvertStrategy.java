/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component(Constants.POSITION)
public class PositionConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {

    private static final String PROP_VALUE = "true";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (!StringUtil.isEmpty(s)) {
            String[] splits = s.replace("，", ",").split(",", -1);
            for (String split : splits) {
                PositionEnum positionEnum = PositionEnum.labelOf(split);
                if (null != positionEnum) {
                    PrivilegeProps props = new PrivilegeProps();
                    props.setPropKey(positionEnum.key);
                    props.setPropValue(PROP_VALUE);
                    list.add(props);
                }
            }
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        Set<String> positionProp = Sets.newHashSet();
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (!StringUtil.isEmpty(kv[0])) {
                        if (PositionEnum.HOME.key.equals(kv[0])
                                || PositionEnum.BOTTOM.key.equals(kv[0])
                                || PositionEnum.POP.key.equals(kv[0])) {
                            positionProp.add(PositionConvertStrategy.PositionEnum.keyOf(kv[0]).getLabel());
                        }
                    }
                }
            }
        }
        return TupleVo.of("positionProp", StringUtils.join(positionProp, ","));
    }

    @Getter
    public enum PositionEnum {
        HOME("首页", "isIndex"),
        BOTTOM("底部", "positionDown"),
        POP("跳出", "jumpOutAlone");
        String label;
        String key;

        PositionEnum(String label, String key) {
            this.label = label;
            this.key = key;
        }

        public static PositionEnum labelOf(String label) {
            for (PositionEnum positionEnum : PositionEnum.values()) {
                if (positionEnum.label.equals(label)) {
                    return positionEnum;
                }
            }
            return null;
        }

        public static PositionEnum keyOf(String key) {
            for (PositionEnum positionEnum : PositionEnum.values()) {
                if (positionEnum.key.equals(key)) {
                    return positionEnum;
                }
            }
            return null;
        }
    }
}
