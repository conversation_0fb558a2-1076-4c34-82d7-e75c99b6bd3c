/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy;

/**
 * <AUTHOR>
 * @since 2020
 */
public enum RoleImportStrategyEnum {

    ROLE_IGNORE_PG_IGNORE(11, "roleIgnorePermissionGroupIgnoreStrategy"),
    <PERSON><PERSON><PERSON>_IGNORE_PG_CREATE(12, "roleIgnorePermissionGroupCreateStrategy"),
    ROLE_OW_PG_IGNORE(21, "roleOverwritePermissionGroupIgnoreStrategy"),
    ROLE_OW_PG_CREATE(22, "roleOverwritePermissionGroupCreateStategy");
    int code;
    String type;

    RoleImportStrategyEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public static RoleImportStrategyEnum codeOf(int code) {
        for (RoleImportStrategyEnum importStrategyEnum : RoleImportStrategyEnum.values()) {
            if (importStrategyEnum.code == code) {
                return importStrategyEnum;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
