/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component(Constants.HIDE)
public class HideConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {

    public static final String PROP_KEY = "profile";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (!StringUtil.isEmpty(s)) {
            String[] splits = s.replace("，", ",").split(",", -1);
            for (String split : splits) {
                HideEnum hideEnum = HideEnum.labelOf(split);
                if (null != hideEnum) {
                    PrivilegeProps props = new PrivilegeProps();
                    props.setPropKey(PROP_KEY);
                    props.setPropValue(hideEnum.val);
                    list.add(props);
                }
            }
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        Set<String> hideProp = Sets.newHashSet();
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (!StringUtil.isEmpty(kv[0])) {
                        if (PROP_KEY.equals(kv[0]) && kv.length > 1) {
                            hideProp.add(HideConvertStrategy.HideEnum.valOf(kv[1]).getLabel());
                        }
                    }
                }
            }
        }
        return TupleVo.of("hideProp", StringUtils.join(hideProp, ","));
    }

    @Getter
    public enum HideEnum {
        PROD("三态分离-生产项目", "projectProdHide"),
        TEST("三态分离-测试项目", "projectTestHide"),
        FULL("一体化环境", "full"),
        PROD_ENV("生产环境", "prod"),
        TEST_ENV("测试环境", "test");
        String label;
        String val;

        HideEnum(String label, String val) {
            this.label = label;
            this.val = val;
        }

        public static HideEnum labelOf(String label) {
            for (HideEnum hideEnum : HideEnum.values()) {
                if (hideEnum.label.equals(label)) {
                    return hideEnum;
                }
            }
            return null;
        }

        public static HideEnum valOf(String val) {
            for (HideEnum hideEnum : HideEnum.values()) {
                if (hideEnum.val.equals(val)) {
                    return hideEnum;
                }
            }
            return null;
        }
    }
}
