/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.service.strategy.privilege;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ksyun.auth.vo.TupleVo;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.entity.PrivilegeProps;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
@Component(Constants.SERVICE)
public class ServiceConvertStrategy implements ConvertStrategy<String, List<PrivilegeProps>, String, TupleVo> {

    public static final String PROP_KEY = "code";

    @Override
    public List<PrivilegeProps> fromExcel(String s) {
        List<PrivilegeProps> list = Lists.newArrayList();
        if (!StringUtil.isEmpty(s)) {
            String[] splits = s.replace("，", ",").split(",", -1);
            for (String split : splits) {
                ServiceEnum netEnum = ServiceEnum.labelOf(split);
                if (null != netEnum) {
                    PrivilegeProps props = new PrivilegeProps();
                    props.setPropKey(PROP_KEY);
                    props.setPropValue(netEnum.val);
                    list.add(props);
                }
            }
        }
        return list;
    }

    @Override
    public TupleVo toExcel(String props) {
        Set<String> serviceProp = Sets.newHashSet();
        if (StringUtils.isNotEmpty(props)) {
            String[] split = props.split(",", -1);
            for (String prop : split) {
                if (StringUtils.isNotEmpty(prop)) {
                    String[] kv = prop.split("=");
                    if (!StringUtil.isEmpty(kv[0])) {
                        if (PROP_KEY.equals(kv[0])) {
                            if (kv.length > 1) {
                                ServiceEnum serviceEnum = ServiceConvertStrategy.ServiceEnum.valOf(kv[1]);
                                if (serviceEnum != null) {
                                    serviceProp.add(serviceEnum.getLabel());
                                }
                            }
                        }
                    }
                }
            }
        }
        return TupleVo.of("serviceProp", StringUtils.join(serviceProp, ","));
    }

    @Getter
    public enum ServiceEnum {
        KSC("流计算", "KSC"),
        KDG("数据采集", "KDG"),
        KDI("数据集成", "KDI"),
        KBDC("离线计算", "KBDC"),
        KDM("数据挖掘", "KDM"),
        KDS("数据服务", "KDS"),
        KGW("API网关", "KGW"),
        KDQ("数据管理", "KDQ"),
        MPP("MPP云数仓", "MPP"),
        KBI("可视化BI", "KBI"),
        KGAP("图分析平台", "KGAP"),
        KFC("图计算", "KFC"),
        KMR("托管Hadoop", "KMR"),
        GDB("图数据库", "GDB"),
        ;
        String label;
        String val;

        ServiceEnum(String label, String val) {
            this.label = label;
            this.val = val;
        }

        public static ServiceEnum labelOf(String label) {
            for (ServiceEnum positionEnum : ServiceEnum.values()) {
                if (positionEnum.label.equals(label)) {
                    return positionEnum;
                }
            }
            return null;
        }

        public static ServiceEnum valOf(String val) {
            for (ServiceEnum positionEnum : ServiceEnum.values()) {
                if (positionEnum.val.equals(val)) {
                    return positionEnum;
                }
            }
            return null;
        }
    }
}
