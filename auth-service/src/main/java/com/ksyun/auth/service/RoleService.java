package com.ksyun.auth.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.dto.GroupSimpleDto;
import com.ksyun.auth.dto.RoleExcelDto;
import com.ksyun.auth.dto.UserSimpleDto;
import com.ksyun.auth.vo.RoleQueryVo;
import com.ksyun.auth.vo.RoleTagsVo;
import com.ksyun.auth.vo.RoleUpdateVo;
import com.ksyun.auth.vo.RoleVo;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.Role;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;


public interface RoleService {


    /**
     * 查询角色
     */
    List<Role> getRoles(RoleQueryVo parameter, Authentication authentication);

    /**
     * 根据code获取角色
     */
    RoleVo getRoleByCode(String code, String source);

    /**
     * 获取角色
     */
    Role get(Long id);

    /**
     * 查询用户的所有角色
     */
    List<RoleVo> getRolesByUserId(Long userId);

    void updateRoleByBigData(RoleUpdateVo role, AuthUser user);

    List<RoleTagsVo> getAllTags();

    void addBindOrDeleteBindUserAndRoles(Set<Long> groupIds, Set<Long> roleIds);

    /**
     * 绑定用户和角色
     */
    void addBindUserAndRoles(Set<Long> userIds, Set<Long> roleIds);

    /**
     * 绑定用户和角色
     */
    void addBindUserAndRoles(Long userId, Long roleId);

    /**
     * 绑定用户和角色
     */
    void addBindUserAndRoles(Long userId,String roleName);

    /**
     * 解绑用户和角色
     */
    void deleteBindUserAndRoles(Set<Long> userIds, Set<Long> roleIds);

    /**
     * 绑定分组和角色
     */
    void addBindGroupAndRoles(Set<Long> groupIds, Set<Long> roleIds, AuthUser authUser);

    /**
     * 绑定分组和角色
     */
    void addBindGroupsAndRole(Set<Long> groupIds, Long roleId);

    /**
     * 解绑分组和角色
     */
    void deleteBindGroupAndRoles(Set<Long> groupIds, Set<Long> roleIds);

    /**
     * 更新分组角色
     */
    void updateBindAndUnbindGroupAndRoles(Set<Long> groupIds, Set<Long> roleIds, AuthUser authUser);

    void deleteRoleById(Long roleId);

    Long getRolesCountByIds(Set<Long> ids);

    void importRole(List<RoleExcelDto> list, String strategy);

    void addRole(RoleVo roleVo, AuthUser user, String envVersion);

    Page<Role> page(RoleQueryVo parameter, Authentication authentication);

    Role getRoleByCreateBy(Long roleId);

    Map<String, List<GroupSimpleDto>> addGroupBound(Long roleId, AuthUser authUser);

    Map<String, List<UserSimpleDto>> addUserBound(long groupId, AuthUser authUser);

    /**
     * 绑定角色和权限组关系
     */
    void addBindRolePermissionGroup(MultipartFile file);
}
