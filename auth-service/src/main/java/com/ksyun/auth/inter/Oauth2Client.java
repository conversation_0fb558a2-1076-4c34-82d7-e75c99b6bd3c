package com.ksyun.auth.inter;


import com.dtflys.forest.annotation.*;
import com.ksyun.auth.inter.interceptor.Oauth2HeaderInterceptor;
import com.ksyun.auth.inter.response.Oauth2LogoutResp;
import com.ksyun.auth.inter.response.Oauth2TokenResp;
import com.ksyun.auth.inter.response.Oauth2UserinfoResp;
import com.ksyun.common.constant.Response;
import org.springframework.stereotype.Repository;

@BaseRequest(baseURL = "${oauth2TokenUrl}",interceptor = Oauth2HeaderInterceptor.class)
@Repository
public interface Oauth2Client {

    @PostRequest(value = "/token")
    Oauth2TokenResp getOauth2Token(@Query("grant_type") String grantType,@Query("code") String code);

    @PostRequest(value = "/check_token")
    Response.OauthResponse<Oauth2TokenResp> checkOauth2Token(@Query("token") String token);

    @GetRequest(value = "/userinfo")
    Response.OauthResponse<Oauth2UserinfoResp> getOauth2Userinfo(@Query("code") String code, @Query("token") String token);

    @PostRequest(value = "/logout")
    Oauth2LogoutResp logoutOauth2(@Query("token") String token);

}
