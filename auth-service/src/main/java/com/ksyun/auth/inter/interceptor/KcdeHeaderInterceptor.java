package com.ksyun.auth.inter.interceptor;

import com.dtflys.forest.http.ForestCookies;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KcdeHeaderInterceptor implements Interceptor<String> {

    /**
     * 该方法在请求发送之前被调用, 若返回false则不会继续发送请求
     * @Param request Forest请求对象
     */
    @Override
    public boolean beforeExecute(ForestRequest request) {
        return true;
    }

    /**
     * 在发送请求前，需要加载Cookie时调用该方法
     * @param request Forest请求对象
     * @param cookies Cookie集合, 需要通过请求发送的Cookie都添加到该集合
     */
    @Override
    public void onLoadCookie(ForestRequest request, ForestCookies cookies) {

    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("返回参数：{}", response.getContent());
    }
}
