package com.ksyun.auth.inter.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class Oauth2UserinfoResp implements Serializable {
    private String id;

    private String loginName;

    private String cnName;

    private String alias;

    private String email;

    private String phone;

    private String source;

    @Override
    public String toString() {
        return "Oauth2UserinfoResp{" +
                "id='" + id + '\'' +
                ", loginName='" + loginName + '\'' +
                ", cnName='" + cnName + '\'' +
                ", alias='" + alias + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", source='" + source + '\'' +
                '}';
    }
}
