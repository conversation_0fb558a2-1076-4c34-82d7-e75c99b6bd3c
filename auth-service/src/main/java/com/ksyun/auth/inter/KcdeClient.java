package com.ksyun.auth.inter;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.GetRequest;
import com.ksyun.auth.inter.interceptor.KcdeHeaderInterceptor;
import com.ksyun.auth.inter.response.LicenseResp;
import com.ksyun.common.constant.Response;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;


@BaseRequest(baseURL = "${velaApiUrl}",interceptor = KcdeHeaderInterceptor.class)
@Repository
@Component
public interface KcdeClient {


    /**
     * 获取license
     * @return
     */
    @GetRequest("/api/kcde/v1/license/time")
    Response.RichResponse<LicenseResp> getLicenseInfo();

}
