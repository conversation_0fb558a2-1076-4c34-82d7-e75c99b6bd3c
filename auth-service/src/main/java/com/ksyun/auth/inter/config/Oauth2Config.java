package com.ksyun.auth.inter.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * oauth2配置信息
 * <AUTHOR>
 * @since 2022-12-09
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "oauth2")
public class Oauth2Config implements InitializingBean {

    private Boolean onOff = false;

    private String clientId;

    private String clientSecret;

    private String loginUrl;

    private String admin;

    private String authenticationExpirationTime;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("oauth2配置信息:{}",toString());
    }


}

