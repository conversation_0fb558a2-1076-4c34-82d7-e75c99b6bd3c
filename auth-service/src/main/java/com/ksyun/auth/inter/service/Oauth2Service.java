package com.ksyun.auth.inter.service;

import com.ksyun.auth.inter.Oauth2Client;
import com.ksyun.auth.inter.response.Oauth2LogoutResp;
import com.ksyun.auth.inter.response.Oauth2TokenResp;
import com.ksyun.auth.inter.response.Oauth2UserinfoResp;
import com.ksyun.common.constant.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class Oauth2Service {

    @Autowired
    private Oauth2Client oauth2Client;


    /**
     * 获取Token
     * @return
     */
    public Oauth2TokenResp getOauth2Token(String code){
        return oauth2Client.getOauth2Token("client_credentials", code);
    }

    public Boolean checkOauth2Token(String token){
        boolean active = false;
        Response.OauthResponse<Oauth2TokenResp> response = oauth2Client.checkOauth2Token(token);
        if(response != null){
            active = response.getData().getActive();
        }
        return active;
    }

    public Oauth2UserinfoResp getOauth2Userinfo(String code, String token) throws Exception{
        Response.OauthResponse<Oauth2UserinfoResp> response = oauth2Client.getOauth2Userinfo(code,token);
        if(response != null && response.getStatus() != 200){
            throw new Exception(response.getMessage());
        }
        return response.getData();
    }

    public Oauth2LogoutResp logoutOauth2(String token){
        try {
            Oauth2LogoutResp response = oauth2Client.logoutOauth2(token);
            log.info("111 {}", response);
            log.info("ccc");

            return response;
        }catch (Exception e){
            e.printStackTrace();
        }
        return new Oauth2LogoutResp();
    }

}
