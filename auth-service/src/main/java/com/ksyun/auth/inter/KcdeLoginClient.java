package com.ksyun.auth.inter;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.PostRequest;
import com.dtflys.forest.annotation.Var;
import com.dtflys.forest.callback.OnLoadCookie;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.UserLoginVo;
import org.springframework.stereotype.Repository;

@BaseRequest(baseURL = "${kcdeAuthServerUrl}")
@Repository
public interface KcdeLoginClient {
    @PostRequest(value = "/api/userLogin",
            contentType = "application/x-www-form-urlencoded",
            headers = "nonceId: {nonceId}")
    Response.RichResponse<AuthUser> kcdeLogin(@Body UserLoginVo user, @Var("nonceId") String nonceId, OnLoadCookie onLoadCookie);
}
