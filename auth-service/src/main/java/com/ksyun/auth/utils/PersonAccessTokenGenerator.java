package com.ksyun.auth.utils;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.text.ParseException;
import java.util.Date;
import java.util.UUID;

/**
 * 个人访问令牌生成器
 */
@Component
@RequiredArgsConstructor
public class PersonAccessTokenGenerator {

    @Autowired
    private JWKSource jwkSource;

    /**
     * 生成个人访问令牌
     *
     * @param issuer            认证服务地址
     * @param userId            用户ID
     * @param username          用户名
     * @param expirationSeconds 过期时间（秒）
     * @return 令牌字符串
     */
    public String generateToken(String issuer, String userId, String username, long expirationSeconds) {
        try {
            JWKSet jwkSet = ((ImmutableJWKSet<?>)jwkSource).getJWKSet();
            JWK jwk = jwkSet.getKeys().get(0);
            RSAPrivateKey privateKey = jwk.toRSAKey().toRSAPrivateKey();

            // 创建JWT头部
            JWSHeader header = new JWSHeader.Builder(JWSAlgorithm.RS256)
                    .keyID(jwk.getKeyID())
                    .build();

            // 创建JWT载荷
            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .jwtID(UUID.randomUUID().toString())
                    .issuer(issuer)
                    .subject(username)
                    .issueTime(new Date())
                    .expirationTime(new Date(System.currentTimeMillis() + expirationSeconds * 1000))
                    .claim("user_id", userId)
                    .build();

            // 创建JWT
            SignedJWT signedJWT = new SignedJWT(header, claimsSet);

            // 签名JWT
            signedJWT.sign(new RSASSASigner(privateKey));

            return signedJWT.serialize();
        } catch (JOSEException e) {
            throw new RuntimeException("生成访问令牌失败", e);
        }
    }

    /**
     * 验证个人访问令牌
     *
     * @param token JWT格式的令牌
     * @return 如果令牌有效且未过期返回用户ID，否则返回null
     */
    public String validateToken(String token) {
        try {
            // 解析JWT
            SignedJWT signedJWT = SignedJWT.parse(token);
            
            // 获取公钥进行验证
            JWKSet jwkSet = ((ImmutableJWKSet<?>)jwkSource).getJWKSet();
            JWK jwk = jwkSet.getKeys().get(0);
            RSAPublicKey publicKey = jwk.toRSAKey().toRSAPublicKey();
            
            // 验证签名
            if (!signedJWT.verify(new RSASSAVerifier(publicKey))) {
                return null;
            }
            
            // 获取Claims
            JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
            
            // 验证是否过期
            Date expirationTime = claimsSet.getExpirationTime();
            if (expirationTime != null && expirationTime.before(new Date())) {
                return null;
            }
            
            // 返回用户ID
            return claimsSet.getClaim("user_id").toString();
            
        } catch (ParseException | JOSEException e) {
            return null;
        }
    }
}
