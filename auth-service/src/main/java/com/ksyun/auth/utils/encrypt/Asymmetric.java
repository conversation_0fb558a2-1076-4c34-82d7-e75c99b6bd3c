package com.ksyun.auth.utils.encrypt;

public interface Asymmetric {

    static class SecretKey {
        private String publicKey;
        private String privateKey;

        public SecretKey() {
        }

        public SecretKey(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public void setPublicKey(String publicKey) {
            this.publicKey = publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

        public void setPrivateKey(String privateKey) {
            this.privateKey = privateKey;
        }
    }

    SecretKey initKey();

    String encryptByPublicKey(String data, String key);

    String decryptByPrivateKey(String content, String key);

}
