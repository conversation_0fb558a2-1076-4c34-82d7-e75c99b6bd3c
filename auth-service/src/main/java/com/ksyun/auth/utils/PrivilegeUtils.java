package com.ksyun.auth.utils;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.vo.MenuPropsTreeItemVo;
import com.ksyun.auth.vo.MenuTreeItemVo;
import com.ksyun.auth.vo.PrivilegeVo;
import com.ksyun.common.utils.TreeNode;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2019/12/6 10:12
 * @Description
 * @Version V1
 **/
public class PrivilegeUtils {
    /**
     * 运营后台权限菜单导航
     */
    public static List<MenuPropsTreeItemVo> getMenusForFont(TreeNode<PrivilegeVo> node, String name) {
        List<MenuPropsTreeItemVo> menus = new ArrayList<>();
        for (TreeNode<PrivilegeVo> subNode : node.getChildren()) {
            PrivilegeVo entity = subNode.getData();
            boolean valid = subNode.containsNode(item -> name == null || item.getName().contains(name));
            if (!valid) {
                continue;
            }
            List<MenuPropsTreeItemVo> subMenus = getMenusForFont(subNode, name);
            MenuPropsTreeItemVo menuItemVo = new MenuPropsTreeItemVo(entity.getId(), entity.getType(), entity.getName(), entity.getIcon(), entity.getUrl(), entity.getCode(), subMenus);
            menuItemVo.setSubMenus(subMenus);
            menus.add(menuItemVo);
        }
        return menus;
    }

    /**
     * 运营后台权限点管理菜单树
     */
    public static List<MenuTreeItemVo> getMenus(TreeNode<PrivilegeVo> node, String name) {
        List<MenuTreeItemVo> menus = new ArrayList<>();
        for (TreeNode<PrivilegeVo> subNode : node.getChildren()) {
            PrivilegeVo entity = subNode.getData();
            boolean valid = subNode.containsNode(item -> name == null || item.getName().contains(name));
            if (!valid) {
                continue;
            }
            List<MenuTreeItemVo> subMenus = getMenus(subNode, name);
            MenuTreeItemVo menuItemVo = new MenuTreeItemVo(entity.getId(), entity.getType(), entity.getName(), subMenus);
            menuItemVo.setSubMenus(subMenus);
            menus.add(menuItemVo);
        }
        return menus;
    }

    /**
     * 运营后台权限菜单
     */
    public static List<MenuTreeItemVo> getUncoveredMenus(TreeNode<PrivilegeVo> node, Set<Long> coveredIds) {
        List<MenuTreeItemVo> menus = new ArrayList<>();
        for (TreeNode<PrivilegeVo> subNode : node.getChildren()) {
            PrivilegeVo entity = subNode.getData();
            boolean valid = subNode.containsNode(item -> !coveredIds.contains(item.getId()));
            if (!valid) {
                continue;
            }
            List<MenuTreeItemVo> subMenus = getUncoveredMenus(subNode, coveredIds);
            MenuTreeItemVo menuItemVo = new MenuTreeItemVo(entity.getId(), entity.getType(), entity.getName(), subMenus);
            menuItemVo.setSubMenus(subMenus);
            menus.add(menuItemVo);
        }
        return menus;
    }


    /**
     * 初始化树形结构
     */
    public static void buildTree(List<PrivilegeVo> entities, TreeNode<PrivilegeVo> node, Long id) {
        List<PrivilegeVo> children = entities.stream()
                .filter(item -> Objects.equals(id, item.getParentId()) || item.getParentId() == null)
                .collect(Collectors.toList());
        if (children.size() == 0) {
            return;
        }
        for (PrivilegeVo item : children) {
            TreeNode<PrivilegeVo> childNode = new TreeNode<>(item);
            buildTree(entities, childNode, item.getId());
            node.addChild(childNode);
        }
    }

    /*auditStatus：走审计上报接口传自定义状态值(1、2、3...)，不走为0*/
    public static Map<String, Object> checkResult(String status, String message, AuthUser authUser, String auditStatus) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("status", status);
        resultMap.put("message", message);
        resultMap.put("authUser", authUser);
        resultMap.put("auditStatus", auditStatus);
        return resultMap;
    }

}
