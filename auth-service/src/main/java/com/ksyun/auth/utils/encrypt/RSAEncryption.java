package com.ksyun.auth.utils.encrypt;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * 非对称加密
 * <AUTHOR>
 */
public class RSAEncryption implements Asymmetric {

	public static final String KEY_ALGORITHM = "RSA";
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";
    public static final String PUBLIC_KEY = "RSAPublicKey";
    public static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * 初始化密钥
     *
     * @return
     * @throws Exception
     */
    @Override
    public SecretKey initKey() {
        try {
			KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
			keyPairGen.initialize(1024);
	        KeyPair keyPair = keyPairGen.generateKeyPair();
            return new SecretKey(Base64.encodeBase64String(keyPair.getPublic().getEncoded()),
                    Base64.encodeBase64String(keyPair.getPrivate().getEncoded()));
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
        return null;
    }

    @Override
    public String encryptByPublicKey(String data, String key) {
        // 对公钥解密
        byte[] keyBytes = Base64.decodeBase64(key);

        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        byte[] bytes = new byte[0];
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);;
            Key publicKey = keyFactory.generatePublic(x509KeySpec);
            // 对数据加密
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            bytes = cipher.doFinal(data.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Base64.encodeBase64String(bytes);
    }

    @Override
    public String decryptByPrivateKey(String content, String key) {
        // 对密钥解密
        byte[] keyBytes = Base64.decodeBase64(key);
        byte[] data = Base64.decodeBase64(content);

        byte[] bytes = new byte[0];
        try {
            // 取得私钥
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

            // 对数据解密
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            bytes = cipher.doFinal(data);
        } catch (Exception e) {

        }
        return new String(bytes);
    }

    /**
     * 加密<br>
     * 用公钥加密
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(byte[] data, String key)
            throws Exception {
        // 对公钥解密
        byte[] keyBytes = Base64.decodeBase64(key);

        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);

        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);

        return cipher.doFinal(data);
    }


    /**
     * 加密<br>
     * 用私钥加密
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPrivateKey(byte[] data, String key)
            throws Exception {
        // 对密钥解密
        byte[] keyBytes = Base64.decodeBase64(key);

        // 取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);

        return Base64.encodeBase64(cipher.doFinal(data));
    }

    /**
     * 解密<br>
     * 用公钥解密
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPublicKey(byte[] data, String key)
            throws Exception {
        // 对密钥解密
        byte[] keyBytes = Base64.decodeBase64(key);

        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);

        // 对数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, publicKey);

        return cipher.doFinal(Base64.decodeBase64(data));
    }

    /**
     * 解密<br>
     * 用私钥解密
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPrivateKey(byte[] data, String key)
            throws Exception {
        // 对密钥解密
        byte[] keyBytes = Base64.decodeBase64(key);

        // 取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

        // 对数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        return cipher.doFinal(data);
    }

    /**
     * 用私钥对信息生成数字签名
     *
     * @param data
     *            加密数据
     * @param privateKey
     *            私钥
     * @return
     * @throws Exception
     */
    public static String sign(byte[] data, String privateKey) throws Exception {
        // 解密由base64编码的私钥
        byte[] keyBytes = Base64.decodeBase64(privateKey);

        // 构造PKCS8EncodedKeySpec对象
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);

        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);

        // 取私钥匙对象
        PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);

        // 用私钥对信息生成数字签名
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data);

        return new String(Base64.encodeBase64(signature.sign()));
    }

    /**
     * 校验数字签名
     *
     * @param data
     *            加密数据
     * @param publicKey
     *            公钥
     * @param sign
     *            数字签名
     *
     * @return 校验成功返回true 失败返回false
     * @throws Exception
     *
     */
    public static boolean verify(byte[] data, String publicKey, String sign)
            throws Exception {

        // 解密由base64编码的公钥
        byte[] keyBytes = Base64.decodeBase64(publicKey);

        // 构造X509EncodedKeySpec对象
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);

        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);

        // 取公钥匙对象
        PublicKey pubKey = keyFactory.generatePublic(keySpec);

        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(data);

        // 验证签名是否正常
        return signature.verify(Base64.decodeBase64(sign));
    }


    public static void main(String[] args) throws Exception {
//        SecretKey secretKey = initKey();
//        String publicKey = secretKey.getPublicKey();
//    	String privateKey = secretKey.getPrivateKey();
//
//    	System.out.println("公钥："+publicKey);
//    	System.out.println("私钥："+privateKey);
//
//    	String data = "{'msg':'success','data':[{'name':'小王','age':26}]}";
//
//    	// 1
//    	byte[] encryptByPublicKey = encryptByPublicKey(data.getBytes(), publicKey);
//    	byte[] decryptByPrivateKey = decryptByPrivateKey(encryptByPublicKey, privateKey);
//
//    	System.out.println("公加私解："+new String(decryptByPrivateKey));
//
//    	// 2
//    	byte[] encryptByPrivateKey = encryptByPrivateKey(data.getBytes(), privateKey);
//        System.out.println("私钥加密："+ new String(encryptByPrivateKey));
//        byte[] decryptByPublicKey = decryptByPublicKey(encryptByPrivateKey, publicKey);
//
//    	System.out.println("私加公解："+new String(decryptByPublicKey));
//
//    	// 3
//    	String sign = sign(data.getBytes(), privateKey);
//    	System.out.println("私锁 数字签名："+sign);
//    	boolean verify = verify(data.getBytes(), publicKey, sign);
//    	System.out.println("公锁验证数字签名："+verify);
        RSAEncryption encryption = new RSAEncryption();
        SecretKey secretKey = encryption.initKey();
        System.out.println(String.format("公钥：%s",secretKey.getPublicKey()));
        System.out.println(String.format("私钥：%s",secretKey.getPrivateKey()));
        String data = "{\"companyName\":\"中国建行15\",\"product\":\"KDE\",\"version\":\"official\",\"sn\":\"asdfcwefcweqc34241\",\"nodeNum\":500,\"effectiveType\":1,\"effectiveTime\":null,\"end_time\":null,\"createTime\":\"2020-08-25 16:08:48\"}";
        String s = encryption.encryptByPublicKey(data, secretKey.getPublicKey());

        System.out.println(s);

        String s1 = encryption.decryptByPrivateKey(s, secretKey.getPrivateKey());

        System.out.println(s1);

    }

}
