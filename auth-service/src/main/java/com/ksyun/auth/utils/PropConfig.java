package com.ksyun.auth.utils;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PropConfig implements InitializingBean {

//    @Value("${rsa.secret_key.private_key:''}")
//    private String rsaPrivateKey;

//    @Value("${sm2.secret_key.public_key:}")
//    private String sm2PublicKey;
//
//    @Value("${sm2.secret_key.private_key:}")
//    private String sm2PrivateKey;

    //    @Value("${env.version:ic}")
//    private String envVersion;

    @Value("${app.ak}")
    private String appKey;

    @Value("${app.sk}")
    private String appSecret;

    @Value("${auth.server.url}")
    private String authServerUrl;

//    @Value("${menu.type:ksyun}")
//    private String menuType;

    @Value("${menu.level:3}")
    private int level;

    @Value("${upload.tmp.dir}")
    String tmpDir;

    private int kaptchaCharLength = 4;

    @Value("${token.expire.seconds:1800}")
    private int tokenExpireSeconds;

    @Value("${refreshToken.expire.seconds:1800}")
    private int refreshTokenExpireSeconds;

    @Value("${user.login.max:2}")
    private int userLoginMax;

    @Value("${cookie.salt}")
    private String cookieSalt;

    @Value("${profile.version:full}")
    private String profile;

    @Value("#{'${auth-server.map.bigdata.cookieNames}'.split(',')}")
    private List<String> authServerCookieNames;

    @Value("${auth-server.map.bigdata.cacheExpire:30}")
    private Integer authServerCacheExpire;

    @Value("${auth-server.map.bigdata.loginCacheTime:30}")
    private Integer authServerLoginCacheTime;

    @Value("${auth-server.map.bigdata.domain}")
    private String authServerDomain;

    @Value("${auth-server.map.bigdata.logoutDomain}")
    private String authServerLogoutDomain;

    @Value("${auth-server.aes.secret_key:37ab0a4b2fdebc0fdbe584b14cec3b91}")
    private String aesSecretKey;

//    public static String RSA_PRIVATE_KEY;
    public static String APP_KEY;
//    public static String SM2_PUBLIC_KEY;
//    public static String SM2_PRIVATE_KEY;
//    public static String ENV_VERSION;
//    public static String MENU_TYPE;
    public static Integer LEVEL;
    public static String TMP_DIR;
    public static String APP_SECRET;
    public static String AUTH_SERVER_URL;
    public static Integer USER_LOGIN_MAX;
    public static Integer KAPT_CHACHAR_LENGTH;
    public static Integer TOKEN_EXPIRE_SECONDS;
    public static Integer REFRESH_TOKEN_EXPIRE_SECONDS;
    public static String COOKIE_SALT;
    public static String PROFILE;
    public static List<String> AUTH_SERVER_COOKIENAMES;
    public static String AUTH_SERVER_LOGOUTDOMAIN;
    public static String AUTH_SERVER_DOMAIN;
    public static Integer AUTH_SERVER_CACHE_EXPIRE;
    public static Integer AUTH_SERVER_LOGIN_CACHE_TIME;
    public static String AES_SECRET_KEY;



    @Override
    public void afterPropertiesSet() throws Exception {
//        RSA_PRIVATE_KEY = rsaPrivateKey;
//        SM2_PUBLIC_KEY = sm2PublicKey;
//        SM2_PRIVATE_KEY = sm2PrivateKey;
//        ENV_VERSION = envVersion;
//        MENU_TYPE = menuType;
        LEVEL = level;
        TMP_DIR = tmpDir;
        APP_KEY = appKey;
        APP_SECRET = appSecret;
        AUTH_SERVER_URL = authServerUrl;

        USER_LOGIN_MAX = userLoginMax;
        KAPT_CHACHAR_LENGTH = kaptchaCharLength;
        TOKEN_EXPIRE_SECONDS = tokenExpireSeconds;
        REFRESH_TOKEN_EXPIRE_SECONDS = refreshTokenExpireSeconds;
        COOKIE_SALT = cookieSalt;
        PROFILE = profile;
        AUTH_SERVER_COOKIENAMES = authServerCookieNames;
        AUTH_SERVER_LOGOUTDOMAIN = authServerLogoutDomain;
        AUTH_SERVER_DOMAIN = authServerDomain;
        AUTH_SERVER_CACHE_EXPIRE = authServerCacheExpire;
        AUTH_SERVER_LOGIN_CACHE_TIME = authServerLoginCacheTime;
        AES_SECRET_KEY = aesSecretKey;
    }
}
