package com.ksyun.auth.utils.encrypt;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.DerivationFunction;
import org.bouncycastle.crypto.digests.SHA256Digest;
import org.bouncycastle.crypto.digests.ShortenedDigest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.KDF1BytesGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Arrays;

/**
 * 国密SM2
 * <AUTHOR>
 */
public class SM2Encryption implements Asymmetric {

    public SM2Encryption() {
        curve = new ECCurve.Fp(p, // q
                a, // a
                b); // b
        G = curve.createPoint(xg, yg);
    }

    /** 素数p */
    private static final BigInteger p = new BigInteger("FFFFFFFE" + "FFFFFFFF"
            + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "00000000" + "FFFFFFFF"
            + "FFFFFFFF", 16);

    /** 系数a */
    private static final BigInteger a = new BigInteger("FFFFFFFE" + "FFFFFFFF"
            + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "00000000" + "FFFFFFFF"
            + "FFFFFFFC", 16);

    /** 系数b */
    private static final BigInteger b = new BigInteger("28E9FA9E" + "9D9F5E34"
            + "4D5A9E4B" + "CF6509A7" + "F39789F5" + "15AB8F92" + "DDBCBD41"
            + "4D940E93", 16);

    /** 坐标x */
    private static final BigInteger xg = new BigInteger("32C4AE2C" + "1F198119"
            + "5F990446" + "6A39C994" + "8FE30BBF" + "F2660BE1" + "715A4589"
            + "334C74C7", 16);

    /** 坐标y */
    private static final BigInteger yg = new BigInteger("BC3736A2" + "F4F6779C"
            + "59BDCEE3" + "6B692153" + "D0A9877C" + "C62A4740" + "02DF32E5"
            + "2139F0A0", 16);

    /** 基点G, G=(xg,yg),其介记为n */
    private static final BigInteger n = new BigInteger("FFFFFFFE" + "FFFFFFFF"
            + "FFFFFFFF" + "FFFFFFFF" + "7203DF6B" + "21C6052B" + "53BBF409"
            + "39D54123", 16);

    private static SecureRandom random = new SecureRandom();
    private ECCurve.Fp curve;
    private ECPoint G;

    @Override
    public SecretKey initKey() {
        BigInteger d = random(n.subtract(new BigInteger("1")));
        ECPoint normalize = G.multiply(d).normalize();
        byte[] publicKey = normalize.getEncoded(false);
        byte[] privateKey = d.toByteArray();

        return new SecretKey(Base64.encodeBase64String(publicKey),
                Base64.encodeBase64String(privateKey));
    }

    @Override
    public String encryptByPublicKey(String data, String key) {
        byte[] bytes = Base64.decodeBase64(key);
        ECPoint publicKey = curve.decodePoint(bytes);
        byte[] inputBuffer = data.getBytes();
        printHexString(inputBuffer);

        /* 1 产生随机数k，k属于[1, n-1] */
        BigInteger k = random(n);
        System.out.print("k: ");
        printHexString(k.toByteArray());

        /* 2 计算椭圆曲线点C1 = [k]G = (x1, y1) */
        ECPoint C1 = G.multiply(k);
        byte[] C1Buffer = C1.getEncoded(false);
        System.out.print("C1: ");
        printHexString(C1Buffer);

        // 3 计算椭圆曲线点 S = [h]Pb * curve没有指定余因子，h为空

        //           BigInteger h = curve.getCofactor(); System.out.print("h: ");
        //           printHexString(h.toByteArray()); if (publicKey != null) { ECPoint
        //           result = publicKey.multiply(h); if (!result.isInfinity()) {
        //           System.out.println("pass"); } else {
        //          System.err.println("计算椭圆曲线点 S = [h]Pb失败"); return null; } }

        /* 4 计算 [k]PB = (x2, y2) */
        ECPoint kpb = publicKey.multiply(k).normalize();

        /* 5 计算 t = KDF(x2||y2, klen) */
        byte[] kpbBytes = kpb.getEncoded(false);
        DerivationFunction kdf = new KDF1BytesGenerator(new ShortenedDigest(
                new SHA256Digest(), 20));
        byte[] t = new byte[inputBuffer.length];
        kdf.init(new ISO18033KDFParameters(kpbBytes));
        kdf.generateBytes(t, 0, t.length);

        if (allZero(t)) {
            System.err.println("all zero");
        }

        /* 6 计算C2=M^t */
        byte[] C2 = new byte[inputBuffer.length];
        for (int i = 0; i < inputBuffer.length; i++) {
            C2[i] = (byte) (inputBuffer[i] ^ t[i]);
        }

        /* 7 计算C3 = Hash(x2 || M || y2) */
        byte[] C3 = calculateHash(kpb.getXCoord().toBigInteger(), inputBuffer,
                kpb.getYCoord().toBigInteger());

        /* 8 输出密文 C=C1 || C2 || C3 */
        byte[] encryptResult = new byte[C1Buffer.length + C2.length + C3.length];
        System.arraycopy(C1Buffer, 0, encryptResult, 0, C1Buffer.length);
        System.arraycopy(C2, 0, encryptResult, C1Buffer.length, C2.length);
        System.arraycopy(C3, 0, encryptResult, C1Buffer.length + C2.length,
                C3.length);

        System.out.print("密文: ");
        printHexString(encryptResult);

        return Base64.encodeBase64String(encryptResult);
    }

    @Override
    public String decryptByPrivateKey(String content, String key) {
        byte[] bytes = Base64.decodeBase64(key);
        byte[] encryptData = Base64.decodeBase64(content);
        BigInteger privateKey = new BigInteger(bytes);

        byte[] C1Byte = new byte[65];
        System.arraycopy(encryptData, 0, C1Byte, 0, C1Byte.length);

        ECPoint C1 = curve.decodePoint(C1Byte).normalize();

        /* 计算[dB]C1 = (x2, y2) */
        ECPoint dBC1 = C1.multiply(privateKey).normalize();

        /* 计算t = KDF(x2 || y2, klen) */
        byte[] dBC1Bytes = dBC1.getEncoded(false);
        DerivationFunction kdf = new KDF1BytesGenerator(new ShortenedDigest(
                new SHA256Digest(), 20));

        int klen = encryptData.length - 65 - 20;
        System.out.println("klen = " + klen);

        byte[] t = new byte[klen];
        kdf.init(new ISO18033KDFParameters(dBC1Bytes));
        kdf.generateBytes(t, 0, t.length);

        if (allZero(t)) {
            System.err.println("all zero");
        }

        /* 5 计算M'=C2^t */
        byte[] M = new byte[klen];
        for (int i = 0; i < M.length; i++) {
            M[i] = (byte) (encryptData[C1Byte.length + i] ^ t[i]);
        }

        /* 6 计算 u = Hash(x2 || M' || y2) 判断 u == C3是否成立 */
        byte[] C3 = new byte[20];
        System.arraycopy(encryptData, encryptData.length - 20, C3, 0, 20);
        byte[] u = calculateHash(dBC1.getXCoord().toBigInteger(), M, dBC1
                .getYCoord().toBigInteger());
        if (Arrays.equals(u, C3)) {
            return new String(M);
        } else {
            printHexString(u);
            printHexString(C3);
            System.err.println("解密验证失败");
        }
        return null;
    }

//    class SM2KeyPair {
//
//        /** 公钥 */
//        private  ECPoint publicKey;
//
//        /** 私钥 */
//        private BigInteger privateKey;
//
//    }

    private BigInteger random(BigInteger max) {
        BigInteger r = new BigInteger(256, random);
        // int count = 1;
        while (r.compareTo(max) >= 0) {
            r = new BigInteger(128, random);
            // count++;
        }
        // System.out.println("count: " + count);
        return r;
    }

    private boolean allZero(byte[] buffer) {
        for (int i = 0; i < buffer.length; i++) {
            if (buffer[i] != 0)
                return false;
        }
        return true;
    }

    private boolean between(BigInteger param, BigInteger min, BigInteger max) {
        if (param.compareTo(min) >= 0 && param.compareTo(max) < 0) {
            return true;
        } else {
            return false;
        }
    }

    private byte[] calculateHash(BigInteger x2, byte[] M, BigInteger y2) {
        ShortenedDigest digest = new ShortenedDigest(new SHA256Digest(), 20);
        byte[] buf = x2.toByteArray();
        digest.update(buf, 0, buf.length);
        digest.update(M, 0, M.length);
        buf = y2.toByteArray();
        digest.update(buf, 0, buf.length);

        buf = new byte[20];
        digest.doFinal(buf, 0);
        return buf;
    }

    private static String printHexString(byte[] b) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < b.length; i++) {
            String hex = Integer.toHexString(b[i] & 0xFF);
            if (hex.length() == 1) {
                builder.append('0'+hex);
                hex = '0' + hex;
            }
            //          System.out.print(hex.toUpperCase());
            System.out.print(hex.toUpperCase());
            builder.append(hex);
        }
        System.out.println();
        return builder.toString();
    }




    /**
     * 获得公私钥对
     * @return
     */
    /*public SM2KeyPair generateKeyPair() {
        BigInteger d = random(n.subtract(new BigInteger("1")));
        SM2KeyPair keyPair = new SM2KeyPair(G.multiply(d).normalize(), d);
        if (checkPublicKey(keyPair.getPublicKey())) {
            System.out.println("generate key successfully");
            return keyPair;
        } else {
            System.err.println("generate key failed");
            return null;
        }
    }*/

//    /**
//     * 公钥校验
//     * @param publicKey 公钥
//     * @return boolean true或false
//     */
//    private boolean checkPublicKey(ECPoint publicKey) {
//        if (!publicKey.isInfinity()) {
//            BigInteger x = publicKey.getXCoord().toBigInteger();
//            BigInteger y = publicKey.getYCoord().toBigInteger();
//            if (between(x, new BigInteger("0"), p) && between(y, new BigInteger("0"), p)) {
//                BigInteger xResult = x.pow(3).add(a.multiply(x)).add(b).mod(p);
//                System.out.println("xResult: " + xResult.toString());
//                BigInteger yResult = y.pow(2).mod(p);
//                System.out.println("yResult: " + yResult.toString());
//                if (yResult.equals(xResult) && publicKey.multiply(n).isInfinity()) {
//                    return true;
//                }
//            }
//            return false;
//        } else {
//            return false;
//        }
//    }
//
//
//
//
    public static final String encrypt(String data, String publicKey) {
        if (StringUtils.isEmpty(data) || StringUtils.isEmpty(publicKey)) {
            return null;
        }
        try {
            X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
            ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
            //提取公钥点
            ECPoint pukPoint = sm2ECParameters.getCurve().decodePoint(Hex.decode(publicKey));
            ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);

            byte[] in = data.getBytes("utf-8");
            SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
            return Hex.toHexString(sm2Engine.processBlock(in, 0, in.length));
        } catch (Exception e) {
            return null;
        }
    }

    public static final String decrypt(String cipherData, String privateKey) {
        if (StringUtils.isEmpty(cipherData) || StringUtils.isEmpty(privateKey)) {
            return null;
        }
        try {
            byte[] cipherDataByte = Hex.decode(cipherData);
            X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
            ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
            ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(new BigInteger(privateKey, 16), domainParameters);

            //用私钥解密
            SM2Engine sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            sm2Engine.init(false, privateKeyParameters);

            return new String(sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length));
        } catch (Exception e) {
            return null;
        }
    }
//
//    public static void main(String[] args) throws UnsupportedEncodingException, UnsupportedEncodingException {
//
////        SM2Encryption sm2 = new SM2Encryption();
////
////        System.out.println("--------3---------");
////        SecretKey secretKey = sm2.initKey();
////        String s = "BH1gOIBAUi56vcow2MJGrKZISdrzpPP3PaGA+DzkRMgyWDJr/px6nV7B2a+XO+GQdAUkbghNIFfIYxi8SxcnlqV7EK1caF+yD9Ab3aw3eMY+vpyc4e+s8KlChXvUdJew2OVuvZ6kkMdxJ/G0g9kSipRRL0iAjUldiF7Jr7C9ruRfZAp+xneutGWs/V79yAbXdGjeOH+AYFJ/3EpzM+wE2RySRtqriDQ15Y7OKyL3q6Cp3roXGb56LshxoggKdvsM7c0AUePSHFHx7s7J9hyXJIEnq4BCDaJt1m/cVJ91d7nysNVA9hx0f94qG1mRfkZ7ZiId7k1M7/NKsSevBeAzrdBYnEI0nLY8Vh02Kj6ZnwlJ3vBGKJcCSi67+z8=";
////
////        String s1 = sm2.decryptByPrivateKey(s, "AO6rT9A2QH1gSyvyIqCpxA3FWtbr1ayQzMf3NW4JvGhsy");
////        System.out.println(s1);
//
//        String publicKey = "BIBoSoBOH0ttu1RPNYI2j7ds7JD4AYnEBM0S+VsI1W9yuZa2c1duI+nCgdD0vg9E1YRnD/dFz8oiaJaXa33Xf8M=";
//        String privateKey = "AM+lu5+QZ/Xq4nWvxhLdXLjlsDcKfqYATRkmxMHc0Ea1";
//
//        // SM2[SM3(口令)+随机数+(口令)】
//        SM2Encryption sm2 = new SM2Encryption();
//        String password = "Kingsoft.com123";
//
//
//
//
//        byte[] md = new byte[32];
//        byte[] msg1 = password.getBytes("UTF-8");
//        SM3Digest sm3 = new SM3Digest();
//        sm3.update(msg1, 0, msg1.length);
//        sm3.doFinal(md, 0);
//        String passwordSM3 = new String(Hex.encode(md));
//
//
//        String encryptSm2 = sm2.encryptByPublicKey(passwordSM3 + "12345" + password, publicKey);
//        String decryptSm2 = sm2.decryptByPrivateKey(encryptSm2, privateKey);
//        System.out.println("encryptSm2=" + encryptSm2);
//        System.out.println("decryptSm2=" + decryptSm2);
//
//
//
////        BigInteger d = sm2.random(n.subtract(new BigInteger("13248230949273")));
////        ECPoint normalize = sm2.G.multiply(d).normalize();
////        byte[] publicKey = normalize.getEncoded(false);
////        byte[] privateKey = d.toByteArray();
////        System.out.println("publicKey = " + Base64.encodeBase64String(publicKey));
////        System.out.println("privateKey = " + Base64.encodeBase64String(privateKey));
//
//    }
}
