package com.ksyun.auth.utils.encrypt;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;

public class AESEncryption {
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS7Padding";
    
    static {
        // Register Bouncy Castle provider
        Security.addProvider(new BouncyCastleProvider());
    }

    public String encrypt(String data, String key) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION, "BC");
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("Error encrypting data", e);
        }
    }

    public String decrypt(String encryptedData, String key) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION, "BC");
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("Error decrypting data", e);
        }
    }

    public static void main(String[] args) {
        AESEncryption aes = new AESEncryption();
        String key = "37ab0a4b2fdebc0fdbe584b14cec3b91";
        
        // Test encryption
        String originalText = "Hello123!@#";
        System.out.println("Original Text: " + originalText);
        
        // Encrypt
        String encrypted = aes.encrypt(originalText, key);
        System.out.println("Encrypted Text: " + encrypted);
        
        // Decrypt
        String decrypted = aes.decrypt(encrypted, key);
        System.out.println("Decrypted Text: " + decrypted);
        
        // Verify
        System.out.println("Verification: " + originalText.equals(decrypted));
    }
}
