package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.Set;

/**
 * 权限分组添加
 */
@Setter
@Getter
public class PermissionGroupOpenCloseVo {
    @NotNull(message="id不能为空")
    private Long id;
    /**
     * status NORMAL 公开  CLOSE 未公开 DELETED 删除
     */
    @NotEmpty(message = "status不能为空")
    private String status;
}
