package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@Setter
@Getter
public class TenantUpdateVo {
    @NotNull(message = "用户ID不能为空")
    private Long id;
    @Pattern(regexp = "^$|((1[3|4|5|7|8]))\\d{9}$", message = "手机号码格式错误")
    protected String phone;
    @Pattern(regexp = "^$|([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$", message = "邮箱码格式错误")
    protected String email;
}
