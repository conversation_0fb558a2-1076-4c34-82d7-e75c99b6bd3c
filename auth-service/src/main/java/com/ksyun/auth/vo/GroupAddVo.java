package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX_MESSAGE;

/**
 * 创建分组请求实体
 */
@Setter
@Getter
public class GroupAddVo extends BasicGroupVo {
    @NotNull(message = "名称不能为空")
    @NotEmpty(message = "名称不能为空")
    @Pattern(regexp = VALIDATE_NAME_REGEX, message = VALIDATE_NAME_REGEX_MESSAGE)
    private String name;
    private Long createBy;
    private Long updateBy;
}
