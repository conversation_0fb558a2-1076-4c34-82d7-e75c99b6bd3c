package com.ksyun.auth.vo;

/**
 * <AUTHOR>
 * @create 2021/12/7 3:58 下午
 */

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色
 */
@Data
public class RoleTagsVo {
    private Long id;
    /**
     * 标签名称
     */
    private String name;
    /**
     * 标签分组
     * 角色的标签，同一组最多1个
     */
    private String group;

    /**
     * 标识key
     */
    private String key;
    /**
     * 适用角色类型
     * 适用范围 0：所有，1：平台角色 2：项目角色
     */
    private Integer roleTypeApply;
}
