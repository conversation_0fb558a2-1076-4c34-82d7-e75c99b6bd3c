package com.ksyun.auth.vo;


import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;

import static com.ksyun.common.constant.Constants.VALIDATE_CODE_REGEX;
import static com.ksyun.common.constant.Constants.VALIDATE_CODE_REGEX_MESSAGE;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX_MESSAGE;

@Setter
@Getter
public class PrivilegeUpdateVo {

    private Long privilegeId;

    @Pattern(regexp = VALIDATE_CODE_REGEX, message = VALIDATE_CODE_REGEX_MESSAGE)
    @NotEmpty(message = "编码不能为空")
    private String code;

    @Pattern(regexp = VALIDATE_NAME_REGEX, message = VALIDATE_NAME_REGEX_MESSAGE)
    @NotEmpty(message = "名称不能为空")
    private String name;

    @NotNull(message = "类型不能为空")
    private Integer type;

    @Pattern(regexp = "^.{0,255}$", message = "url不能超过255")
    private String url;

    private String icon;

    private int order;

    @NotEmpty(message = "应用不能为空")
    private String ak;

    private Long parentId;

    private List<PrivilegePropsVo> menuProps;
}

