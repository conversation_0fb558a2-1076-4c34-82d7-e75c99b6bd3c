package com.ksyun.auth.vo;

import jodd.util.StringUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色
 */
@Setter
@Getter
public class RoleVo {
    public static final String TAG_ID_SEPERATOR = ";";
    private Long id;
    private String code;
    private String name;
    private String description;
    private Integer type;
    private String tagIds;
    private String createTime;
    private String updateTime;
    private String createBy;
    private int isDefault = 0;
    private String source;
    private List<String> tags;

    public static String serializeTagIds(Set<Long> tagIds) {
        if (tagIds == null || tagIds.size() == 0) {
            return null;
        }
        String str = tagIds.stream().map(String::valueOf).collect(Collectors.joining(RoleVo.TAG_ID_SEPERATOR));
        return str;
    }

    public static long[] deserializeTagIds(String tagIds) {
        if (StringUtil.isEmpty(tagIds)) {
            return new long[0];
        }
        String[] items = tagIds.split(RoleVo.TAG_ID_SEPERATOR);
        return Arrays.stream(items).mapToLong(Long::parseLong).toArray();
    }

    public boolean hasTag(int tagId) {
        long[] items = deserializeTagIds(tagIds);
        return Arrays.stream(items).anyMatch(item -> item == tagId);
    }

    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type=" + type +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
