package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
public class IAASAkSkAddVo {
    @NotEmpty(message = "AK不能为空")
    private String ak;
    @NotEmpty(message = "SK不能为空")
    private String sk;
    @Null(message = "用户ID后台从Cookie中获取")
    private Long userId;
    @NotEmpty(message = "appId不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{1,255}$", message = "appId只能是数字或字符,长度在1-255之间")
    private String appId;
}
