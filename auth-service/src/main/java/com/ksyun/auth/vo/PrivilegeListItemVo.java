package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class PrivilegeListItemVo {
    /**
     * 权限id
     */
    private Long id;
    /**
     * 权限名称
     */
    private String name;
    /**
     * 权限编码
     */
    private String code;
    /**
     * url
     */
    private String url;
    /**
     * 权限类型，菜单or按钮
     */
    private int type;
    /**
     * icon
     */
    private String icon;
    /**
     * 菜单属性
     */
    private List<String> props;

    /**
     * 应用
     */
    private String ak;

    /**
     * 父级
     */
    private Long parentId;

    /**
     *  子级节点
     */
    private List<PrivilegeListItemVo> subNodes;
}