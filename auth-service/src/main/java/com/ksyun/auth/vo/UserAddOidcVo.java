package com.ksyun.auth.vo;

import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.exception.Assert;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 创建用户参数，这里创建的只能是子帐户，由租户或具有创建子帐号权限的用户创建
 */
@Data
public class UserAddOidcVo{

    private String oidcId;

    private String loginName;

    protected String phone;

    protected String email;

    private String cnName;

    private String alias;

    private String roleName;


}
