package com.ksyun.auth.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ksyun.auth.dto.InnerGroupDto;
import com.ksyun.auth.dto.InnerTenantDto;
import com.ksyun.common.enums.UserSourceEnum;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.*;

@Data
public class UserVo extends BasicUserVo {
    public enum Status {NORMAL, SLEEP, LOGOUT, DELETED}
    private Long id;
    private String name;
    private Status status = Status.NORMAL;
    private Date createTime;
    private Date updateTime;
    private UserSourceEnum source;
    //private InnerTenantDto tenant;
    private List<PropVo> props = new LinkedList<>();
    private int secretLevel;
    @JsonIgnore
    private List<InnerGroupDto> groups = new LinkedList<>();
    //private boolean tenantFlag = true;
    private String password;
    protected String phone;
    protected String email;
    private UserAddVo.DEADLINE deadline = UserAddVo.DEADLINE.LONGTERM;
    private Integer deadlineValidDay;
    //private Long userId;
    @NotNull(message = "角色不能为空")
    private Set<Long> roleIds = new HashSet<>();
    private String remark;
}
