package com.ksyun.auth.vo;

import lombok.Data;
import org.springframework.util.Assert;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Null;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 绑定/解绑用户分组时的批操作对象
 */
@Data
public class UserGroupBindVo {
    @NotEmpty(message = "用户不能为空")
    private Set<Long> userIds = new HashSet<>();
    @NotEmpty(message = "分组不能为空")
    private Set<Long> groupIds = new HashSet<>();
    @Null(message = "租户信息，后台从登录信息中获取")
    private Long tenantId;
    private Long groupId;
    private Long userId;

    public static UserGroupPairVo newUserGroupPair(Long userId, Long groupId) {
        UserGroupPairVo pair = new UserGroupPairVo();
        pair.setUserId(userId);
        pair.setGroupId(groupId);
        return pair;
    }

    public static List<UserGroupPairVo> newUserGroupPairs(Collection<Long> userIds, Set<Long> groupIds) {
        List<UserGroupPairVo> pairs = new ArrayList<>();
        for (Long userId : userIds) {
            pairs.addAll(newUserGroupPairsByUserId(userId, groupIds));
        }
        return pairs;
    }

    public static Set<UserGroupPairVo> newUserGroupPairsByUserId(Long userId, Set<Long> groupIds) {
        Assert.isTrue(!groupIds.isEmpty(), "Groups Cannot Be Empty");
        Set<UserGroupPairVo> pairs = groupIds.stream().map(groupId -> newUserGroupPair(userId, groupId)).collect(Collectors.toSet());
        return pairs;
    }
}
