package com.ksyun.auth.vo;

import com.ksyun.common.entity.PermissionGroup;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 分类添加
 */
@Data
public class CategoryVo {
    private Long id;
    private String name;
    private String description;
    List<PermissionGroup> permissionGroups = new ArrayList<>();
    private List<PermissionGroupVo> permissionGroupList;
}
