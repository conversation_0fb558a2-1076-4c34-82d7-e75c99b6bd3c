package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 权限查询
 */
@Setter
@Getter
public class PrivilegeQueryVo {
    private Long id;
    private String code;
    private String name;
    private Long parentId;
    private Long projectId;
    private List<PropVo> props = new ArrayList<>();
    private Set<String> businesses = new HashSet<>();
    private Set<Long> roleIds = new HashSet<>();
    private Integer readonly;
    private boolean tenant = false;
    private Long userId;
    private String productCode;
    /**
     * 权限类型，菜单or按钮
     */
    private Integer type;

    /**
     * kcde dls
     */
    private String from;
    /**
     * 多网区分
     */
//    private String networkTag;
}
