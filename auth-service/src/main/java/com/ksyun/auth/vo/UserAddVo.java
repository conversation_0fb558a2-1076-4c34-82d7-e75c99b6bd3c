package com.ksyun.auth.vo;

import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.exception.Assert;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.Set;

/**
 * 创建用户参数，这里创建的只能是子帐户，由租户或具有创建子帐号权限的用户创建
 */
@Data
public class UserAddVo extends BasicUserVo {

    enum DEADLINE {LONGTERM, TEMP}

    @Size(max = 128, message = "用户名长度在[1~128]之间")
    @NotEmpty(message = "用户名不能为空")
    private String name;

    @NotEmpty(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,32}$",message = "密码长度在[8-32]之间，并且必须包含数字、大写字母、小写字母")
    private String password;

    protected String phone;

    protected String email;

    /**
     * 备注
     */
    protected String remark;
    /**
     * 用户密级
     */
    private int secretLevel;

    /**
     * 子账号首次登陆时，是否重置密码,默认值是false
     */
    private boolean resetPwdWhenFirstLogin = false;

    private DEADLINE deadline = DEADLINE.LONGTERM;

    private Integer deadlineValidDay;

    @NotNull(message = "角色不能为空")
    private Set<Long> roleIds;

//    @Override
//    public Long getTenantId() {
//        Assert.isTrue(tenantUser != null, BusinessExceptionEnum.USER_NOT_LOGIN);
//        Assert.isTrue(tenantUser.getTenant() != null && tenantUser.getTenant().getId() != null, BusinessExceptionEnum.PARAM_INVALID);
//        return tenantUser.getTenant().getId();
//    }


}
