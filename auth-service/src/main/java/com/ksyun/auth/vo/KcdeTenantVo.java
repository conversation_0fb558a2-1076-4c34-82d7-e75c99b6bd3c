package com.ksyun.auth.vo;

import com.ksyun.common.entity.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.*;

@Data
public class KcdeTenantVo {
//    public KcdeTenantVo(Tenant tenant, List<UserTenant> tenantUsers) {
//        id = tenant.getId();
//        name = tenant.getName();
//        status = tenant.getStatus();
//        createTime = tenant.getCreateTime();
//        updateTime = tenant.getUpdateTime();
//        userIds = new ArrayList<>();
//        remark = tenant.getRemark();
//        description = tenant.getDescription();
//        if (tenantUsers != null) {
//            for (UserTenant u : tenantUsers) {
//                userIds.add(u.getUserId());
//            }
//        }
//    }

    private Long id;

    private String name;

    private String status;

//    private List<Long> userIds;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String remark;

    private String description;

    private User createdBy;

    private Set<User> users = new HashSet<>();
}
