package com.ksyun.auth.vo;

import com.ksyun.common.enums.AkSkStatusEnum;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;

/**
 * 用户AkSk修改实体
 **/
@Setter
@Getter
public class UserAkSkVo {

    @NotNull(message = "用户编号不能为空")
    private Long userId;
    @NotNull(message = "用户AkSk状态不能为空")
    private AkSkStatusEnum userAkSkStatus;
    private String userAk;
    private String userSk;
}
