package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/12 14:09
 */
@Setter
@Getter
public class CheckPrivilegeResultVo {
    private boolean passed;
    private String message;

    public CheckPrivilegeResultVo(boolean passed, String message) {
        this.passed = passed;
        this.message = message;
    }

    public static CheckPrivilegeResultVo fail(String message) {
        return new CheckPrivilegeResultVo(false, message);
    }

    public static CheckPrivilegeResultVo success() {
        return new CheckPrivilegeResultVo(true, "");
    }
}