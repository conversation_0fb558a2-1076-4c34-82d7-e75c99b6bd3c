package com.ksyun.auth.vo;

import com.ksyun.common.enums.SystemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.HashSet;
import java.util.Set;

import static com.ksyun.common.constant.Constants.VALIDATE_CODE_REGEX;
import static com.ksyun.common.constant.Constants.VALIDATE_CODE_REGEX_MESSAGE;
import static com.ksyun.common.constant.Constants.VALIDATE_DESC_MESSAGE;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX_MESSAGE;

@Setter
@Getter
public class RoleAddVo {

    @NotNull(message = "编码不能为空")
    @NotBlank(message = "编码不能为空")
    @Pattern(regexp = VALIDATE_CODE_REGEX, message = VALIDATE_CODE_REGEX_MESSAGE)
    private String code;
    @NotNull(message = "名称不能为空")
    @NotBlank(message = "名称不能为空")
    @Pattern(regexp = VALIDATE_NAME_REGEX, message = VALIDATE_NAME_REGEX_MESSAGE)
    private String name;
    private String source = SystemTypeEnum.BIGDATA.getCode();

    /**
     * 角色类型
     */
    private int type = 1;

    /**
     * 角色标签
     */
    private Set<Long> tagIds = new HashSet<>();

    /**
     * 角色描述
     */
    @Size(max = 500, message = VALIDATE_DESC_MESSAGE)
    private String description;

    //默认 运营平台创建
    private int isDefault = 0;

}

