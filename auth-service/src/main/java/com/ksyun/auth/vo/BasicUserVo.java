package com.ksyun.auth.vo;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.common.enums.UserSourceEnum;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
public class BasicUserVo {
    // 租户信息从登录信息中获取，前台勿设
    // protected AuthUser tenantUser;
    // 用户来源，默认为LOCAL 代表由KCDE本身创建的用户 其他也包括 HH、CAS等
    protected UserSourceEnum source = UserSourceEnum.LOCAL;
    // 用户分组ID，创建用户时，可以不填分组
    protected Set<Long> groupIds = new HashSet<>();

//    @NotEmpty(message = "用户显示名不能为空")
    protected String alias;

    @Pattern(regexp = "^$|((1[3|4|5|7|8]))\\d{9}$", message = "手机号码格式错误")
    protected String phone;

    @Pattern(regexp = "^$|([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\." +
            "([A-Za-z]{2,4})$", message = "邮箱码格式错误")
    protected String email;

//    public Long getTenantId() {
//        if (tenantUser != null && tenantUser.getTenant() != null) {
//            return tenantUser.getTenant().getId();
//        }
//        return null;
//    }

}
