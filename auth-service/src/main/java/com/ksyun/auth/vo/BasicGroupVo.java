package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Size;

import static com.ksyun.common.constant.Constants.VALIDATE_DESC_MESSAGE;

@Setter
@Getter
public class BasicGroupVo {
    @Null(message = "租户信息，后台从登录信息中获取")
    protected Long tenantId;
    @Size(max = 500, message = VALIDATE_DESC_MESSAGE)
    protected String description;
}
