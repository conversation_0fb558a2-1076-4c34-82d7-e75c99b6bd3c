package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.Min;

/**
 * 分页参数
 */
@Setter
@Getter
public class PageVo {
    private int pageNo = 1;
    private int pageSize = 10;
    private boolean pageGlobal = true;
    /**
     * 当前页
     */
    @Min(value = 1, message = "{min.page}")
    private int page = 1;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 总记录数
     */
    private long totalRecord;

    public long getTotalPage() {
        if (getPageSize() == 0) {
            return 0L;
        }
        long pages = getTotalRecord() / getPageSize();
        if (getTotalRecord() % getPageSize() != 0) {
            pages++;
        }
        return pages;
    }
}
