package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;

@Setter
@Getter
public class SubAccountPasswordResetVo {
    // 租户信息从登录信息中获取
//    private Long tenantId;
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @NotEmpty(message = "新密码不能为空")
    @Pattern(regexp = "(^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,32}$)", message = "密码长度在[8-32]之间，并且必须包含数字、大写字母、小写字母")
    private String newPassword;
    @NotEmpty(message = "确认密码不能为空")
    @Pattern(regexp = "(^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,32}$)", message = "密码长度在[8-32]之间，并且必须包含数字、大写字母、小写字母")
    private String confirmPassword;
}
