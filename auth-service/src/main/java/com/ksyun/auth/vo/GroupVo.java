package com.ksyun.auth.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Setter
@Getter
public class GroupVo {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String description;
    private String createTime;
    private String updateTime;
    @JsonIgnore
    private Long createBy;
    @JsonIgnore
    private Long updateBy;
    private Set<RoleVo> roles = new HashSet<>();
    private Set<Long> userIds = new HashSet<>();
}
