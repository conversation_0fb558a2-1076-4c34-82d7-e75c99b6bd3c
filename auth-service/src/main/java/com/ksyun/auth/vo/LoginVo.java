package com.ksyun.auth.vo;

import com.ksyun.common.constant.Constants;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;

/**
 * 用户登录信息
 */
@Data
public class LoginVo {

    protected int loginType = Constants.LOGIN_TYPE_TENANT;
    @NotEmpty(message = "用户名称不能为空")
    protected String username;
    @NotEmpty(message = "用户密码不能为空")
    protected String password;
    protected String code;

    public LoginVo() {
        this.loginType = Constants.LOGIN_TYPE_TENANT;
    }

}
