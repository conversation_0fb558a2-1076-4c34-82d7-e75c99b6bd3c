package com.ksyun.auth.vo;


import com.ksyun.common.enums.UserSourceEnum;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户查询条件
 */
@Data
public class UserQueryVo extends PageVo {
    private boolean isTenant;
    private String name;
    private String alias;
    private String phone;
    private String email;
    private Set<String> status = new HashSet<>();
    //private Long tenantId;
    private String pageType;
    private UserSourceEnum source;
    private String deadline;
    private String orderParameter;
    private Set<Long> userIds = new HashSet<>();
    private Set<Long> groupIds = new HashSet<>();
    private Set<Long> roleIds = new HashSet<>();
    private Set<Long> noRoleIds = new HashSet<>();
    private Set<String> roleTags = new HashSet<>();
    private Set<String> roleNames = new HashSet<>();
    //private boolean isNotDisplayTenant = true;
    private boolean includeDeletedUsers = false;
    private boolean userBindRole = true;//默认是true，查询与指定角色绑定的用户；当需要查询与指定角色没有绑定的用户时，设置false
    private boolean userInGroup = true;//默认是true，查询在指定分组里的用户；当需要查询不在指定分组的用户时，设置false
    private Integer secretLevel;// 用户密级
    private Long projectId;//项目Id
    private boolean onlyShowUsersBindToRole = false;// 默认是false，查询与某个角色相关的用户，包括用户与角色绑定和分组与角色绑定；当只需要查询用户与角色绑定时，设置true

    // 是否脱敏显示
    private String dataMask;

//    public boolean isNotDisplayTenant() {
//        return isNotDisplayTenant;
//    }
//
//    public void setIsNotDisplayTenant(boolean notDisplayTenant) {
//        isNotDisplayTenant = notDisplayTenant;
//    }

}


