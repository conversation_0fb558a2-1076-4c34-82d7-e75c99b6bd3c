package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户分组查询对象
 */
@Setter
@Getter
public class GroupQueryVo extends PageVo {
    private Long id;
    private String name;  // 分组名称，模糊匹配
    private Long tenantId;
    private String description;  // 分组描述，模糊匹配
    private Set<Long> groupIds = new HashSet<>(); // 分组ID
    private Set<Long> roleIds = new HashSet<>();
    private boolean roleBindGroup = true;////默认是true，查询与指定角色绑定的分组；当需要查询没有与指定角色绑定的分组时，设置false
}
