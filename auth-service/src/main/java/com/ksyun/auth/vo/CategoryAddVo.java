package com.ksyun.auth.vo;

import com.ksyun.common.constant.Constants;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.Date;

/**
 * 分类添加
 */
@Setter
@Getter
public class CategoryAddVo {
    private Long id;
    @Pattern(regexp = Constants.VALIDATE_NAME_REGEX, message = Constants.VALIDATE_NAME_REGEX_MESSAGE)
    @NotEmpty(message = "名称不能为空")
    private String name;
    private String description;
    private Date createTime;
    private Date updateTime;
}
