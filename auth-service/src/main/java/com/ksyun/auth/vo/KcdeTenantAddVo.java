package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.Pattern;
import java.util.List;

@Setter
@Getter
public class KcdeTenantAddVo {
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,64}$", message = "租户名长度在[4~64]之间，以字母开头，可使用数字、字母和下划线。")
    private String name;

    private List<Long> userIds;

    private String remark;

    private String description;

    private Long createdBy;
}
