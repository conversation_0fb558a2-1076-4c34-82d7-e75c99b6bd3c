package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Mail <EMAIL>
 * @Date 2019-03-30 09:27
 * 应用级AkSk创建请求参数
 **/
@Getter
@Setter
public class AddAppAkSkVo {
    @Null(message = "AppSk 由后台生成，请勿填写")
    private String appSk;
    @Null(message = "创建人ID由后台从请求中获取")
    private Long createBy;
    @NotEmpty(message = "AppAK 不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9-]{1,50}$", message = "AppAk 只能由数字字符下划线组成，长度[6~50]之间")
    private String appAk;
    @NotEmpty(message = "appName不能为空")
    private String appName;
}
