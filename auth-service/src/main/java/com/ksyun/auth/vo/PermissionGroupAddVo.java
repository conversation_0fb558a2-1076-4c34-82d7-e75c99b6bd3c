package com.ksyun.auth.vo;

import com.ksyun.common.enums.PermissionStatusEnum;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import static com.ksyun.common.constant.Constants.VALIDATE_DESC_MESSAGE;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX;
import static com.ksyun.common.constant.Constants.VALIDATE_NAME_REGEX_MESSAGE;

/**
 * 权限分组添加
 */
@Setter
@Getter
public class PermissionGroupAddVo {
    private Long id;
    @NotNull(message = "名称不能为空")
    @NotBlank(message = "名称不能为空")
    @Pattern(regexp = VALIDATE_NAME_REGEX, message = VALIDATE_NAME_REGEX_MESSAGE)
    private String name;
    private Long categoryId;
    @Size(max = 500, message = VALIDATE_DESC_MESSAGE)
    private String description;
    private String createBy;
    private PermissionStatusEnum status = PermissionStatusEnum.NORMAL;
    private Date createTime;
    private Date updateTime;
    private Set<Long> privilegeIds = new HashSet<>();
}
