package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;

@Setter
@Getter
public class TenantAddVo {
    @Pattern(regexp = "^[a-zA-Z0-9_]{6,18}$", message = "用户名长度在[6~18]之间，以字母开头，可使用数字、字母和下划线。")
    private String name;

    @NotEmpty(message = "密码不能为空。")
    @Pattern(regexp = "^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,32}$", message = "密码长度在[8-32]之间，必须包含数字、大写字母、小写字母。")
    private String password;

    @Pattern(regexp = "^$|((1[3|4|5|7|8]))\\d{9}$", message = "手机号码格式错误")
    private String phone;

    @Pattern(regexp = "^$|([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$", message = "邮箱码格式错误")
    private String email;

    private int secretLevel = 1;// 租户的密级等级设置成最高

}
