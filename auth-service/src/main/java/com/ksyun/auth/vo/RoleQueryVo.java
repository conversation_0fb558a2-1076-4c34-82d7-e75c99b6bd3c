package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
public class RoleQueryVo extends PageVo {
    private Long tenantId;
    private String code;
    private Set<Long> roleIds = new HashSet<>();
    /**
     * 角色标签, 任何一个标签匹配上即可
     */
    private Set<String> roleTags = new HashSet<>();
    /**
     * 角色中文名
     */
    private String name;
    /**
     * 平台
     */
    private String platform;
}
