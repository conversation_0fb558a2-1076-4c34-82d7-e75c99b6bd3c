package com.ksyun.auth.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/16 17:41
 * @Description
 * @Version V1
 **/
@Setter
@Getter
public class MenuPropsTreeItemVo {

    public MenuPropsTreeItemVo(Long id, Integer type, String name, String icon, String url, String code, List<MenuPropsTreeItemVo> subMenus) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.icon = icon;
        this.url = url;
        this.code = code;
        this.subMenus = subMenus;
    }

    private Long id;
    private Integer type;
    private String name;
    private String icon;
    private String url;
    private String code;
    private boolean positionDown;
    private boolean jumpOutAlone;
    private List<MenuPropsTreeItemVo> subMenus;
}