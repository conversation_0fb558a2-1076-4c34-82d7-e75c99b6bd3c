package com.ksyun.auth.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class PropVo {
    public static final String USER_PROP_KEY_AK = "ak";
    public static final String USER_PROP_KEY_SK = "sk";
    public static final String USER_PROP_KEY_PHONE = "phone";
    public static final String USER_PROP_KEY_EMAIL = "email";
    public static final String USER_PROP_KEY_ALIAS = "alias";
    public static final String USER_PROP_KEY_PWD = "password";
    public static final String USER_PROP_KEY_APPID = "appId";
    public static final String USER_PROP_KEY_SECRET_LEVEL = "secret_level";
    public static final String USER_PROP_KEY_RESET_PWD = "reset_password";

    public static final String USER_PROP_KEY_PASSPORT_ID = "passport_id";

    private String key;
    private String value;
}
