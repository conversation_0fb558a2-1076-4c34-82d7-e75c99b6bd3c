package com.ksyun.auth.dao;

import com.ksyun.common.entity.UserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    int batchInsert(@Param("roleId") Long roleId,@Param("userIds") Set<Long> userIds);

}
