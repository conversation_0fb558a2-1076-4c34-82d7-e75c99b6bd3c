package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.common.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface RoleMapper extends BaseMapper<Role> {
    /**
     * 获取角色
     */
    Role get(@Param("id") long id);

    /**
     * 获取所有内置角色
     */
    List<Role> getAllDefaultRoles();

}
