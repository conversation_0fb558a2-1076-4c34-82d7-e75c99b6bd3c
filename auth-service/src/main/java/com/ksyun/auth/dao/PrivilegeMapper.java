package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.common.entity.Privilege;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Set;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface PrivilegeMapper extends BaseMapper<Privilege> {

    int updatePrePrivilegeOrders(@Param("parentId") Long parentId, @Param("currOrder") Long currOrder);

    int updateAfterPrivilegeOrders(@Param("parentId") Long parentId, @Param("currOrder") Long currOrder);

    Privilege selectSmallerPrivilege(@Param("parentId") Long parentId, @Param("currOrder") Long currOrder);

    Privilege selectLargerPrivilege(@Param("parentId") Long parentId, @Param("currOrder") Long currOrder);

    Privilege selectFirstMinPrivilege(@Param("parentId") Long parentId);

    Privilege selectLastMaxPrivilege(@Param("parentId") Long parentId);

    int updateAllMenusOrder(@Param("codes") Set<String> codes);
}
