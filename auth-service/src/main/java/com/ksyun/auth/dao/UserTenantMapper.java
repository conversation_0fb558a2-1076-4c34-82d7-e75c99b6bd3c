package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.vo.KcdeTenantUserQueryVo;
import com.ksyun.auth.vo.KcdeTenantVo;
import com.ksyun.common.entity.UserTenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Repository
@Mapper
public interface UserTenantMapper extends BaseMapper<UserTenant> {
    List<KcdeTenantVo> getUserTenants(@Param("userId") Long userId);
    List<BaseUser> selectTenantUserByCondition(KcdeTenantUserQueryVo parameter);
    int selectTenantUserCountByCondition(KcdeTenantUserQueryVo parameter);
}
