package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.auth.vo.KcdeTenantQueryVo;
import com.ksyun.auth.vo.KcdeTenantVo;
import com.ksyun.common.entity.Tenant;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Repository
@Mapper
public interface TenantMapper extends BaseMapper<Tenant> {
    List<KcdeTenantVo> selectTenantByCondition(KcdeTenantQueryVo parameter);
    int selectTenantCountByCondition(KcdeTenantQueryVo parameter);
}
