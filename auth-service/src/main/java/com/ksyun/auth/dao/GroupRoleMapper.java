package com.ksyun.auth.dao;

import com.ksyun.auth.vo.RoleVo;
import com.ksyun.common.entity.GroupRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface GroupRoleMapper extends BaseMapper<GroupRole> {

    int batchInsert(@Param("roleId") Long roleId, @Param("groupIds") Set<Long> groupIds);

    /**
     * 通过groupId查询当前组的所有角色
     * @param groupId
     * @return
     */
    List<RoleVo> getRoleListByGroupId(Long groupId);
}
