package com.ksyun.auth.dao;

import com.ksyun.auth.vo.GroupVo;
import com.ksyun.common.entity.GroupUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface GroupUserMapper extends BaseMapper<GroupUser> {

    int batchInsertUser(@Param("groupId") Long groupId, @Param("userIds") Set<Long> userIds);

    int batchInsertGroup(@Param("userId") Long userId, @Param("groupIds") Set<Long> groupIds);

    /**
     * 通过userId查询当前用户所在的所有组
     * @param userId
     * @return
     */
    List<GroupVo> getGroupListByUserId(Long userId);
}
