package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.common.entity.PersonAccessToken;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 个人访问令牌Mapper接口
 */
@Mapper
@Repository
public interface PersonAccessTokenMapper extends BaseMapper<PersonAccessToken> {
    
    /**
     * 根据用户ID查询所有有效的访问令牌
     */
    default List<PersonAccessToken> findValidTokensByUserId(Long userId) {
        return selectList(
            new QueryWrapper<PersonAccessToken>()
                .eq("user_id", userId)
                .gt("expires_at", new Date())
        );
    }

    /**
     * 删除用户的所有访问令牌
     */
    default void deleteAllByUserId(Long userId) {
        delete(
            new QueryWrapper<PersonAccessToken>()
                .eq("user_id", userId)
        );
    }

    /**
     * 根据用户ID查询访问令牌
     * 如果有多个令牌，返回最新创建的一个
     */
    default PersonAccessToken selectByUserId(Long userId) {
        return selectOne(
            new QueryWrapper<PersonAccessToken>()
                .eq("user_id", userId)
                .orderByDesc("created_at")
                .last("LIMIT 1")
        );
    }
}
