package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.auth.dto.UserAkSkDto;
import com.ksyun.common.entity.UserProps;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface UserPropsMapper extends BaseMapper<UserProps> {

    UserAkSkDto selectUserAKSK(Long userId);

//    String getRoleRoleMutexStatusByUserId(Long userId);
}
