package com.ksyun.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.vo.UserQueryVo;
import com.ksyun.common.entity.User;
import com.ksyun.common.entity.UserProps;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface UserMapper extends BaseMapper<User> {

    List<BaseUser> selectBaseUserByCondition(UserQueryVo parameter);

    int selectBaseUserCountByCondition(UserQueryVo parameter);

    //AuthUser selectByTenantTypePassport(@Param("userName") String username, @Param("sourceList") Set<String> sourceList);

    AuthUser selectByUserNameAndPassword(@Param("userName") String userNameVo, @Param("password") String password);
    AuthUser selectByUserNameAndPassword2(@Param("userName") String userNameVo, @Param("encryptPassword") String encryptPassword,@Param("password") String password,@Param("sourceList") Set<String> sourceList);

    //AuthUser lookupSubAccountTypePassport(@Param("tenantId") Long tenantId, @Param("userName") String userName, @Param("sourceList") Set<String> sourceList);

    AuthUser selectBySourceAndOidcId(@Param("source") String source,@Param("oidcId") String oidcId);


    void batchDelete(@Param("deleteUserIds") Set<Long> deleteUserIds);

    User lookupUserByUserId(Long userId);

    List<UserProps> lookupUserPropsByUserId(Long id);

    AuthUser lookupAuthUserByUserName(String userName);

    void updateUserProps(@Param("userId") Long id, @Param("propKey") String propKey, @Param("propValue") String propValue);

    List<User> getUserByRole(@Param("roleId")Long roleId);
}
