package com.ksyun.auth.dao;

import com.ksyun.common.entity.PermissionGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Repository
@Mapper
public interface PermissionGroupMapper extends BaseMapper<PermissionGroup> {

    List selectAllPrivileges();
}
