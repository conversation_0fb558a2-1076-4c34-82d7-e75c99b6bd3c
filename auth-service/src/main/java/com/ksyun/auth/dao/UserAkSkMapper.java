package com.ksyun.auth.dao;

import com.ksyun.auth.dto.UserAkSkDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> zhang
 * @Mail <EMAIL>
 * @Date 2019-03-30 15:45
 **/
@Mapper
public interface UserAkSkMapper {

    UserAkSkDto lookupUserAkSkByUserId(Long id);

    UserAkSkDto lookupUserIdByUserProps(@Param("key") String key, @Param("value") String value);
}
