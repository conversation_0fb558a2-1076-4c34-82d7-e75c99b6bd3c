package com.ksyun.auth.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ksyun.auth.vo.PropVo;
import com.ksyun.common.enums.UserStatusEnum;
import com.ksyun.common.enums.UserSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Setter
@Getter
public class UserDto {

    private Long id;
    private String name;
    private UserStatusEnum status = UserStatusEnum.NORMAL;
    private Date createTime;
    private Date updateTime;
    private UserSourceEnum source = UserSourceEnum.LOCAL;
    private InnerTenantDto tenant;
    private List<PropVo> props = new LinkedList<>();
    private Integer secretLevel;
    @JsonIgnore
    private List<InnerGroupDto> groups = new LinkedList<>();

    public static UserDto newUser(Long id, List<PropVo> props) {
        UserDto userBo = new UserDto();
        userBo.id = id;
        userBo.props = props;
        return userBo;
    }

    public UserDto setTenantId(Long tenantId) {
        this.tenant = new InnerTenantDto();
        this.tenant.id = tenantId;
        return this;
    }

    public boolean hasProp(String key) {
        return props.stream().filter(prop -> Objects.equals(prop.getKey(), key)).findAny().isPresent();
    }

    public Object getProp(String key) {
        Optional<PropVo> optional = props.stream().filter(prop -> prop.getKey().equals(key)).findAny();
        return optional.isPresent() ? optional.get().getValue() : null;
    }

    public void addOrUpdateProp(String key, String value) {
        PropVo target = new PropVo(key, value);
        this.props.removeIf(prop -> Objects.equals(prop, target));
        this.props.add(target);
    }

}
