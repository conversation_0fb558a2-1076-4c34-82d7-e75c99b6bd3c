/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.dto;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2020
 */
@Data
public class ExportPrivilegeDto {
    /**
     * 分类名称
     */
    String categoryName;
    /**
     * 权限组名称
     */
    String groupName;
    /**
     * 权限组描述
     */
    String groupDesc;
    /**
     * 菜单/权限编码
     */
    String code;
    /**
     * 菜单/权限点名称
     */
    String name;
    /**
     * 上级菜单编码
     */
    String parentCode;
    /**
     * 资源类型
     */
    Integer type;
    /**
     * 应用
     */
    String ak;
    /**
     * 接口与url
     */
    String url;
    /**
     * 一级菜单图标名称
     */
    String icon;
    String privilegeProps;
    /**
     * 操作
     */
    String operation;
}
