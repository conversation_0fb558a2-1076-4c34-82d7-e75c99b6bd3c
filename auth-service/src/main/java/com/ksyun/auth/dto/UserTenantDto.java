package com.ksyun.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/05/04 14:08
 * @Description
 * @Version V1
 **/
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class UserTenantDto {
    private Long userId;
    private Long tenantId;
    private LocalDateTime createTime;
}
