package com.ksyun.auth.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ksyun.common.enums.TenantStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Setter
@Getter
public class TenantDto {

    private Long id;
    private String name;
    private List<Long> userIds;
    private TenantStatusEnum status = TenantStatusEnum.NORMAL;
    private String remark;
    private String description;
    private Long createdBy;
    private Date createTime;
    private Date updateTime;

}
