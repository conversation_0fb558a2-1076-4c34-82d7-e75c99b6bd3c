package com.ksyun.auth.dto;


import com.ksyun.common.entity.PrivilegeProps;
import com.ksyun.auth.service.strategy.PrivilegeImportStrategyEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020
 */
@Data
public class PrivilegeExcelDto {
    /**
     * 分类名称
     */
    String categoryName = "租户侧权限组";
    /**
     * 权限组名称
     */
    String groupName;
    /**
     * 权限组描述
     */
    String groupDesc;
    /**
     * 菜单/权限编码
     */
    String code;
    /**
     * 菜单/权限点名称
     */
    String name;
    /**
     * 上级菜单编码
     */
    String parentCode;
    /**
     * 资源类型
     */
    Integer type;
    /**
     * 应用
     */
    String ak;
    /**
     * 接口与url
     */
    String url;
    /**
     * 一级菜单图标名称
     */
    String icon;
    List<PrivilegeProps> privilegeProps;
    /**
     * 操作
     */
    private PrivilegeImportStrategyEnum operation;
}
