package com.ksyun.auth.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ksyun.common.enums.UserSourceEnum;
import com.ksyun.common.enums.UserStatusEnum;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.Objects;

/**
 * 认证用户信息
 */
@Data
public class BaseUser {
    private Long id;
    private String name;
    private String alias;
    private String deadline;
    private String deadlineValidDay;
    private String phone;
    private String email;
    @JsonIgnore
    private String password;
    private Date createTime;
    private Date updateTime;
    private UserSourceEnum source;
    private InnerTenantDto tenant;
    private UserStatusEnum status;
    private Collection<InnerGroupDto> groups = new HashSet<>(1);
    private Collection<RoleDto> roles = new HashSet<>(1);
    private int secretLevel;
    private String remark;

//    /**
//     * 给页面的标识，表示当前用户是否是租户
//     */
//    public Boolean getIsTenant() {
//        return tenant != null && Objects.equals(tenant.getId(), id);
//    }

}
