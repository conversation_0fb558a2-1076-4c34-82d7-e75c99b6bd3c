package com.ksyun.auth.dto;

import jodd.util.StringUtil;
import lombok.Data;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色
 */
@Data
public class RoleDto {
    public static final String TAG_ID_SEPERATOR = ";";
    private Integer id;
    private String code;
    private String name;
    private String description;
    private int type;
    private String platform;
    private String tagIds;
    private Date createTime;
    private Date updateTime;
    private String createBy;
    private int isDefault;
    private List<String> tags;

    public static String serializeTagIds(int[] tagIds) {
        if (tagIds.length == 0) {
            return null;
        }
        String str = Arrays.stream(tagIds).mapToObj(String::valueOf).collect(Collectors.joining(RoleDto.TAG_ID_SEPERATOR));
        return str;
    }

    public static int[] deserializeTagIds(String tagIds) {
        if (StringUtil.isEmpty(tagIds)) {
            return new int[0];
        }
        String[] items = tagIds.split(RoleDto.TAG_ID_SEPERATOR);
        return Arrays.stream(items).mapToInt(Integer::parseInt).toArray();
    }

    public boolean hasTag(int tagId) {
        int[] items = deserializeTagIds(tagIds);
        return Arrays.stream(items).anyMatch(item -> item == tagId);
    }

    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type=" + type +
                ", platform=" + platform +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
