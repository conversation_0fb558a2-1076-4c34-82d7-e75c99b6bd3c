package com.ksyun.auth.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/11/27 19:46
 */
@Data
public class PrivilegeQueryDto {
    /**
     * 父级节点id
     */
    private Long parentId;
    /**
     * 权限类型，菜单or按钮
     */
    private Integer type;
    /**
     * 权限名称
     * 模糊查询
     */
    private String name;
    /**
     * id列表
     */
    private Set<Long> ids;

    private String ak;
    /**
     * 权限编码
     */
    private String code;

    private Set<Long> roleIds = new HashSet<>();

    private String appCode;

    private Set<Long> notIds = new HashSet<>();

    public PrivilegeQueryDto(Long parentId, Integer type, String name, Set<Long> ids) {
        this.parentId = parentId;
        this.type = type;
        this.name = name;
        this.ids = ids;
    }

    public PrivilegeQueryDto() {
    }
}
