/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.dto;

import com.ksyun.common.annotation.Column;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2020
 */
@Data
public class RoleExcelRowDto {
    @Column(index = 0, label = "角色名称", strategy = Column.Strategy.NONE)
    String roleName;
    @Column(index = 1, label = "角色code", strategy = Column.Strategy.NONE)
    String code;
    @Column(index = 2, label = "角色类型", strategy = Column.Strategy.NONE)
    String type;
    @Column(index = 3, label = "角色标签", strategy = Column.Strategy.NONE)
    String tag;
    @Column(index = 4, label = "角色描述", strategy = Column.Strategy.NONE)
    String desc;
    @Column(index = 5, label = "关联角色体系", strategy = Column.Strategy.NONE)
    String roleGroup;
    @Column(index = 6, label = "分配权限组", strategy = Column.Strategy.NONE)
    String permissionGroup;
    @Column(index = 7, label = "角色所属平台", strategy = Column.Strategy.NONE)
    String platform;
}
