/*
 * Copyright (C) 2020 bsyonline
 */
package com.ksyun.auth.dto;

import com.ksyun.common.annotation.Column;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2020
 */
@Data
public class PrivilegeExcelRowDto {

    @Column(index = 0, label = "权限组名称", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE, notNull = true, length = 200)
    String groupName;
    @Column(index = 1, label = "权限组描述", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE)
    String groupDesc;
    @Column(index = 2, label = "菜单/权限编码", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE, notNull = true)
    String code;
    @Column(index = 3, label = "菜单/权限点名称", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE, notNull = true, length = 100)
    String name;
    @Column(index = 4, label = "上级菜单编码", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE)
    String parentCode;
    @Column(index = 5, label = "资源类型", type = Column.Type.MAJOR, strategy = Column.Strategy.ENUM, notNull = true, length = 30)
    String type;
    @Column(index = 6, label = "应用", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE)
    String ak;
    @Column(index = 7, label = "接口与url", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE)
    String url;
    @Column(index = 8, label = "一级菜单图标名称", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE)
    String icon;
    @Column(index = 9, label = "操作", type = Column.Type.MAJOR, strategy = Column.Strategy.OPERATION)
    String operation;
    @Column(index = 10, label = "分类名称", type = Column.Type.MAJOR, strategy = Column.Strategy.NONE)
    String categoryName = "租户侧权限组";
    @Column(index = 11, label = "位置属性", type = Column.Type.PROP, strategy = Column.Strategy.POSITION)
    String positionProp;
    @Column(index = 12, label = "隐藏属性", type = Column.Type.PROP, strategy = Column.Strategy.HIDE)
    String hideProp;
    @Column(index = 13, label = "多网属性", type = Column.Type.PROP, strategy = Column.Strategy.NET)
    String netProp;
    @Column(index = 14, label = "产品服务", type = Column.Type.PROP, strategy = Column.Strategy.SERVICE)
    String serviceProp;
    @Column(index = 15, label = "默认菜单", type = Column.Type.PROP, strategy = Column.Strategy.DEFAULT_MENU)
    String defaultProp;
    @Column(index = 16, label = "系统环境", type = Column.Type.PROP, strategy = Column.Strategy.SYS_ENV)
    String sysEnvProp;
    @Column(index = 17, label = "系统授权", type = Column.Type.PROP, strategy = Column.Strategy.LICENSE)
    String licenseProp;

}
