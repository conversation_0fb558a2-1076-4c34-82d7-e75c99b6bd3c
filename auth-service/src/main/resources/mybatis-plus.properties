#æ¬å°é¡¹ç®è·¯å¾
baseDir=/tmp/common-service
#ä»£ç çæå¨è¾åºè·¯å¾
OutputDir=/auth-center/auth-privilege/src/main/java/com/ksyun/privilege
#mapper.xmlççæä½ç½®
OutputDirXml=/auth-center/auth-service/src/main/resources
#serviceççæä½ç½®
serviceOutputDir=/auth-center/auth-service/src/main/java/com/ksyun/auth
#entiyççæä½ç½®
entityOutputDir=/auth-center/auth-common/src/main/java/com/ksyun/common/entity
#æ°æ®åºè¡¨å(æ­¤å¤åä¸å¯ä¸ºç©ºï¼å¦æä¸ºç©ºï¼åé»è®¤è¯»åæ°æ®åºçææè¡¨å)
tableName=project
#è£ä»£ç çæä»¶å¤¹å
className=
#è®¾ç½®ä½è
author=libing7
#æ­£å¸¸æåµä¸ï¼ä¸é¢çä»£ç æ éä¿®æ¹ï¼ï¼ï¼ï¼ï¼ï¼ï¼ï¼ï¼ï¼
#èªå®ä¹åè·¯å¾
parent=com.ksyun
#æ°æ®åºå°å
url=**********************************************************************************************************************************************
userName=kingsoft
password=kingsoft!@#