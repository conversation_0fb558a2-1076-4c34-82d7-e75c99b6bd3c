<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.UserPropsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.UserProps">
        <id column="user_id" property="userId"/>
        <result column="prop_key" property="propKey"/>
        <result column="prop_value" property="propValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, prop_key, prop_value
    </sql>



    <select id="selectUserAKSK" resultType="com.ksyun.auth.dto.UserAkSkDto">
        select
          max(case prop_key when 'userAk' then prop_value else null end) userAk,
          max(case prop_key when 'userSk' then prop_value else null end) userSk,
          max(case prop_key when 'userAkSk-status' then prop_value else null end) userAkSkStatus,
          max(case prop_key when 'userAk.create-time' then prop_value else null end) userCreateTime,
          max(case prop_key when 'password' then prop_value else null end) password
          from user_props where user_id = #{userId}
    </select>

<!--    <select id="getRoleRoleMutexStatusByUserId" parameterType="long" resultType="string">-->
<!--        select-->
<!--            max(case prop_key when 'role_mutex_status' then prop_value else null end) roleMutexStatus-->
<!--        from user_props where user_id = #{arg0}-->
<!--    </select>-->


</mapper>
