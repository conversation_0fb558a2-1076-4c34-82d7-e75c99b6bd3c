<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.UserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.UserRole">
        <id column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, role_id, create_time
    </sql>

    <insert id="batchInsert">
        insert ignore into user_role (user_id, role_id, create_time) values
        <foreach collection="userIds" item="userId" separator=",">
            (#{userId}, #{roleId}, now())
        </foreach>
    </insert>

</mapper>
