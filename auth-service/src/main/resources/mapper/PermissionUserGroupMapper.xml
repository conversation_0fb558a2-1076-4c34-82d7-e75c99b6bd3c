<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.PermissionUserGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="PermissionGroupMap" type="com.ksyun.common.entity.PermissionGroup">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="readonly" property="readonly"/>
        <result column="status" property="status"/>
        <result column="category_id" property="categoryId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>




</mapper>
