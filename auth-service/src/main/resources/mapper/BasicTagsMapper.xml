<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.privilege.dao.mapper.BasicTagsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.BasicTags">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="key" property="key" />
        <result column="group" property="group" />
        <result column="role_type_apply" property="roleTypeApply" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, key, group, role_type_apply
    </sql>

</mapper>