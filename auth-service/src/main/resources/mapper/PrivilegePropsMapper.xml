<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.PrivilegePropsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.PrivilegeProps">
        <id column="privilege_id" property="privilegeId" />
        <result column="prop_key" property="propKey" />
        <result column="prop_value" property="propValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        privilege_id, prop_key, prop_value
    </sql>

</mapper>
