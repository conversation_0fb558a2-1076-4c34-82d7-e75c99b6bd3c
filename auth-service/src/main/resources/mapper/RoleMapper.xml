<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.RoleMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.Role">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="role" property="role"/>
        <result column="description" property="description"/>
        <result column="tag_ids" property="tagIds"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="default" property="defaultRole"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, type, role, description, tag_ids, create_time, create_by, update_time, default
    </sql>

    <select id="get" resultType="com.ksyun.common.entity.Role" resultMap="BaseResultMap">
        select * from role where id=#{id}
    </select>

    <select id="getAllDefaultRoles" resultType="com.ksyun.common.entity.Role" resultMap="BaseResultMap">
        select r.* from role r where r.default='1' order by r.name
    </select>

</mapper>
