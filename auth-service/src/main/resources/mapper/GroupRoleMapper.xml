<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.GroupRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.GroupRole">
        <id column="group_id" property="groupId" />
        <result column="role_id" property="roleId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        group_id, role_id, create_time
    </sql>

    <insert id="batchInsert">
        insert ignore into group_role (role_id, group_id, create_time) values
        <foreach collection="groupIds" item="groupId" separator=",">
            (#{roleId}, #{groupId}, now())
        </foreach>
    </insert>

    <select id="getRoleListByGroupId" parameterType="long" resultType="com.ksyun.auth.vo.RoleVo">
        select r.id, r.name from `role` r left join group_role gu on gu.group_id = r.id
        where gu.group_id = #{groupId}
    </select>

</mapper>
