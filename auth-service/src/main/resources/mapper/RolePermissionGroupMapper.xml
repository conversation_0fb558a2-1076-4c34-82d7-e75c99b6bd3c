<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.RolePermissionGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.RolePermissionGroup">
        <result column="role_id" property="roleId" />
        <result column="role_name" property="roleName" />
        <result column="permission_group_id" property="permissionGroupId" />
        <result column="permission_group_name" property="permissionGroupName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        role_id, role_name,permission_group_id,permission_group_name, create_time
    </sql>

</mapper>
