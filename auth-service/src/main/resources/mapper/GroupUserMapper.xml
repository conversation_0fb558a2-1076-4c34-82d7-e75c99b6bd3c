<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.GroupUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.GroupUser">
        <id column="user_id" property="userId" />
        <result column="group_id" property="groupId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, group_id, create_time
    </sql>

    <insert id="batchInsertUser" >
        insert ignore into group_user (group_id, user_id, create_time) values
        <foreach collection="userIds" item="userId" separator=",">
            (#{groupId}, #{userId}, now())
        </foreach>
    </insert>


    <insert id="batchInsertGroup" >
        insert ignore into group_user (group_id, user_id, create_time) values
        <foreach collection="groupIds" item="groupId" separator=",">
            (#{groupId}, #{userId}, now())
        </foreach>
    </insert>


    <select id="getGroupListByUserId" parameterType="long" resultType="com.ksyun.auth.vo.GroupVo">
        select g.id, g.name from `group` g left join group_user gu on gu.group_id = g.id
        where gu.user_id = #{userId}
    </select>


</mapper>
