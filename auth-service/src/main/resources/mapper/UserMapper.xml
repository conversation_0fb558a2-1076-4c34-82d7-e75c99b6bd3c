<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.User">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="source" property="source"/>
<!--        <result column="tenant_id" property="tenantId"/>-->
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
    </resultMap>
    <resultMap id="GroupMap"
               type="com.ksyun.auth.dto.InnerGroupDto">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="description" column="description"/>
    </resultMap>
    <resultMap id="RoleMap" type="com.ksyun.auth.dto.RoleDto">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
    </resultMap>
    <resultMap id="UserRoleSystemResultMap" type="com.ksyun.auth.vo.UserVo">
        <result column="id" property="id"></result>
        <result column="name" property="name"></result>
        <result column="create_time" property="createTime"></result>
    </resultMap>
    <resultMap id="BaseUserMap" type="com.ksyun.auth.dto.BaseUser">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="password" column="password"/>
        <result property="source" column="source"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="alias" column="alias"/>
        <result property="deadline" column="deadline"/>
        <result property="deadlineValidDay" column="deadlineValidDay"/>
        <result property="secretLevel" column="secret_level"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
<!--        <association property="tenant" javaType="com.ksyun.auth.dto.InnerTenantDto">-->
<!--            <id property="id" column="tenant_id"/>-->
<!--            <result property="name" column="tenant_name"/>-->
<!--        </association>-->
        <collection property="groups" ofType="com.ksyun.auth.dto.InnerGroupDto" javaType="java.util.Set"
                    column="id" select="selectUserGroups">
        </collection>
        <collection property="roles" ofType="com.ksyun.auth.dto.RoleDto" javaType="java.util.Set"
                    column="id" select="lookupRolesByUserId">
        </collection>
    </resultMap>

    <resultMap id="UserAuthenticationMap" type="com.ksyun.auth.client.authentication.AuthUser">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="alias" column="alias"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="secretLevel" column="secret_level"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="resetPwdWhenFirstLogin" column="reset_password"/>
<!--        <result property="ak" column="ak"/>-->
<!--        <result property="sk" column="sk"/>-->
<!--        <result property="userAk" column="userAk"/>-->
<!--        <result property="userSk" column="userSk"/>-->
<!--        <association property="tenant" javaType="com.ksyun.auth.client.authentication.AuthUser$InnerTenant">-->
<!--            <id property="id" column="tenant_id"/>-->
<!--            <result property="name" column="tenant_name"/>-->
<!--        </association>-->
        <collection property="groups" ofType="string" javaType="java.util.Set">
            <result column="group_name"/>
        </collection>
    </resultMap>
    <resultMap id="UserSimpleMap" type="com.ksyun.auth.dto.UserSimpleDto">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="alias" column="alias"/>
    </resultMap>
    <!-- 通用查询结果列 -->
<!--    <sql id="Base_Column_List">-->
<!--        id, name, source, tenant_id, create_time, update_time, status-->
<!--    </sql>-->

    <sql id="base_user_page_sql">
        from user u left join (select user_id,
        max(case up.prop_key when 'password' then up.prop_value else null end) as password,
        max(case up.prop_key when 'phone' then up.prop_value else null end) as phone,
        max(case up.prop_key when 'email' then up.prop_value else null end) as email,
        max(case up.prop_key when 'alias' then up.prop_value else null end) as alias,
        max(case up.prop_key when 'deadline' then up.prop_value else 'LONGTERM' end) as deadline,
        max(case up.prop_key when 'deadline-valid-day' then up.prop_value else null end) as deadlineValidDay,
        max(case up.prop_key when 'secret_level' then up.prop_value else null end) as secret_level
        from user_props up group by user_id) up on u.id = up.user_id
        <where>
            <if test="includeDeletedUsers == false and status.size==0" >
                u.status in ('NORMAL','SLEEP','LOGOUT')
            </if>
            <if test="userIds.size() > 0">
                <foreach collection="userIds" item="uid" open="and u.id in (" separator="," close=")">#{uid}</foreach>
            </if>
            <if test="status.size() > 0">
                <foreach collection="status" item="s" open="and u.status in (" separator="," close=")">#{s}</foreach>
            </if>
            <if test="name != null and name != ''">and instr(u.name, #{name})</if>
            <if test="phone != null">and instr(up.phone , #{phone})</if>
            <if test="email != null">and instr(up.email , #{email})</if>
            <if test="alias != null">and instr(up.alias , #{alias})</if>
            <if test="source != null">and u.source = #{source}</if>
            <if test="groupIds.size() > 0">
                <if test="userInGroup == true">
                    and u.id in (select distinct user_id from group_user where group_id in (
                    <foreach collection="groupIds" item="item" separator=",">#{item}</foreach>
                    ))
                </if>
                <if test="userInGroup == false">
                    and u.id not in (select distinct user_id from group_user where group_id in (
                    <foreach collection="groupIds" item="item" separator=",">#{item}</foreach>
                    ))
                </if>
            </if>
            <if test="roleIds.size() > 0 and onlyShowUsersBindToRole == false">
                <if test="userBindRole == true">
                    and u.id in (select distinct user_id from user_role where role_id in (
                    <foreach collection="roleIds" item="item" separator=",">#{item}</foreach>
                    ) union SELECT distinct user_id from group_user where group_id in (SELECT distinct group_id from
                    group_role where role_id in (
                    <foreach collection="roleIds" item="item" separator=",">#{item}</foreach>
                    )))
                </if>
                <if test="userBindRole == false">
                    and u.id not in (select distinct user_id from user_role where role_id in (
                    <foreach collection="roleIds" item="item" separator=",">#{item}</foreach>
                    ) union SELECT distinct user_id from group_user where group_id in (SELECT distinct group_id from
                    group_role where role_id in (
                    <foreach collection="roleIds" item="item" separator=",">#{item}</foreach>
                    )))
                </if>
            </if>
            <if test="roleIds.size() > 0 and onlyShowUsersBindToRole == true">
                and u.id in (select distinct user_id from user_role where role_id in (
                <foreach collection="roleIds" item="item" separator=",">#{item}</foreach>
                ))
            </if>

            <if test="noRoleIds != null and noRoleIds.size() > 0">
                and u.id not in (select distinct user_id from user_role where role_id in (
                <foreach collection="noRoleIds" item="item" separator=",">#{item}</foreach>
                ))
            </if>

            <if test="roleNames.size() > 0">
                and u.id in (select distinct ur.user_id from user_role ur left join role r on r.id = ur.role_id where
                r.code in(
                <foreach collection="roleNames" item="item" separator=",">#{item}</foreach>
                ) union SELECT distinct user_id from group_user where group_id in (SELECT distinct group_id from
                group_role gr left join role r on r.id = gr.role_id where
                r.code in(
                <foreach collection="roleNames" item="item" separator=",">#{item}</foreach>
                )))
            </if>
            <if test="secretLevel != null">
                and secret_level = #{secretLevel}
            </if>
        </where>
    </sql>
    <sql id="lookup_auth_user">
        select u.id, u.name, u.source, u.create_time, u.update_time, up.alias, up.password, up.reset_password, g.name group_name, u.status, r.code
        role_name
        from `user` u
        left join ( select user_id,
        max(case up.prop_key when 'password' then prop_value else null end) password,
        max(case up.prop_key when 'alias' then prop_value else null end) alias,
        max(case prop_key when 'secret_level' then prop_value else null end) as secret_level,
        max(case prop_key when 'reset_password' then prop_value else null end) as reset_password
        from `user_props` up group by user_id) up on u.id = up.user_id
        left join group_user gu on u.id = gu.user_id
        left join `group` g on gu.group_id = g.id
        left join (select a.user_id, b.role_id from group_user a inner join group_role b on a.group_id = b.group_id
        union
        select user_id, role_id from user_role) ur on u.id = ur.user_id
        left join `role` r on ur.role_id = r.id
        where u.status in ('NORMAL','SLEEP','LOGOUT')
    </sql>

    <sql id="lookup_auth_user2">
        SELECT
            u.id,
            u.NAME,
            u.source,
            u.create_time,
            u.update_time,
            up.alias,
            up.PASSWORD,
            up.reset_password,
            g.NAME group_name,
            u.STATUS
        FROM
            `user` u
                LEFT JOIN (
                SELECT
                    user_id,
                    max( CASE up.prop_key WHEN 'password' THEN prop_value ELSE NULL END ) PASSWORD,
                    max( CASE up.prop_key WHEN 'alias' THEN prop_value ELSE NULL END ) alias,
                    max( CASE prop_key WHEN 'secret_level' THEN prop_value ELSE NULL END ) AS secret_level,
                    max( CASE prop_key WHEN 'reset_password' THEN prop_value ELSE NULL END ) AS reset_password
                FROM
                    `user_props` up
                GROUP BY
                    user_id
            ) up ON u.id = up.user_id
                LEFT JOIN group_user gu ON u.id = gu.user_id
                LEFT JOIN `group` g ON gu.group_id = g.id
        WHERE
            u.STATUS IN ( 'NORMAL', 'SLEEP', 'LOGOUT' )
    </sql>

    <select id="selectBaseUserByCondition" parameterType="com.ksyun.auth.vo.UserQueryVo"
            resultMap="BaseUserMap">
        select u.id, u.name, u.source, u.status, up.alias, up.password, up.phone, up.deadline,up.deadlineValidDay, up.email,up.secret_level, u.create_time, u.update_time, u.remark
        <include refid="base_user_page_sql"/>
        <if test='orderParameter == "1"'>
            order by u.name
        </if>
        <if test='orderParameter == "2"'>
            order by u.name desc
        </if>
        <if test="orderParameter == null">
            order by u.id desc
        </if>
        <if test="pageGlobal == true">
            limit ${(pageNo - 1) * pageSize}, #{pageSize}
        </if>
    </select>

    <select id="selectBaseUserCountByCondition"
            parameterType="com.ksyun.auth.vo.UserQueryVo" resultType="int">
        SELECT count(*)
        <include refid="base_user_page_sql"/>
    </select>

<!--    <select id="selectPageForTenantPage" resultType="com.ksyun.auth.vo.UserVo"-->
<!--            resultMap="UserRoleSystemResultMap">-->
<!--        select u.id,u.name from `user` u where u.id = u.tenant_id-->
<!--        and u.id not in (select rsu.user_id from role_system_user rsu)-->
<!--        <if test="name != null">-->
<!--            and instr(u.name, #{name})-->
<!--        </if>-->
<!--    </select>-->

<!--    <select id="selectByTenantTypePassport" resultMap="UserAuthenticationMap">-->
<!--        <include refid="lookup_auth_user"/>-->
<!--        and u.id = u.tenant_id and u.name = #{userName}-->
<!--        <if test="sourceList.size() > 0">-->
<!--            <foreach collection="sourceList" item="source" open="and u.source in (" separator="," close=")">-->
<!--                #{source}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->

<!--    <select id="lookupSubAccountTypePassport" resultMap="UserAuthenticationMap">-->
<!--        <include refid="lookup_auth_user"/>-->
<!--        and u.id != u.tenant_id and tenant.tenant_id = #{tenantId} and u.name = #{userName}-->
<!--        <if test="sourceList.size() > 0">-->
<!--            <foreach collection="sourceList" item="source" open="and u.source in (" separator="," close=")">-->
<!--                #{source}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->

    <select id="selectByUserNameAndPassword" resultMap="UserAuthenticationMap">
        <include refid="lookup_auth_user"/>
--         and u.source in ('LOCAL')
            and u.name = #{userName} and up.password = #{password}
    </select>

    <select id="selectBySourceAndOidcId" resultMap="UserAuthenticationMap">
        <include refid="lookup_auth_user"/>
        and u.source =#{source} and u.oidc_id = #{oidcId}
    </select>

    <select id="selectUserGroups" parameterType="int" resultMap="GroupMap">
        select g.id, g.name, g.description, gu.create_time from group_user gu
        inner join `group` g on gu.group_id = g.id where gu.user_id = #{id}
    </select>

    <select id="lookupRolesByUserId" resultMap="RoleMap" parameterType="long">
        select r.id, r.code, r.name, r.platform from user_role ur inner join role r on ur.role_id = r.id where ur.user_id = #{id}
        union SELECT r.id, r.code, r.name, r.platform from group_role gr inner join role r on gr.role_id = r.id  where gr.group_id in (select group_id from group_user where user_id = #{id})
        order by id
    </select>

    <update id="batchDelete" parameterType="java.util.Set">
        update user set status='DELETED', update_time=now() where
        <foreach collection="deleteUserIds" item="id" open=" id in (" separator="," close=")">#{id}</foreach>
    </update>

    <select id="lookupUserByUserId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select * from user u where u.id = #{userId}
    </select>

    <select id="lookupUserPropsByUserId" resultType="com.ksyun.common.entity.UserProps">
        select user_id as userId,prop_key as propKey,prop_value as propValue from user_props where user_id = #{id}
    </select>

    <select id="lookupAuthUserByUserName" resultMap="UserAuthenticationMap">
        <include refid="lookup_auth_user"/>
        and u.source in ('LOCAL') and u.name = #{userName}
    </select>
    <select id="selectByUserNameAndPassword2" resultType="com.ksyun.auth.client.authentication.AuthUser">
        <include refid="lookup_auth_user2"/>
        and u.name = #{userName}
        and (up.password = #{encryptPassword} or up.password = #{password})
        and u.source in
        <foreach collection="sourceList" item="source" open="(" separator="," close=")">
             #{source}
        </foreach>
    </select>

    <update id="updateUserProps">
        update `user_props` set prop_value=#{propValue} where prop_key = #{propKey} and user_id = #{userId}
    </update>

    <select id="getUserByRole" resultMap="BaseResultMap">
        select u.id as id,
               u.name as name,
               u.source as source,
               u.create_time as create_time,
               u.update_time as update_time,
               u.status as status,
               u.oidc_id as oidc_id,
               u.remark as remark
        from `user` as u
        where u.id in (select ur.user_id from user_role as ur where ur.role_id = #{roleId}) and u.status != 'DELETED';
    </select>
</mapper>
