<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.TokenMapper">

    <select id="lookupLatestDsk" parameterType="int" resultType="string">
        select dynamic_key from auth_dynamic_key order by period desc limit #{limit}
    </select>

    <select id="lookupAppSk" resultType="string">
        select sk from app_aksk where ak = #{ak}
    </select>

    <select id="lookupTenantAkSk" resultType="string">
       select concat(up.ak, ',', up.sk) from
        (select user_id, max(case prop_key when 'ak' then prop_value else null end) ak, max(case prop_key when 'sk' then prop_value else null end) sk from user_props group by user_id ) up
        left join user u on up.user_id = u.id
      where up.user_id = u.tenant_id and u.status = 'NORMAL' and up.ak is not null and up.sk is not null and u.name = #{tenant}
    </select>

    <insert id="saveDsy">
        insert ignore into auth_dynamic_key(period, dynamic_key) values(#{period}, #{dsk})
    </insert>

</mapper>