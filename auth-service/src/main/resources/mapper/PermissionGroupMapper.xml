<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.PermissionGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.PermissionGroup">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="readonly" property="readonly"/>
        <result column="status" property="status"/>
        <result column="source" property="source"/>
        <result column="category_id" property="categoryId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="ExportPrivilegeDataMap" type="com.ksyun.auth.dto.ExportPrivilegeDto">
        <result column="category_name" property="categoryName"/>
        <result column="group_name" property="groupName"/>
        <result column="group_desc" property="groupDesc"/>
        <result column="CODE" property="code"/>
        <result column="NAME" property="name"/>
        <result column="parent_code" property="parentCode"/>
        <result column="type" property="type"/>
        <result column="icon" property="icon"/>
        <result column="url" property="url"/>
        <result column="ak" property="ak"/>
        <result column="operation" property="operation"/>
        <result column="props" property="privilegeProps"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, readonly, status, category_id, create_by, create_time, update_time
    </sql>
    <select id="selectAllPrivileges" resultMap="ExportPrivilegeDataMap">
        SELECT
            g.category_name,
            g.group_name,
            g.group_desc,
            p.CODE,
            p.NAME,
            p.type,
            p.icon,
            p.parent_id,
            p2.CODE parent_code,
            p.url,
            p.ak,
            "新增" as operation,
            props
        FROM
            privilege p
            LEFT JOIN ( SELECT privilege_id, GROUP_CONCAT( concat( prop_key, '=', prop_value ) ) props FROM privilege_props GROUP BY privilege_id ) pp ON p.id = pp.privilege_id
            LEFT JOIN privilege p2 ON p.parent_id = p2.id
            LEFT JOIN (
        SELECT
            p.CODE,
            c.NAME category_name,
            GROUP_CONCAT( pg.NAME ) group_name,
            GROUP_CONCAT( pg.description ) group_desc
        FROM
            privilege p
            LEFT JOIN privilege_permission_group ppg ON p.id = ppg.privilege_id
            LEFT JOIN permission_group pg ON pg.id = permission_group_id
            AND pg.STATUS = 'NORMAL'
            LEFT JOIN category c ON c.id = pg.category_id
        GROUP BY
            p.id,
            p.CODE,
            c.NAME
            ) g ON p.CODE = g.CODE
    </select>

    <!--<select id="selectPermissionGroupByRoleId" resultMap="BaseResultMap">
        SELECT
            a.id,
            a.name,
            a.description,
            a.readonly,
            a.status,
            a.category_id,
            a.create_by,
            a.create_time,
            a.update_time
        FROM
            role_permission_group u
                LEFT JOIN permission_group a ON u.permission_group_id = a.id
        WHERE
            a.status = 'NORMAL'
            AND u.role_id = ${roleId}
    </select>-->

</mapper>
