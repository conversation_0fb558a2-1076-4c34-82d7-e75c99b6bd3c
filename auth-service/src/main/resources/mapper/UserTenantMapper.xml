<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.UserTenantMapper">

    <resultMap id="UserTenantMap" type="com.ksyun.auth.vo.KcdeTenantVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="description" column="description"/>
<!--        <result property="createdBy" column="create_by"/>-->
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <association property="createdBy"  column="created_by"
                     select="com.ksyun.auth.dao.UserMapper.lookupUserByUserId"/>
    </resultMap>

    <resultMap id="RoleMap" type="com.ksyun.auth.dto.RoleDto">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
    </resultMap>

    <sql id="tenant_user_page_sql">
        from user u join user_tenant ut on u.id = ut.user_id
        left join (select user_id,
        max(case up.prop_key when 'password' then up.prop_value else null end) as password,
        max(case up.prop_key when 'phone' then up.prop_value else null end) as phone,
        max(case up.prop_key when 'email' then up.prop_value else null end) as email,
        max(case up.prop_key when 'alias' then up.prop_value else null end) as alias,
        max(case up.prop_key when 'deadline' then up.prop_value else 'LONGTERM' end) as deadline,
        max(case up.prop_key when 'deadline-valid-day' then up.prop_value else null end) as deadlineValidDay,
        max(case up.prop_key when 'secret_level' then up.prop_value else null end) as secret_level
        from user_props up group by user_id) up on up.user_id = u.id
        <if test="roleId != null">
            join user_role ur on u.id = ur.user_id
        </if>
        <where>
            ut.tenant_id = ${tenantId}
            <if test="username != null">
                and u.name = "${username}"
            </if>
            <if test="roleId != null">
                and ur.role_id = #{roleId}
            </if>
        </where>
    </sql>

    <select id="getUserTenants" parameterType="java.lang.Long" resultMap="UserTenantMap">
        select t.id, t.name, t.status, t.create_time from tenant t join user_tenant ut on ut.tenant_id = t.id where ut.user_id = #{userId};
    </select>

    <select id="lookupRolesByUserId" resultMap="RoleMap" parameterType="long">
        select r.id, r.code, r.name from user_role ur inner join role r on ur.role_id = r.id where ur.user_id = #{id}
        union SELECT r.id, r.code, r.name from group_role gr inner join role r on gr.role_id = r.id where gr.group_id in (select group_id from group_user where user_id = #{id})
        order by id
    </select>

    <select id="selectTenantUserByCondition" parameterType="com.ksyun.auth.vo.KcdeTenantUserQueryVo" resultMap="com.ksyun.auth.dao.UserMapper.BaseUserMap">
        select u.id, u.name, u.source, u.status, up.alias, up.password, up.phone, up.deadline,up.deadlineValidDay, up.email,up.secret_level, u.create_time, u.update_time
        <include refid="tenant_user_page_sql"/>
        <if test="pageGlobal == true">
            limit ${(pageNo - 1) * pageSize}, #{pageSize}
        </if>
    </select>

    <select id="selectTenantUserCountByCondition" parameterType="com.ksyun.auth.vo.KcdeTenantUserQueryVo" resultType="int">
        SELECT count(distinct u.id)
        <include refid="tenant_user_page_sql"/>
    </select>

</mapper>
