<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.AppAkskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.AppAksk">
        <id column="ak" property="ak" />
        <result column="sk" property="sk" />
        <result column="description" property="description" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ak, sk, description, create_by, create_time
    </sql>

</mapper>
