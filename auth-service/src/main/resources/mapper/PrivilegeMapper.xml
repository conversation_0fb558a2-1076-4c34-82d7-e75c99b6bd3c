<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.PrivilegeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ksyun.common.entity.Privilege">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="icon" property="icon"/>
        <result column="order" property="order"/>
        <result column="parent_id" property="parentId"/>
        <result column="url" property="url"/>
        <result column="business" property="business"/>
        <result column="ak" property="ak"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, type, icon, order, parent_id, url, business, ak, create_time
    </sql>

    <select id="selectSmallerPrivilege" resultType="com.ksyun.common.entity.Privilege">
        <![CDATA[
        SELECT id,code,name,`order` from privilege WHERE parent_id=#{parentId}  AND `order` < #{currOrder} ORDER BY `order` DESC limit 1;
        ]]>
    </select>
    <select id="selectLargerPrivilege" resultType="com.ksyun.common.entity.Privilege">
        <![CDATA[
        SELECT id,code,name,`order` from privilege WHERE parent_id=#{parentId}  AND `order` > #{currOrder} ORDER BY `order` ASC limit 1;
        ]]>
    </select>
    <select id="selectFirstMinPrivilege" resultType="com.ksyun.common.entity.Privilege">
        <![CDATA[
        SELECT id, `name`, parent_id,`order` from privilege WHERE parent_id=#{parentId} order by `order` ASC limit 1;
        ]]>
    </select>
    <select id="selectLastMaxPrivilege" resultType="com.ksyun.common.entity.Privilege">
        <![CDATA[
        SELECT id, `name`, parent_id,`order` from privilege WHERE parent_id=#{parentId} order by `order` DESC limit 1;
        ]]>
    </select>

    <update id="updatePrePrivilegeOrders" parameterType="map">
        <![CDATA[
         UPDATE privilege SET `order` = `order`+ 1 WHERE parent_id= #{parentId} and `order` < #{currOrder};
        ]]>
    </update>

    <update id="updateAfterPrivilegeOrders" parameterType="map">
        <![CDATA[
         UPDATE privilege SET `order` = `order`- 1 WHERE parent_id= #{parentId} and `order` > #{currOrder};
        ]]>
    </update>
    <update id="updateAllMenusOrder">
         UPDATE privilege SET `order` = id where code in (
        <foreach collection="codes" item="code" separator=",">
            #{code}
        </foreach>
        )
    </update>

</mapper>
