<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.UserAkSkMapper">

    <select id="lookupUserAkSkByUserId" parameterType="long" resultType="com.ksyun.auth.dto.UserAkSkDto">
        select u.id, u.name, up.ak, up.sk, up.password, t.id tenantId, t.name tenantName from
          (select user_id,
            max(case prop_key when 'ak' then prop_value else null end) ak,
            max(case prop_key when 'sk' then prop_value else null end) sk,
            max(case prop_key when 'password' then prop_value else null end) password from user_props group by user_id ) up
        left join user u on up.user_id = u.id
        left join user t on u.tenant_id = t.id
      where up.user_id = #{id} and u.status = 'NORMAL' and up.ak is not null and up.sk is not null and u.name is not null
    </select>

    <select id="lookupUserIdByUserProps" resultType="com.ksyun.auth.dto.UserAkSkDto">
        select id,tenant_id as tenantId,name from user where id = (select user_id from user_props where prop_key = #{key} and prop_value = #{value})
    </select>

</mapper>