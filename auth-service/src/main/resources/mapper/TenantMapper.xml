<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.auth.dao.TenantMapper">

    <resultMap id="TenantMap" type="com.ksyun.auth.vo.KcdeTenantVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="description" column="description"/>
<!--        <result property="createdBy" column="created_by"/>-->
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <association property="createdBy"  column="created_by"
                     select="com.ksyun.auth.dao.UserMapper.lookupUserByUserId"/>
        <collection property="users" column="id" select="getTenantUserById"/>

<!--        <collection property="userIds" ofType="java.lang.Long" javaType="java.util.List">-->
<!--            <result column="userId"/>-->
<!--        </collection>-->
    </resultMap>

    <sql id="tenant_page_sql">
        from tenant t
        <if test="userId != null">
            inner join user_tenant ut on ut.tenant_id = t.id
        </if>
        <where>
            t.status != "DELETED"
            <if test="userId != null">
                and ut.user_id = ${userId}
            </if>
            <if test="tenantId != null">
                and t.id = "${tenantId}"
            </if>
            <if test="tenantName != null">
                and t.name = "${tenantName}"
            </if>
        </where>
    </sql>

    <select id="selectTenantByCondition" parameterType="com.ksyun.auth.vo.KcdeTenantQueryVo" resultMap="TenantMap">
        select t.id, t.name, t.status, t.create_time, t.update_time, t.remark, t.description, t.created_by
        <include refid="tenant_page_sql"/>
        <if test="pageGlobal == true">
            limit ${(pageNo - 1) * pageSize}, #{pageSize}
        </if>
    </select>

    <select id="getTenantUserById" parameterType="java.lang.Long" resultMap="com.ksyun.auth.dao.UserMapper.BaseUserMap">
        select u.id, u.name, u.source, u.status, up.alias, up.password, up.phone, up.deadline,up.deadlineValidDay, up.email,up.secret_level, u.create_time, u.update_time from user u
            join user_tenant ut on u.id = ut.user_id
            left join (select user_id,
                              max(case up.prop_key when 'password' then up.prop_value else null end) as password,
                              max(case up.prop_key when 'phone' then up.prop_value else null end) as phone,
                              max(case up.prop_key when 'email' then up.prop_value else null end) as email,
                              max(case up.prop_key when 'alias' then up.prop_value else null end) as alias,
                              max(case up.prop_key when 'deadline' then up.prop_value else 'LONGTERM' end) as deadline,
                              max(case up.prop_key when 'deadline-valid-day' then up.prop_value else null end) as deadlineValidDay,
                              max(case up.prop_key when 'secret_level' then up.prop_value else null end) as secret_level
            from user_props up group by user_id) up on up.user_id = u.id
            where ut.tenant_id = #{id}
    </select>

    <select id="selectTenantCountByCondition" parameterType="com.ksyun.auth.vo.KcdeTenantQueryVo" resultType="int">
        SELECT count(distinct t.id)
        <include refid="tenant_page_sql"/>
    </select>

</mapper>
