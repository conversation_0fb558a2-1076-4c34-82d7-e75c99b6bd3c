package com.ksyun.common.dto;

import com.ksyun.common.entity.PersonAccessToken;
import lombok.Data;

import java.util.Date;

/**
 * 个人访问令牌DTO
 */
@Data
public class PersonAccessTokenDTO {
    /**
     * 令牌值
     */
    private String tokenValue;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 过期时间
     */
    private Date expiresAt;

    /**
     * 从实体类转换为DTO
     */
    public static PersonAccessTokenDTO fromEntity(PersonAccessToken entity) {
        PersonAccessTokenDTO dto = new PersonAccessTokenDTO();
        dto.setTokenValue(entity.getTokenValue());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setExpiresAt(entity.getExpiresAt());
        return dto;
    }
}
