package com.ksyun.common.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/13 17:29
 */
public enum PrivilegePropEnum {
    /**
     * 权限配置枚举
     */
    IS_INDEX("isIndex", "首页"),
    JUMP_OUT_ALONE("jumpOutAlone", "跳出"),
    POSITION_DOWN("positionDown", "底部"),
    PRODUCT_CODE("code", "服务映射码");

    private String key;
    private String description;

    PrivilegePropEnum(String key, String description) {
        this.key = key;
        this.description = description;
    }

    public static List<String> getMenuKeys() {
        return Arrays.asList(IS_INDEX.getKey(), JUMP_OUT_ALONE.getKey(), POSITION_DOWN.getKey(),
            PRODUCT_CODE.getKey());
    }

    public static List<String> getConsoleMenuKeys() {
        return Arrays.asList(JUMP_OUT_ALONE.getKey(), POSITION_DOWN.getKey());
    }

    public static List<String> getPositionDownAndJumpMenuKeys() {
        return Arrays.asList(JUMP_OUT_ALONE.key, POSITION_DOWN.key);
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }
}
