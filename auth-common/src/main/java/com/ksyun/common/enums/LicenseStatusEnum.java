package com.ksyun.common.enums;

import lombok.Getter;

public enum LicenseStatusEnum {
    UNAUTH("unauth","未授权"),
    NORMAL("normal","正常"),
    OUT_OF_DATE("outOfDate","过期"),
    NEARLY("nearly","即将过期"),
    PERPETUAL("perpetual","永久有效")
    ;

    @Getter
    private String code;

    @Getter
    private String description;

    LicenseStatusEnum(String code, String description){
        this.code = code;
        this.description = description;
    }

}