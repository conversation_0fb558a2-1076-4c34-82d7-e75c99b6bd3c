package com.ksyun.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020
 */
@Getter
@AllArgsConstructor
public enum BusinessExceptionEnum implements IExceptionResponse {

    /**
     * AKSK
     */
    AK_SK_CAN_NOT_BE_OPERATING(7001, "没有操作AKSK权限"),
    AK_SK_NOT_BINDING_APP_ID(7002, "当前用户未绑定AppId"),
    AK_SK_ALREADY_BOUND(7003, "已绑定AKSK"),
    AK_SK_NOT_BINDING(7004, "当前用户未绑定AKSK"),
    AK_SK_ALREADY_IN_USE(7005, "当前AkSk为启用状态"),
    AK_SK_EXISTED(7006, "AkSk已存在"),
    AK_SK_INVALID(7007, "AKSK不正确"),

    /**
     * 用户
     */
    USER_INVALID(7020, "userId不正确"),
    USER_NOT_FOUND(7021, "用户不存在"),
    USER_EXISTED(7022, "用户已存在"),
    TENANT_NOT_FOUND(7023, "租户不存在"),
    TENANT_INVALID(7024, "tenantId不正确"),
    USER_SECRET_LEVEL_INVALID(7025, "无效的保密等级"),
    USER_PASSWORD_CONSISTENT(7026, "新密码与原密码一致"),
    USER_PASSWORD_NOT_CONSISTENT(7027, "密码不一致"),
    USER_PASSWORD_ERROR(7028, "原密码错误"),
    USER_USERNAME_OR_PASSWORD_ERROR(7029, "用户名或密码错误"),
    USER_PROHIBITED_NAME(7030, "名称禁止使用"),
    USER_NOT_LOGIN(7031, "用户未登录"),
    USER_NOT_EXISTED(7032, "用户不存在"),
    TENANT_EXISTED(7033, "租户已存在"),
    LOGINTYPE_NOT_EXISTED(7034, "登录类型不存在"),
    SOURCE_NOT_EXISTED(7035, "用户来源不存在"),

    /**
     * 群组
     */
    GROUP_NAME_NOT_EMPTY(7040, "群组名称不能为空"),
    GROUP_NOT_BINDING(7041, "用户与群组没有绑定关系，不能进行解绑."),
    GROUP_EXISTED(7042, "群组已存在"),
    GROUP_NOT_FOUND(7043, "群组不存在"),
    GROUP_ALREADY_BOUND(7044, "群组已绑定"),

    /**
     * 角色体系
     */
    ROLE_SYSTEM_NOT_FOUND(7050, "角色体系不存在"),
    ROLE_SYSTEM_EXISTED(7051, "角色体系已存在"),
    ROLE_SYSTEM_ALREADY_IN_USE(7052, "角色体系已绑定"),
    ROLE_SYSTEM_ALREADY_IN_USE_CAN_NOT_CANCEL(7052, "角色体系已关联租户，无法取消"),
    ROLE_SYSTEM_ALREADY_IN_USE_CAN_NOT_DELETE(7054, "角色体系已关联用户，无法删除"),
    ROLE_SYSTEM_RELATED(7053, "请先解绑角色体系再删除"),

    /**
     * 角色
     */
    ROLE_EXISTED(7060, "角色已存在"),
    ROLE_NOT_FOUND(7061, "角色不存在"),
    ROLE_RELATED(7062, "请先解绑角色再修改绑定"),
    ROLE_RELATED_USER(7063, "请先解绑角色下的子用户再修改绑定"),
    ROLE_CAN_NOT_BE_OPERATING(7066, "修改平台角色不能小于用户所在项目拥有的角色"),
    ROLE_NOT_BINDING(7068, "当前用户未绑定角色"),
    ROLE_CUSTOM_CREAT_CLOSE(7070,"运营平台当前租户已关闭自定义角色"),

    /**
     * 分类
     */
    CATEGORY_EXISTED(7115, "分类已存在"),

    /**
     * 权限分组
     */
    PERMISSION_GROUP_EXISTED(7160, "权限分组已存在"),
    PERMISSION_GROUP_NOT_FOUND(7161, "权限分组不存在"),
    PERMISSION_GROUP_CAN_NOT_BE_OPERATING(7162, "此类别存在权限组，无法删除"),
    PERMISSION_GROUP_NOT_DELETE(7162, "内置权限组不允许删除"),

    /**
     * 权限点
     */
    PRIVILEGE_NOT_FOUND(7070, "权限点不存在"),
    PRIVILEGE_URL_NOT_NULL(7071, "url不能为空"),
    PRIVILEGE_URL_INVALID(7072, "权限点url格式不正确"),
    PRIVILEGE_CAN_NOT_OPERATE(7073, "不能操作权限点"),
    PRIVILEGE_EXISTED(7074, "权限点已存在"),
    PRIVILEGE_DUPLICATED_URL(7075, "url重复"),
    PRIVILEGE_HAS_CHILD_NODE(7076, "含有子节点"),
    PRIVILEGE_NOT_SELECT_SELF(7077, "父级菜单不能选择自己"),
    PRIVILEGE_NOT_SELECT_CHILD(7078, "父级菜单不能选择自己的子菜单"),
    PRIVILEGE_TOO_DEEP_LEVEL(7079, "父级菜单不能大于三级"),

    /**
     *
     */
    BAD_DOMAIN(7090, "Bad Domain"),
    PARAM_INVALID(7091, "参数不合法"),
    ACCESS_INVALID(7092, "无效的访问方式"),

    /**
     * 项目
     */
    PROJECT_OR_CODE_INVALID(7110, "项目ID和业务类型不能同时为空"),
    PROJECT_NOT_FOUND(7111, "项目不存在"),
    PROJECT_NO_SERVICE(7112, "没有开通服务"),

    /**
     * tag
     */
    TAG_INVALID(7114, "tagId不正确"),

    /**
     * 产品
     */
    PRODUCT_NOT_FOUND(7116, "产品不存在"),

    /**
     * IP
     */
    IP_CONFIG_INVALID(7120, "IP配置名单格式有误"),
    IP_CONFIG_EXISTED(7121, "IP网关配置已存在"),
    IP_CONFIG_NOT_FOUND(7122, "IP网关配置不存在"),
    FILE_INVALID(7123, "文件有误"),
    FILE_TYPE_INVALID(7124, "文件类型错误"),

    IP_BLOCK_LIST_CONFIG_INVALID(7125, "黑名单IP设置不能超过1000个"),
    IP_ALLOW_LIST_CONFIG_INVALID(7126, "白名单IP设置不能超过1000个"),
    IP_SEGMENT_BLOCK_LIST_CONFIG_INVALID(7127, "黑名单IP网段设置不能超过10个"),
    IP_SEGMENT_ALLOW_LIST_CONFIG_INVALID(7128, "白名单IP网段设置不能超过10个"),
    IP_DISJOINT_INVALID(7129, "该IP/该网段已经包含在黑名单/黑名单网段/白名单/白名单网段中，不允许添加"),

    /**
     * 权限点排序
     */
    PRIVILEGE_ORDER_TOP(7150, "数据已置顶"),
    PRIVILEGE_ORDER_BUTTOM(7151, "数据已置底"),
    PRIVILEGE_ORDER_UP(7152, "数据已无法上移"),
    PRIVILEGE_ORDER_DOWN(7153, "数据已无法下移"),


    INVALID_ACCESS(7160, "无效的访问方式"),

    NONCEID_IS_NULL(7170,"nonceId 不允许为空"),
    ;

    /**
     * 返回码
     */
    private int code;
    /**
     * 返回消息
     */
    private String message;
}
