package com.ksyun.common.enums;

/**
 * 环境枚举
 */
public enum GlobalConfigEnum {

    LOGIN_LOCK("login-lock", "loginLock"),
    ADMIN_LOCK("admin-lock", "adminLock"),
    NUMBER_LOCK("number-lock", "numberLock"),

    TIME_LOCK("time-lock", "timeLock"),
    PWD_VALID("pwd-valid", "pwdValid"),
    PWD_VALID_DAY("pwd-valid-day", "pwdValidDay"),

    SLEEP_VALID_DAY("sleep-valid-day", "sleepValidDay"),
    SLEEP_STATUS("sleep-status", "sleepStatus"),

    ACCESS_TIME_LIMIT("access-time-limit", "accessTimeLimit"),
    ACCESS_TIME_DAY_LIMIT("access-time-day-limit", "accessTimeDayLimit"),
    ACCESS_ROLE("access-role", "accessRole"),
    ACCESS_TIME_STATUS("access-time-status", "accessTimeStatus"),
    SESSION_CONFIG_SIZE("session-config-size", "sessionConfigSize");


    private String key;
    private String description;

    GlobalConfigEnum(String key, String description) {
        this.key = key;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {

        return description;
    }


    public static String getValue(String key) {
        GlobalConfigEnum[] businessModeEnums = values();
        for (GlobalConfigEnum businessModeEnum : businessModeEnums) {
            if (businessModeEnum.getKey().equals(key)) {
                return businessModeEnum.getDescription();
            }
        }
        return null;
    }

}
