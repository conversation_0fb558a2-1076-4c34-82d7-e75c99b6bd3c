package com.ksyun.common.constant;

import java.util.Collection;

/**
 * Http响应实体
 */
public class Response {

    public static final int CODE_SUCCESS = 200;
    public static final int CODE_FAILURE = 201;
    public static final int CODE_PRIVILEGE = 404;
    public static final String MESSAGE_SUCCESS = "成功";
    public static final String MESSAGE_FAILURE = "失败";
    public static final String MESSAGE_PRIVILEGE = "无权限访问此内容";

    private int status;
    private String message;
    private int code;
    private String trace;

    public Response(int status, int code, String message, String trace) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.trace = trace;
    }

    public Response(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public static class RichResponse<T> extends Response {
        private T result;

        public RichResponse(int status, String message, T result) {
            super(status, message);
            this.result = result;
        }


        public T getResult() {
            return result;
        }

        public void setResult(T result) {
            this.result = result;
        }

    }

    public static class OauthResponse<T> extends Response {
        private T data;

        public OauthResponse(int code, String message, T data) {
            super(code, message);
            this.data = data;
        }


        public T getData() {
            return data;
        }

        public void setData(T data) {
            this.data = data;
        }

    }

    public static class PageResponse<T> extends Response {
        private Collection<T> result;
        private PageNav page;

        public PageResponse(Page<T> result) {
            super(CODE_SUCCESS, MESSAGE_SUCCESS);
            this.result = result.result;
            this.page = result.page;
        }

        public PageResponse page(int pageNo, int pageSize, int totalSize) {
            this.page = new PageNav(pageNo, pageSize, totalSize);
            return this;
        }

        public Collection<T> getResult() {
            return result;
        }

        public void setResult(Collection<T> result) {
            this.result = result;
        }

        public PageNav getPage() {
            return page;
        }

        public void setPage(PageNav page) {
            this.page = page;
        }
    }

    public static class Page<T> {
        PageNav page;
        Collection<T> result;

        Page(Collection<T> result, int pageNo, int pageSize, int totalSize) {
            this.result = result;
            this.page = new PageNav(pageNo, pageSize, totalSize);
        }

        public PageNav getPage() {
            return page;
        }

        public void setPage(PageNav page) {
            this.page = page;
        }

        public Collection<T> getResult() {
            return result;
        }

        public void setResult(Collection<T> result) {
            this.result = result;
        }
    }

    public static class EnvResponse<T> extends Response {
        private T result;

        private T env;

        private T profile;

        public EnvResponse(int status, String message, T data, T env) {
            super(status, message);
            this.result = data;
            this.env = env;
        }

        public EnvResponse(int status, String message, T data, T env, T profile) {
            super(status, message);
            this.result = data;
            this.env = env;
            this.profile = profile;
        }

        public T getEnv() {
            return env;
        }

        public void setEnv(T env) {
            this.env = env;
        }

        public T getResult() {
            return result;
        }

        public void setResult(T result) {
            this.result = result;
        }

        public T getProfile() {
            return profile;
        }

        public void setProfile(T profile) {
            this.profile = profile;
        }
    }

    static class PageNav {
        int pageNo;
        int pageSize;
        int totalSize;
        int totalPage;

        public PageNav(int pageNo, int pageSize, int totalSize) {
            this.pageNo = pageNo;
            this.pageSize = pageSize;
            this.totalSize = totalSize;
            this.totalPage = totalSize % pageSize == 0 ? totalSize / pageSize : totalSize / pageSize + 1;
        }

        public int getPageNo() {
            return pageNo;
        }

        public void setPageNo(int pageNo) {
            this.pageNo = pageNo;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(int totalSize) {
            this.totalSize = totalSize;
        }

        public int getTotalPage() {
            return totalPage;
        }

        public void setTotalPage(int totalPage) {
            this.totalPage = totalPage;
        }
    }

    public static Response success() {
        return new Response(CODE_SUCCESS, MESSAGE_SUCCESS);
    }

    public static <T> RichResponse<T> success(T result) {
        return new RichResponse<>(CODE_SUCCESS, MESSAGE_SUCCESS, result);
    }

    public static <T> EnvResponse<T> success(T data, T env) {
        return new EnvResponse<>(CODE_SUCCESS, MESSAGE_SUCCESS, data, env);
    }

    public static <T> EnvResponse<T> success(T data, T env, T profile) {
        return new EnvResponse<>(CODE_SUCCESS, MESSAGE_SUCCESS, data, env, profile);
    }

    public static <T> PageResponse<T> success(Page<T> result) {
        return new PageResponse<>(result);
    }

    public static Response failure() {
        return new Response(CODE_FAILURE, 0, MESSAGE_FAILURE, "");
    }

    public static Response failure(String msg) {
        return new Response(CODE_FAILURE, 0, msg, "");
    }

    public static Response failure(int code, String msg, String trace) {
        return new Response(CODE_FAILURE, code, msg, trace);
    }

    public static Response failure(int status, int code, String msg, String trace) {
        return new Response(status, code, msg, trace);
    }

    public static <T> RichResponse<T> failure(T result) {
        return new RichResponse<>(CODE_FAILURE, MESSAGE_FAILURE, result);
    }

    public static <T> Response response(int code, String message, T result) {
        return new RichResponse<T>(code, message, result);
    }

    public static <T> Page<T> newPage(Collection<T> result, int pageNo, int pageSize, int totalSize) {
        return new Page<T>(result, pageNo, pageSize, totalSize);
    }

    public Response message(String message) {
        this.message = message;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
