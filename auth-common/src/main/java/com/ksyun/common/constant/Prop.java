package com.ksyun.common.constant;

import java.io.Serializable;
import java.util.Objects;

public class Prop implements Serializable {
    public static final String USER_PROP_KEY_PHONE = "phone";
    public static final String USER_PROP_KEY_EMAIL = "email";
    public static final String USER_PROP_KEY_ALIAS = "alias";
    public static final String USER_PROP_KEY_SECRET_LEVEL = "secret_level";
    public static final String USER_PROP_KEY_RESET_PASSWORD = "reset_password";
    public static final String USER_PROP_KEY_PASSPORT_ID = "passport_id";

    private String key;
    private String value;

    public Prop() {
    }

    public Prop(String key, String value) {
        this.key = key;
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Prop prop = (Prop) o;
        return Objects.equals(key, prop.key) && Objects.equals(value, prop.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(key, value);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
