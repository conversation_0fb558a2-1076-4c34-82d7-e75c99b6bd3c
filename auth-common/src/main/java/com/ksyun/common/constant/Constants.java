package com.ksyun.common.constant;

/**
 * @description:
 * @author: wuzhenliang
 * @date: 2020-06-19
 */
public class Constants {
    private Constants() {
    }

    /**
     * 用来标识去哪个数据源做权限校验
     */
    public static final String AUTH_TYPE = "Auth-Type";
    /**
     * 用来标识路由到哪个数据源
     */
    public static final String SYSTEM_TYPE = "System-Type";

    public static final String CODE_PREFIX = "code_";

    public static final String DAY_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String USER_AK = "userAk";
    public static final String USER_SK = "userSk";

    public static final String SSO_TOKEN_COOKIE_NAME = "sso-token";
    // cache prefix
    public static final String AUTH_LOGIN_USER_PREFIX = "auth_login_user_";

    // 越权访问 cache prefix
    public static final String BROKEN_ACCESS_CONTROL_PREFIX = "broken_access_control_";

    // 用户登录 SESSION 过期时间，单位秒，默认一小时
    public static final int SESSION_EXPIRE_SECONDS = 60 * 60;
    public static final String SUPERADMIN = "superadmin";

    public static final String PRIVILEGE_TEMPLATE_FILE_NAME = "template/privilege-import-template.xlsx";
    public static final String ROLE_TEMPLATE_FILE_NAME = "template/role-import-template.xlsx";
    public static final String ROLE_ERROR_TEMPLATE_FILE_NAME = "template/role-import-fail-template.xlsx";
    public static final String COMMA_CH = "；";
    public static final String COMMA_EN = ";";


    public static final String POSITION = "POSITION";
    public static final String HIDE = "HIDE";
    public static final String NET = "NET";
    public static final String SERVICE = "SERVICE";
    public static final String DEFAULT_MENU = "DEFAULT_MENU";
    public static final String ENUM = "ENUM";
    public static final String SYS_ENV = "SYS_ENV";
    public static final String LICENSE = "LICENSE";
    public static final String OPERATION = "OPERATION";


    public static final String PRIVILEGE_OVERWRITE_STRATEGY = "privilegeOverwriteStrategy";
    public static final String PRIVILEGE_IGNORE_STRATEGY = "privilegeIgnoreStrategy";
    /**
     * 权限更新策略
     */
    public static final String PRIVILEGE_UPDATE_STRATEGY = "privilegeUpdateStrategy";
    /**
     * 权限删除策略
     */
    public static final String PRIVILEGE_DELETE_STRATEGY = "privilegeDeleteStrategy";

    public static final String USER_PROP_KEY_PHONE = "phone";
    public static final String USER_PROP_KEY_EMAIL = "email";
    public static final String USER_PROP_KEY_ALIAS = "alias";
    public static final String USER_PROP_KEY_PWD = "password";

    public static final String USER_PROP_KEY_SECRET_LEVEL = "secret_level";
    public static final String USER_PROP_KEY_RESET_PWD = "reset_password";


    public static final String VALIDATE_NAME_REGEX = "^[\\u4e00-\\u9fffa-zA-Z0-9_-]{0,50}$";
    public static final String VALIDATE_NAME_REGEX_MESSAGE = "名称只能由中文、数字、字母、下划线、中划线组成且不能超过50个字符";
    public static final String VALIDATE_CODE_REGEX = "^[a-zA-Z0-9_-]{0,100}$";
    public static final String VALIDATE_CODE_REGEX_MESSAGE = "编码只能由字母、数字、下划线组成且不能超过100个字符";
    public static final String VALIDATE_DESC_MESSAGE = "支持任意字符，限制500个字符以内";

    public static final String USER_PROP_KEY_LOGIN_TIME = "login-time";

    public static final String USER_PROP_KEY_DEADLINE = "deadline";
    public static final String USER_PROP_KEY_DEADLINE_VALID_DAY = "deadline-valid-day";
    public static final String USER_PROP_KEY_PASSWORD_GENERATE_TIME = "password-generate-time";

    public static final int LOGIN_TYPE_TENANT = 0;

}
