package com.ksyun.common.constant;

import java.io.Serializable;
import java.util.Objects;

public class PropVoConstant implements Serializable {
    //全局设置属性
    public static final String USER_PROP_KEY_PWD_VALID = "pwd-valid";
    public static final String USER_PROP_KEY_PWD_VALID_DAY = "pwd-valid-day";

    public static final String USER_PROP_KEY_LOGIN_TIME = "login-time";
    public static final String USER_PROP_KEY_PWD_GENERATE_TIME = "password-generate-time";

    public static final String USER_PROP_KEY_DEADLINE = "deadline";
    public static final String USER_PROP_KEY_DEADLINE_VALID_DAY = "deadline-valid-day";

    public static final String USER_PROP_KEY_ACCESS_TIME_LIMIT = "access-time-limit";
    public static final String USER_PROP_KEY_ACCESS_TIME_DAY_LIMIT = "access-time-day-limit";
    public static final String USER_PROP_KEY_ACCESS_ROLE = "access-role";
    public static final String USER_PROP_KEY_ACCESS_TIME_STATUS = "access-time-status";

    private String key;
    private String value;


    public PropVoConstant(String key, String value) {
        this.key = key;
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        PropVoConstant propVo = (PropVoConstant) o;
        return Objects.equals(key, propVo.key) && Objects.equals(value, propVo.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(key, value);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "PropVo{" + "key='" + key + '\'' + ", value='" + value + '\'' + '}';
    }
}
