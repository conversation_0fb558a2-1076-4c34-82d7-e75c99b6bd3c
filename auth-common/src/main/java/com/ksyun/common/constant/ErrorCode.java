package com.ksyun.common.constant;

/**
 * 错误码常量类
 */
public class ErrorCode {
    /**
     * 通用错误码
     */
    public static final int ILLEGAL_USER = 201;        // 非法用户
    
    /**
     * 密钥相关错误码 (400-499)
     */
    public static final int KEY_NAME_EXISTS = 400;     // 密钥名称已存在
    public static final int INVALID_PUBLIC_KEY = 401;  // 无效的公钥格式
    public static final int NO_PERMISSION = 403;       // 无权操作此密钥
    public static final int KEY_NOT_FOUND = 404;       // 密钥不存在

    /**
     * 获取错误消息
     */
    public static String getMessage(int code) {
        switch (code) {
            case ILLEGAL_USER:
                return "非法用户";
            case KEY_NAME_EXISTS:
                return "密钥名称已存在";
            case INVALID_PUBLIC_KEY:
                return "公钥格式错误，必须是以'ssh-rsa'开头的SSH格式公钥";
            case NO_PERMISSION:
                return "无权操作此密钥";
            case KEY_NOT_FOUND:
                return "密钥不存在";
            default:
                return "未知错误";
        }
    }

    /**
     * 私有构造函数，防止实例化
     */
    private ErrorCode() {
    }
}
