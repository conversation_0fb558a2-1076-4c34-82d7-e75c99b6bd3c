package com.ksyun.common.constant;
import com.ksyun.common.entity.RoleUploadResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局变量
 */
public class GlobalVariable {

    public static Map<String, RoleUploadResponse> ROLE_UPLOAD_RESPONSE_MAP = new HashMap<String, RoleUploadResponse>();
    public static ThreadLocal<String> CURRENT_USERNAME = new ThreadLocal<>();
    public static ThreadLocal<String> CURRENT_UUID = new ThreadLocal<>();
    public static ThreadLocal<String> CURRENT_SYSTEM_TYPE = new ThreadLocal<>();

}
