package com.ksyun.common.exception;

import com.ksyun.common.enums.IExceptionResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020
 */
@Setter
@Getter
public class BaseException extends RuntimeException {

    int code;
    String message;
    Object args;

    public BaseException(Object args, String message) {
        super();
        this.message = message;
        this.args = args;
    }

    public BaseException(Object args, IExceptionResponse ex, String message) {
        super();
        this.code = ex.getCode();
        this.message = message;
        this.args = args;
    }

    public BaseException(Object args, Throwable e) {
        super();
        this.message = e.getMessage();
        this.args = args;
    }

    public BaseException(String message) {
        super();
        this.message = message;
    }

    public BaseException(Object args, String message, Throwable cause) {
        super(cause);
        this.message = message;
        this.args = args;
    }

    public BaseException(Object args, IExceptionResponse ex) {
        super();
        this.code = ex.getCode();
        this.message = ex.getMessage();
        this.args = args;
    }

    public BaseException(IExceptionResponse ex) {
        super();
        this.code = ex.getCode();
        this.message = ex.getMessage();
    }

    public BaseException(Object args, IExceptionResponse ex, Throwable cause) {
        super(cause);
        this.code = ex.getCode();
        this.message = ex.getMessage();
        this.args = args;
    }
}
