package com.ksyun.common.exception;


import com.ksyun.common.enums.IExceptionResponse;

/**
 * <AUTHOR>
 * @since 2020
 */
public abstract class Assert {

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     *
     * @param obj 待判断对象
     */
    public static void notNull(Object obj, String message) {
        if (obj == null) {
            throw new BusinessException(obj, message);
        }
    }

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     *
     * @param obj 待判断对象
     */
    public static void notNull(Object obj, IExceptionResponse enums) {
        if (obj == null) {
            throw new BusinessException(obj, enums);
        }
    }

    /**
     * <p>断言对象<code>obj</code>非空。如果对象<code>obj</code>为空，则抛出异常
     *
     * @param obj 待判断对象
     */
    public static void notNull(Object obj, IExceptionResponse enums, String message) {
        if (obj == null) {
            throw new BusinessException(obj, enums, message);
        }
    }

    /**
     * <p>断言对象<code>obj</code>为空。如果对象<code>obj</code>非空，则抛出异常
     *
     * @param obj 待判断对象
     */
    public static void isNull(Object obj, IExceptionResponse enums) {
        if (obj != null) {
            throw new BusinessException(obj, enums);
        }
    }

    /**
     * <p>断言对象<code>obj</code>为空。如果对象<code>obj</code>非空，则抛出异常
     *
     * @param obj 待判断对象
     */
    public static void isNull(Object obj, String message) {
        if (obj != null) {
            throw new BusinessException(obj, message);
        }
    }

    /**
     * <p>断言对象<code>obj</code>为真。如果对象<code>obj</code>为假，则抛出异常
     *
     * @param expression 表达式
     */
    public static void isTrue(boolean expression, IExceptionResponse enums) {
        if (!expression) {
            throw new BusinessException(enums);
        }
    }

    public static void isTrue(boolean expression, IExceptionResponse enums, String message) {
        if (!expression) {
            throw new BusinessException(null, enums, message);
        }
    }

    /**
     * <p>断言对象<code>obj</code>为真。如果对象<code>obj</code>为假，则抛出异常
     *
     * @param expression 表达式
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new BusinessException(message);
        }
    }

    public static void moreThanOneRole(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(message);
        }
    }
}
