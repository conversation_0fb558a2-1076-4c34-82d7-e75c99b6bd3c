package com.ksyun.common.exception;

import com.ksyun.common.enums.IExceptionResponse;

/**
 * <AUTHOR>
 * @since 2020
 */
public class BusinessException extends BaseException {

    private static final long serialVersionUID = 1L;

    public BusinessException(Object args, String message) {
        super(args, message);
    }

    public BusinessException(Object args, IExceptionResponse ex, String message) {
        super(args, ex, message);
    }

    public BusinessException(Object args, Throwable e) {
        super(args, e);
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(Object args, IExceptionResponse ex) {
        super(args, ex);
    }

    public BusinessException(IExceptionResponse ex) {
        super(ex);
    }

    public BusinessException(Object args, IExceptionResponse ex, Throwable cause) {
        super(args, ex, cause);
    }

    public BusinessException(Object args, String message, Throwable cause) {
        super(args, message, cause);
    }
}
