package com.ksyun.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @since 2020
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface Column {
    int index();

    String label();

    Type type() default Type.MAJOR;

    Strategy strategy();

    int length() default 0;

    boolean notNull() default false;

    enum Type {
        MAJOR,
        PROP
    }

    enum Strategy {
        NONE,
        ENUM,
        POSITION,
        HIDE,
        NET,
        SERVICE,
        DEFAULT_MENU,
        SYS_ENV,
        LICENSE,
        OPERATION
    }
}
