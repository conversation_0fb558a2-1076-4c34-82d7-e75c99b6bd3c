package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Role extends Model<Role> {

    private static final long serialVersionUID = 1L;

    public static final String TAG_ID_SEPERATOR = ";";

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色中文名
     */
    private String name;

    /**
     * 角色级别 1 平台级别 2 项目级别
     */
    private Integer type;

    /**
     * 角色标签
     */
    private String role;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色标签
     */
    private String tagIds;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 0 非默认 1默认
     */
    @TableField("`default`")
    private String defaultRole;

    /**
     * 角色所属平台，KCDE、HANHAI、KAS等
     */
    @TableField("`platform`")
    private String platform;

    private String source;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @TableField(exist = false)
    private List<String> tags;

    @TableField(exist = false)
    private Long userCount;

    public static String serializeTagIds(int[] tagIds) {
        if (tagIds.length == 0) {
            return null;
        }
        String str = Arrays.stream(tagIds).mapToObj(String::valueOf).collect(Collectors.joining(Role.TAG_ID_SEPERATOR));
        return str;
    }

    public static int[] deserializeTagIds(String tagIds) {
        if (StringUtils.isEmpty(tagIds)) {
            return new int[0];
        }
        String[] items = tagIds.split(Role.TAG_ID_SEPERATOR);
        return Arrays.stream(items).mapToInt(Integer::parseInt).toArray();
    }

    public boolean hasTag(int tagId) {
        int[] items = deserializeTagIds(tagIds);
        return Arrays.stream(items).anyMatch(item -> item == tagId);
    }

}
