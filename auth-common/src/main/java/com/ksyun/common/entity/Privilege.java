package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Privilege extends Model<Privilege> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限编码
     */
    private String code;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限类型 1 菜单 2 按钮
     */
    private Integer type;

    /**
     * icon名称
     */
    private String icon;

    /**
     * 顺序
     */
    @TableField("`order`")
    private Long order;

    /**
     * 父ID
     */
    private Long parentId;

    /**
     * 权限路径
     */
    private String url;

    /**
     * 业务类型
     */
    private String business;

    /**
     * ak
     */
    private String ak;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Override
    public boolean equals(Object obj) {
        if (this.getId().equals (((Privilege)obj).getId())) {
            return true;
        }
        return false;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
