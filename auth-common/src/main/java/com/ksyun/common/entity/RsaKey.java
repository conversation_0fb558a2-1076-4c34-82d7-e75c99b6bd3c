package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("rsa_keys")
public class RsaKey {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String name;
    
    private String type;
    
    @TableField(value = "public_key")
    private String publicKey;
    
    @TableField(exist = false)
    private String privateKey;
    
    private Integer keySize;
    
    private String userId;
    
    private Boolean isActive;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}
