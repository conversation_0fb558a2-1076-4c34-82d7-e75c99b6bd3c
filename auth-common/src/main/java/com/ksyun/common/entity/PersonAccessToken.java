package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 个人访问令牌实体类
 */
@Data
@TableName("person_access_token")
public class PersonAccessToken {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 令牌值
     */
    @TableField("token_value")
    private String tokenValue;

    /**
     * 有效期（秒）
     */
    @TableField("lifetime_seconds")
    private Long lifetimeSeconds;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private Date expiresAt;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;
}
