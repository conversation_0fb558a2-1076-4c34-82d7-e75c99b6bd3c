package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/12/7 4:16 下午
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BasicTags extends Model<BasicTags> {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 标签名
     */
    @TableField("`name`")
    private String name;

    /**
     * 标签key
     */
    @TableField("`key`")
    private String key;

    /**
     * 标签组
     */
    @TableField("`group`")
    private String group;

    /**
     * 适用角色类型 0：所有，1：平台角色 2：项目角色
     */
    private Integer roleTypeApply;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
