package com.ksyun.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PrivilegePermissionGroup extends Model<PrivilegePermissionGroup> {

    private static final long serialVersionUID = 1L;

    /**
     * 权限id
     */
    private Long privilegeId;

    /**
     * 权限分组id
     */
    private Long permissionGroupId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
