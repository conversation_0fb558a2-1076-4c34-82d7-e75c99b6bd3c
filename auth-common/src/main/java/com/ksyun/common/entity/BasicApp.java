package com.ksyun.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BasicApp extends Model<BasicApp> {

    private static final long serialVersionUID = 1L;

    /**
     * ak
     */
    private String ak;

    /**
     * 路径
     */
    private String contextPath;

    /**
     * 描述
     */
    private String description;

    /**
     * 产品code
     */
    private String productCode;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
