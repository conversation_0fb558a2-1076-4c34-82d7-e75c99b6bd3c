package com.ksyun.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GroupRole extends Model<GroupRole> {

    private static final long serialVersionUID = 1L;

    /**
     * 分组Id
     */
    private Long groupId;

    /**
     * 角色Id
     */
    private Long roleId;

    /**
     * 分组与角色的关联时间
     */
    private LocalDateTime createTime;


    @Override
    public Serializable pkVal() {
        return this.groupId;
    }

}
