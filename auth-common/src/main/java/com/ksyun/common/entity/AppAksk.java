package com.ksyun.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AppAksk extends Model<AppAksk> {

    private static final long serialVersionUID = 1L;

    /**
     * AK,
     */
    private String ak;

    /**
     * SK
     */
    private String sk;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    @Override
    public Serializable pkVal() {
        return this.ak;
    }

}
