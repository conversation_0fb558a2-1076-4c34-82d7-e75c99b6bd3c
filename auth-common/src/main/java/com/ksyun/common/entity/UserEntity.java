package com.ksyun.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ksyun.common.constant.PropVoConstant;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

@ToString
@Getter
@Setter
public class UserEntity implements Serializable {
    /**
     * 用户状态枚举
     */
    public enum Status {
        /**
         * 用户状态
         */
        NORMAL, DELETED, LOGOUT, SLEEP
    }

    /**
     * 用户来源枚举类
     */
    @Getter
    public enum UserSource {
        /**
         * 用户来源枚举
         */
        NLOCAL("NLOCAL"),
        LDAP("LDAP"),
        PASSPORT("PASSPORT"),
        CMGT("CMGT"),
        UASS("UASS"),
        SELF_UASS("SELF_UASS"),
        CAM("CAM");
        private String code;

        UserSource(String code) {
            this.code = code;
        }
    }

    @Setter
    @Getter
    public class InnerGroup {
        Long id;
        String name;
        LocalDateTime createTime;
        String description;
    }

    /**
     * 租户信息
     */
    @Setter
    @Getter
    public class InnerTenant {
        /**
         * 租户id
         */
        Long id;
        /**
         * 租户name
         */
        String name;
    }

    /**
     * 用户id
     */
    private Long id;
    /**
     * 用户name
     */
    private String name;
    /**
     * 用户状态
     */
    private Status status = Status.NORMAL;
    /**
     * 用户来源
     */
    private UserSource source;
    /**
     * 租户信息
     */
    private InnerTenant tenant;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 密级等级
     */
    private int secretLevel;
    private Collection<PropVoConstant> props = new LinkedList<>();
    /**
     * iam id
     */
    private String iamId;
    /**
     * 租户 true 默认
     * 子账号 false
     */
    private boolean tenantFlag = true;
    @JsonIgnore
    private List<InnerGroup> groups = new LinkedList<>();

    public static UserEntity newUser(Long id, Collection<PropVoConstant> propVos) {
        UserEntity user = new UserEntity();
        user.id = id;
        user.props = propVos;
        return user;
    }

    public void addOrUpdateProp(String key, String value) {
        Assert.isTrue(!StringUtils.isEmpty(key) && !StringUtils.isEmpty(value), "User Property[" + key + "] Cannot Be Empty");
        PropVoConstant target = new PropVoConstant(key, value);
        this.props.removeIf(prop -> Objects.equals(prop, target));
        this.props.add(target);
    }

    public void removeProp(String key, String value) {
        Assert.isTrue(!StringUtils.isEmpty(key) && !StringUtils.isEmpty(value), "User Property[" + key + "]  Cannot Be Empty");
        PropVoConstant target = new PropVoConstant(key, value);
        this.props.remove(target);
    }
}
