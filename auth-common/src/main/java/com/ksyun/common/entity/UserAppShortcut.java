package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/28 19:28
 */
@Data
public class UserAppShortcut extends Model<UserAppShortcut> {

    /**
     * 自增主键
     */
    @TableField(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer Id;

    /**
     *  应用 id
     */
    @TableField(value = "app_id")
    private Integer appId;
    /**
     * 应用 别名
     */
    @TableField(value = "alias")
    private String alias;
    /**
     * 创建者
     */
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}
