package com.ksyun.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PrivilegeProps extends Model<PrivilegeProps> {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    private Long privilegeId;

    /**
     * 权限属性KEY
     */
    private String propKey;

    /**
     * 权限属性值
     */
    private String propValue;


    @Override
    public Serializable pkVal() {
        return this.privilegeId;
    }

}
