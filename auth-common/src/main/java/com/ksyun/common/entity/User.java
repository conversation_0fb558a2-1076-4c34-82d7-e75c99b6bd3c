package com.ksyun.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class User extends Model<User> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String name;
    /**
     * 用户来源（LOCAL,UASS,CAM...）
     */
    private String source;

//    /**
//     * 所属租户ID
//     */
//    private Long tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 数据状态，做逻辑删除时使用
     */
    private String status;

    /**
     * 统一认证平台id
     */
    private String oidcId;

    /**
     * 备注
     */
    private String remark;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
