package com.ksyun.common.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class TimestampCache<TKey, TValue> {
    private long maxMills;
    private Map<TKey, CacheValue> items = new ConcurrentHashMap<>();

    public TimestampCache(int second) {
        this.maxMills = second * 1000;
    }

    public void put(TKey key, TValue value) {
        items.put(key, new CacheValue<>(value, System.currentTimeMillis()));
    }

    public TValue get(TKey key) {
        CacheValue<TValue> cacheValue = items.get(key);
        if (cacheValue == null) {
            return null;
        }
        if (System.currentTimeMillis() - cacheValue.getTimestamp() > maxMills) {
            items.remove(key);
            return null;
        }
        return cacheValue.getValue();
    }

    public static class CacheValue<TValue> {
        private TValue value;
        private long timestamp;

        private CacheValue(TValue value, long timestamp) {
            this.value = value;
            this.timestamp = timestamp;
        }

        public TValue getValue() {
            return value;
        }

        private long getTimestamp() {
            return timestamp;
        }
    }

    public static void main(String[] args) {
        String key = "1_KDQ";
        TimestampCache<String,Integer> dmVersion = new TimestampCache<>(10);
        dmVersion.put(key,2);
        System.out.println(dmVersion.get(key));
        System.out.println(dmVersion.get(key));
        System.out.println(dmVersion.get(key));
        System.out.println(dmVersion.get(key));
        System.out.println(dmVersion.get(key));
        System.out.println(dmVersion.get(key));
        System.out.println(dmVersion.get(key));
    }
}
