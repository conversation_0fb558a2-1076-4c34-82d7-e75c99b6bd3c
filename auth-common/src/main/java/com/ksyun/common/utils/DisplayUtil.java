package com.ksyun.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 敏感信息掩码规则<br>
 * @date 2021/6/25 11:42
 */
public class DisplayUtil {

    /**
     * 手机号显示首3末4位，中间用*号隐藏代替，如：138****4213
     *
     * @param mobile
     * @return
     */
    public static String displayMobile(String mobile) {
        if(StringUtils.length(mobile) <= 8) {
            return mobile;
        }
        try {
            return wordMask(mobile, 3, 4, "*");
        } catch (Exception e) {
            return StringUtils.repeat("*", StringUtils.length(mobile));
        }
    }

    /**
     * 邮箱像是前两位及最后一位字符，及@后邮箱域名信息，如：ye****<EMAIL>
     *
     * @param email
     * @return
     */
    public static String displayEmail(String email) {
        if(StringUtils.isEmpty(email)) {
            return email;
        }
        try {
            String[] temp = email.split("@");
            return wordMask(temp[0], 1, 1, "*") + "@" + temp[1];
        } catch (Exception e) {
            return StringUtils.repeat("*", StringUtils.length(email));
        }
    }

    /**
     * 对字符串进行脱敏处理 --
     *
     * @param word 被脱敏的字符
     * @param startLength 被保留的开始长度 前余n位
     * @param endLength 被保留的结束长度 后余n位
     * @param pad 填充字符
     * */
    private static String wordMask(String word,int startLength ,int endLength,String pad){
        if (startLength + endLength > word.length()) {
            return StringUtils.leftPad("", word.length() - 1, pad);
        }
        String startStr = word.substring(0, startLength);
        String endStr = word.substring(word.length() - endLength, word.length());
        return startStr + StringUtils.leftPad("", word.length() - startLength - endLength, pad) + endStr;
    }
}
