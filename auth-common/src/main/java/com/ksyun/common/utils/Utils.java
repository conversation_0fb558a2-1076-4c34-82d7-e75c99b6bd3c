package com.ksyun.common.utils;
import com.ksyun.common.constant.PropVoConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

import jakarta.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/12/24 11:38
 */
public class Utils {

    public static ImmutablePair<String, String> parse(String url) {
        int i = url.length() - 1;
        while (i > 0) {
            if (url.charAt(i) == '(') {
                break;
            }
            i--;
        }
        String urlPart = url.substring(0, i);
        String methodPart = url.substring(i + 1, url.length() - 1);

        return new ImmutablePair<>(urlPart, methodPart);
    }

    public static boolean isEmail(String email){
        String regex = "^$|([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(email).matches();
    }

    public static boolean isRequirePassword(String password){
        String regex = "(^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,32}$)";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(password).matches();
    }

    public static boolean isPhone(String phone){
        String regex = "^1[3456789]\\d{9}$";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(phone).matches();
    }

    public static boolean judgeIntersection(Collection<Integer> list1, List<Integer> list2) {
        boolean flag = false;
        // 使用retainAll会改变list1的值，所以写一个替代
        List<Integer> origin = new ArrayList<>(list1);
        origin.retainAll(list2);
        // 有交集
        if (origin.size() > 0) {
            flag = true;
        }
        return flag;
    }

    public static List<String> accessTimeConfigEnum() {
        return Arrays.asList(PropVoConstant.USER_PROP_KEY_ACCESS_TIME_LIMIT, PropVoConstant.USER_PROP_KEY_ACCESS_TIME_STATUS, PropVoConstant.USER_PROP_KEY_ACCESS_TIME_DAY_LIMIT, PropVoConstant.USER_PROP_KEY_ACCESS_ROLE);
    }

    public static boolean isInIpConfig(String ip, String ipConfigStr, String ipSegment) {
        if (StringUtils.isNotEmpty(ipConfigStr)) {
            ipConfigStr = ipConfigStr.replace(" ", "");
            String[] ipConfigList = ipConfigStr.split(",");
            for (String oneIpConfig : ipConfigList) {
                if (oneIpConfig.contains("/")) {
                    if (IpCheckUtils.isInRange(ip, oneIpConfig)) {
                        return true;
                    }
                } else {
                    if (ip.equals(oneIpConfig)) {
                        return true;
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(ipSegment)) {
            String[] ipSegs = ipSegment.split("-");
            if (ipSegs.length == 2) {
                String[] ipSeg = ip.split("\\.");
                long dispIp = NumberUtils.toLong("1" + StringUtils.leftPad(ipSeg[0], 3, '0')
                        + StringUtils.leftPad(ipSeg[1], 3, '0')
                        + StringUtils.leftPad(ipSeg[2], 3, '0')
                        + StringUtils.leftPad(ipSeg[3], 3, '0'));

                ipSeg = ipSegs[0].split("\\.");
                long ips = NumberUtils.toLong("1" + StringUtils.leftPad(ipSeg[0], 3, '0')
                        + StringUtils.leftPad(ipSeg[1], 3, '0')
                        + StringUtils.leftPad(ipSeg[2], 3, '0')
                        + StringUtils.leftPad(ipSeg[3], 3, '0'));

                ipSeg = ipSegs[1].split("\\.");
                long ipe = NumberUtils.toLong("1" + StringUtils.leftPad(ipSeg[0], 3, '0')
                        + StringUtils.leftPad(ipSeg[1], 3, '0')
                        + StringUtils.leftPad(ipSeg[2], 3, '0')
                        + StringUtils.leftPad(ipSeg[3], 3, '0')
                );
                if ((dispIp >= ips && dispIp <= ipe) || dispIp >= ipe && dispIp <= ips) {
                    return true;
                }
            }
        }
        return false;
    }

    public static Map<String, String> getAllHeaderIps(HttpServletRequest request) {
        Map<String, String> allHeaderIps = new HashMap<>();
        String finallIp = null;

        String ipAddresses = request.getHeader("X-Real-IP");
        //X-Forwarded-For：Squid 服务代理
        if (ipAddresses == null || ipAddresses.length() == 0 || Objects.equals("unknown", ipAddresses)) {
            ipAddresses = request.getHeader("X-Forwarded-For");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //Proxy-Client-IP：apache 服务代理
            ipAddresses = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //WL-Proxy-Client-IP：weblogic 服务代理
            ipAddresses = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //HTTP_CLIENT_IP：有些代理服务器
            ipAddresses = request.getHeader("HTTP_CLIENT_IP");
        }

        //有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
        if (ipAddresses != null && ipAddresses.length() != 0) {
            finallIp = ipAddresses.split(",")[0];
        }
        //还是不能获取到，最后再通过request.getRemoteAddr();获取
        if (finallIp == null || finallIp.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            finallIp = request.getRemoteAddr();
        }
        allHeaderIps.put("IS-APP", request.getHeader("IS-APP"));

        allHeaderIps.put("X-Real-IP", request.getHeader("X-Real-IP"));
        allHeaderIps.put("X-Forwarded-For", request.getHeader("X-Forwarded-For"));
        allHeaderIps.put("Proxy-Client-IP", request.getHeader("Proxy-Client-IP"));
        allHeaderIps.put("WL-Proxy-Client-IP", request.getHeader("WL-Proxy-Client-IP"));
        allHeaderIps.put("HTTP_CLIENT_IP", request.getHeader("HTTP_CLIENT_IP"));
        allHeaderIps.put("Remote-Addr", request.getRemoteAddr());

        allHeaderIps.put("final-ipaddress", finallIp);
        return allHeaderIps;
    }


}
