package com.ksyun.common.utils;

import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @date 2019/12/24 11:38
 */
public class Md5Utils {

    private static final String LOGIN_CACHE_PREFIX = "login_cache_";

    private static String[] hexDigits = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    public static String encodeByMD5(String originStr) {
        if (originStr != null) {
            try {
                // 创建具有指定算法名称的信息摘要
                MessageDigest md = MessageDigest.getInstance("MD5");
                // 使用指定的字节数组对摘要进行最后的更新，然后完成摘要计算
                byte[] results = md.digest(originStr.getBytes());
                // 将得到的字节数组编程字符串返回
                String resultString = byteArrayToHexString(results);
                return resultString.toUpperCase();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }

    // 转换字节数组为十六进制字符串
    public static String byteArrayToHexString(byte[] b) {
        StringBuilder result = new StringBuilder();
        int i;
        for (i = 0; i < b.length; i++) {
            result.append(byteToHexString(b[i]));
        }
        return result.toString();
    }

    // 将字节转化成十六进制的字符串
    public static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n / 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    // 将字节转化成十六进制的字符串
    public static String getLoginCacheByIdAndName(Long id, String name) {
        return Md5Utils.encodeByMD5(LOGIN_CACHE_PREFIX.concat(String.valueOf(id)).concat(name));
    }


}
