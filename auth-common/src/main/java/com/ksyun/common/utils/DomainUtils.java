package com.ksyun.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: wuzhenliang
 * @date: 2020-05-20
 */
@Slf4j
public class DomainUtils {

    public static String getLogoutDomain(HttpServletRequest request, String logoutDomain) {
        if (!StringUtils.isEmpty(logoutDomain)) {
            return logoutDomain;
        }
        return getDomain(request.getRequestURL().toString());
    }

    public static String getDomain(HttpServletRequest request, String domain, String type) {
        if (!StringUtils.isEmpty(domain) && Objects.equals(type, "1")) {
            return domain;
        }
        return getDomain(request.getRequestURL().toString());
    }

    private static String getDomain(String url) {
        try {
            String host = new URL(url).getHost();
            log.info("Auto Get Domain from host: host={}", host);

            String[] domainArr = host.split("\\.");
            Assert.isTrue(domainArr.length >= 2, "");
            Assert.isTrue(!domainArr[domainArr.length - 1].matches("\\d+"), "Bad Domain: " + host);
            Assert.isTrue(!domainArr[domainArr.length - 2].matches("\\d+"), "Bad Domain: " + host);
            return String.format(".%s.%s", domainArr[domainArr.length - 2], domainArr[domainArr.length - 1]);
        } catch (Exception e) {
            log.error("获取domain异常!", e);
            return "";
        }
    }

}
