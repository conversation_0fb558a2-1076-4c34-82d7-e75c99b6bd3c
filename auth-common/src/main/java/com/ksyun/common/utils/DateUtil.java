package com.ksyun.common.utils;

import com.ksyun.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020
 */
public class DateUtil {

    /**
     * convert java.time.LocalDate to java.util.Date
     *
     * @param localDate
     * @return
     */
    public static Date localDate2Date(LocalDate localDate) {
        if (null == localDate) {
            return null;
        }
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * convert java.time.LocalDateTime to java.util.Date
     *
     * @param localDateTime
     * @return
     */
    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        Date date = Date.from(zdt.toInstant());
        return date;
    }

    /**
     * convert java.util.Date to java.time.LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime date2LocalDateTime(Date date) {
        if (null == date) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
    }

    /**
     * convert java.util.Date to java.time.LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate date2LocalDate(Date date) {
        if (null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static long getTime(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return 0L;
        }
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static String toDefaultString(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 日期字符串转换成日期格式
     *
     * @param dateTime
     * @return java.util.Date
     * <AUTHOR>
     * @date 2020/8/25 10:30
     **/
    public static Date toDefaultDate(String dateTime) {
        Date date = null;
        try {
            if (StringUtils.isNotBlank(dateTime)) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                date = format.parse(dateTime);
            }
        } catch (ParseException e) {
            throw new BusinessException("日期格式错误");
        }
        return date;
    }

    public static String getDateByFormat(String format) {
        if(StringUtils.isEmpty(format)){
            format = "yyyyMMddHHmmS0";
        }
        String date = new SimpleDateFormat(format).format(new Date());
        return date;
    }



    /**
     * 判断当前日期是否小于某个日期+指定天数
     *
     * @param date yyyy-MM-dd
     */
    public static boolean afterDate(String date, Integer pwdValidDay) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //把String转为LocalDate
        LocalDateTime localDateTime = LocalDateTime.parse(date, dtf).plusDays(pwdValidDay == null ? 90 : pwdValidDay);
        //判断当前日期是否大于指定日期
        return LocalDateTime.now().isAfter(localDateTime);
    }

    /**
     * @param startTime 数据库中申请开始时间
     * @param endTime   数据库中结束时间
     * @param now       当前时间
     */
    public static boolean compare(String startTime, String endTime, Date now, String format1) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format1);
            String format = sdf.format(now);
            //将字符串形式的时间转化为Date类型的时间
            Date a = sdf.parse(startTime);
            Date b = sdf.parse(endTime);
            Date c = sdf.parse(format);
            if (a.getTime() == c.getTime() || b.getTime() == c.getTime()) {
                return true;
            }
            //当前申请时间在已经预约申请开始时间后面
            return c.after(a) && c.before(b);
        } catch (ParseException e) {
            return false;
        }
    }


}
