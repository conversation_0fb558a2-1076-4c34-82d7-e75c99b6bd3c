package com.ksyun.common.utils;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 验证码工具类
 */
public class CodeUtils {
    /**
     * 随机生成一个包含大小写字母和数字的字符串
     * <p>
     *     字符串长度至少为4位；不允许纯英文和纯数字的字符串
     * </p>
     * @param kaptchaCharLength 字符串长度
     * @return
     */
    public static String getRandomString(int kaptchaCharLength) {
        if (kaptchaCharLength < 4) {
            kaptchaCharLength = 4;
        }
        while (true) {
            String kaptcha = RandomStringUtils.random(kaptchaCharLength, true, true);
            if (StringUtils.isAlpha(kaptcha) || StringUtils.isNumeric(kaptcha)) {
                continue;
            }
            return kaptcha;
        }
    }

    /**
     * 国网需求：登录成功后重置SessionId.
     * @param request 请求.
     */
    public static void rewriteSessionId(HttpServletRequest request) {
        HttpSession session = request.getSession();
        Map<String, Object> tempMap = new HashMap();
        if (session != null && !session.isNew()) {
            //首先将原session中的数据转移至一临时map中
            Enumeration<String> sessionNames = session.getAttributeNames();
            while (sessionNames.hasMoreElements()) {
                String sessionName = sessionNames.nextElement();
                tempMap.put(sessionName, session.getAttribute(sessionName));
            }
            //注销原session，为的是重置sessionId
            session.invalidate();
        }
        //将临时map中的数据转移至新session
        session = request.getSession();
        for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
            session.setAttribute(entry.getKey(), entry.getValue());
        }
    }

    public static String generateToken(Long id, String name) {
        String md5LoginCachePrefix = Md5Utils.getLoginCacheByIdAndName(id, name);
        String token = md5LoginCachePrefix.concat("-").concat(UUID.randomUUID().toString().replace("-", ""));
        return token;
    }
}
