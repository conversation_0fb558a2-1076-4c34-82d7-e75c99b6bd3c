package com.ksyun.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class StringEscapeUtils {

    private StringEscapeUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * mysql查询转义通配符 %和_
     *
     * @param s 需要转义的参数
     * @return 转义字符后的值
     */
    public static String escapeSqlQueryStr(String s) {
        if (StringUtils.isEmpty(s)) {
            return s;
        }
        if (s.contains("%") || s.contains("_")) {
            s = s.replace("%", "\\%");
            s = s.replace("_", "\\_");
        }
        return s;
    }
}
