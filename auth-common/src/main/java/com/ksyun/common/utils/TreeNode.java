package com.ksyun.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2019/11/27 11:36
 */

public class TreeNode<T> {
    private T data;
    private TreeNode<T> parent;
    private List<TreeNode<T>> children;

    public TreeNode(T data) {
        this.data = data;
        this.children = new ArrayList<>();
    }

    public static void main(String[] args) {
        TreeNode<String> root = new TreeNode<String>("0");
        TreeNode na = new TreeNode("a");
        TreeNode na1 = new TreeNode("a1");
        TreeNode na2 = new TreeNode("a2");
        na.addChild(na1);
        na.addChild(na2);
        TreeNode nb = new TreeNode("b");
        TreeNode nc = new TreeNode("c");
        root.addChild(na);
        root.addChild(nb);
        root.addChild(nc);

        root.printTree(item -> item);
    }

    public void addChild(TreeNode<T> childNode) {
        childNode.parent = this;
        this.children.add(childNode);
    }

    public int getLevel() {
        if (parent == null) {
            return 0;
        }
        return parent.getLevel() + 1;
    }

    public TreeNode<T> searchNode(T data) {
        if (this.data.equals(data)) {
            return this;
        }
        for (TreeNode node : children) {
            if (node.data.equals(data)) {
                return node;
            }
            TreeNode targetNode = node.searchNode(data);
            if (targetNode != null) {
                return targetNode;
            }
        }
        return null;
    }

    public List<TreeNode<T>> getChildren() {
        return children;
    }

    public T getData() {
        return data;
    }

    @Override
    public String toString() {
        return new String(new char[getLevel()]).replace("\0", "-") + data;
    }

    public void printTree(Function<? super T, String> mapper) {
        String str = new String(new char[getLevel()]).replace("\0", "-") + mapper.apply(this.getData());
        System.out.println(str);
        for (TreeNode<T> node : children) {
            node.printTree(mapper);
        }
    }


    public boolean containsNode(Predicate<? super T> predicate) {
        if (predicate.test(data)) {
            return true;
        }
        for (TreeNode<T> node : children) {
            if (node.containsNode(predicate)) {
                return true;
            }
        }
        return false;
    }

}
