package com.ksyun.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.util.regex.Pattern;

public class IpCheckUtils {
    private static Logger logger = LoggerFactory.getLogger(IpCheckUtils.class);

    private static final String IP_UTILS_FLAG = ",";
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IP = "0:0:0:0:0:0:0:1";
    private static final String LOCALHOST_IP1 = "127.0.0.1";

    /**
     * 功能：判断一个IP是不是在一个网段下的
     * 格式：isInRange("***********", "************/22");
     */
    public static boolean isInRange(String ip, String cidr) {
        String[] ips = ip.split("\\.");
        int ipAddr = (Integer.parseInt(ips[0]) << 24)
                | (Integer.parseInt(ips[1]) << 16)
                | (Integer.parseInt(ips[2]) << 8) | Integer.parseInt(ips[3]);
        int type = Integer.parseInt(cidr.replaceAll(".*/", ""));
        int mask = 0xFFFFFFFF << (32 - type);
        String cidrIp = cidr.replaceAll("/.*", "");
        String[] cidrIps = cidrIp.split("\\.");
        int cidrIpAddr = (Integer.parseInt(cidrIps[0]) << 24)
                | (Integer.parseInt(cidrIps[1]) << 16)
                | (Integer.parseInt(cidrIps[2]) << 8)
                | Integer.parseInt(cidrIps[3]);

        return (ipAddr & mask) == (cidrIpAddr & mask);
    }

    /**
     * 功能：判断ip格式是否合法
     * 格式：isIP("*************")
     */
    public static boolean isIP(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        String regex = "^(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}$";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(str).matches();
    }

    /**
     * 获取IP地址
     * <p>
     * 使用Nginx等反向代理软件， 则不能通过request.getRemoteAddr()获取IP地址
     * 如果使用了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP地址，X-Forwarded-For中第一个非unknown的有效IP字符串，则为真实IP地址
     */
    public static String getClientIpAddr(HttpServletRequest request) {
        logger.info("getClientIpAddr X-Real-IP={}, X-Original-Forwarded-For={},x-forwarded-for={},X-Forwarded-For={},Proxy-Client-IP={},WL-Proxy-Client-IP={},HTTP_CLIENT_IP={},HTTP_X_FORWARDED_FOR={},RemoteAddr={}",
                request.getHeader("X-Real-IP"),
                request.getHeader("X-Original-Forwarded-For"),
                request.getHeader("X-Forwarded-For"),
                request.getHeader("x-forwarded-for"),
                request.getHeader("Proxy-Client-IP"),
                request.getHeader("WL-Proxy-Client-IP"),
                request.getHeader("HTTP_CLIENT_IP"),
                request.getHeader("HTTP_X_FORWARDED_FOR"),
                request.getRemoteAddr()
        );
        String ip = null;
        try {
            //以下两个获取在k8s中，将真实的客户端IP，放到了x-Original-Forwarded-For。而将WAF的回源地址放到了 x-Forwarded-For了。
            ip = getFirstUnknownIp(request.getHeader("X-Original-Forwarded-For"));
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getHeader("X-Forwarded-For"));
            }
            //获取nginx等代理的ip
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getHeader("x-forwarded-for"));
            }
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getHeader("Proxy-Client-IP"));
            }
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getHeader("WL-Proxy-Client-IP"));
            }
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getHeader("HTTP_CLIENT_IP"));
            }
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getHeader("HTTP_X_FORWARDED_FOR"));
            }
            //兼容k8s集群获取ip
            if (StringUtils.isEmpty(ip)) {
                ip = getFirstUnknownIp(request.getRemoteAddr());
                if (LOCALHOST_IP1.equalsIgnoreCase(ip) || LOCALHOST_IP.equalsIgnoreCase(ip)) {
                    //根据网卡取本机配置的IP
                    ip = getFirstUnknownIp(InetAddress.getLocalHost().getHostAddress());
                }
            }
        } catch (Exception e) {
            logger.error("getClientIpAddr ERROR ", e);
        }
        return ip;
    }

    private static String getFirstUnknownIp(String ip) {
        if (StringUtils.isNotEmpty(ip)) {
            //StringUtils.split(ip, IP_UTILS_FLAG).
            for (String s : StringUtils.split(ip, IP_UTILS_FLAG)) {
                if (StringUtils.isNotEmpty(s.trim()) && !UNKNOWN.equalsIgnoreCase(StringUtils.trim(s))) {
                    return s.trim();
                }
            }
        }
        return null;
    }
}
