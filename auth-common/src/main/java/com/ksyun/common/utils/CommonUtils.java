package com.ksyun.common.utils;

import jakarta.servlet.http.Cookie;

/**
 * <AUTHOR>
 * @Mail <EMAIL>
 * @Date 2019-04-02 19:21
 **/
public class CommonUtils {

    /**
     * 创建一个 Jo<PERSON>, 确保只读
     *
     * @param key   Cookie 名
     * @param value Cookie 值
     */
    public static jodd.http.Cookie newJoddCookie(String key, String value) {
        jodd.http.Cookie cookie = new jodd.http.Cookie(key, value);
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        return cookie;
    }

    /**
     * java cookie 转成 jodd cookie
     */
    public static jodd.http.Cookie javaCookie2JoddCookie(Cookie cookie) {
        return newJoddCookie(cookie.getName(), cookie.getValue());
    }

}
