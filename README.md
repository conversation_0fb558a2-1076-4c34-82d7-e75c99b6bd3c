# 公共组件服务介绍说明

## 一、模块划分
### auth-common
##### 公共模块，主要包括有Annotation注解、Enums枚举、Entity实体、Constant常量、Utils工具类等

### auth-service
##### 公共服务类，主要包括有Dao接口、Mapper文件、Service服务、Vo出入参转换、Dto复杂出参转换、Utils服务工具类

### auth-client
#### 认证客户端，主要包括Interceptor认证拦截器、Processor处理器、认证注解等等

### auth-privilege
#### 认证权限模块，主要包括查询权限API

### auth-server
#### 认证服务中心，主要包括认证服务API

## 二、构建说明
#### 可使用命令  mvn clean package -Dmaven.test.skip=true 进行构建。

## 三、部署说明
#### 将构建完成的jar，auth-privilege/target/auth-privilege-{版本号}.jar和auth-server/target/auth-server-{版本号}.jar分别进行部署，部署完成后通过执行 ssh bin/app.sh 进行启动。

    备注：app.sh需要放到bin文件中，jar放到lib文件中，配置文件放到config文件中，具体可参考下图；
![img.png](img.png)

## 四、命名规范
### controller 控制层
##### 1、总入口
     /api/模块名 遵循Restful 风格
##### 2、方法名
    /page        查询分页数据
    /get/{id}    查询单个记录
    /getXXX      查询某个特定条件       
    /add         添加
    /update      更新
    /delete      删除
    /delete/{id} 删除单个记录
    /updateXXX   更新-xxx,可按业务调整
    /addXXX      添加-xxx,可按业务调整
### service  服务层
    page();       查询分页数据
    get();        查询单个数据
    getXXX();     查询某个条件数据
    add();        添加数据
    addXXX();      添加-xxx
    delete();     删除数据
    deleteById(); 删除某个id数据
    update();     更新数据
    updateXXX()；  更新-xxx
### vo 出入参转换层（禁止继承Entity）
    XxxVo 一般Entiy最多4个Vo；例如
    UserBaseVo                基础Vo,供继承使用
    UserVo\UserAddOrUpdateVo  新增和修改Vo
    UserDeleteVo              删除Vo
    
### dto 复杂出参转换层（否则可以直接使用Vo）
    XxxDto 根据不同的业务进行制定，一般dto会直接继承对应的Vo,例如：
    UserDto
