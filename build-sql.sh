#!/bin/bash
#脚本错误直接退出
set -o errexit
set -o pipefail

BASE_PATH="$(dirname $0)"
version=$1

echo "打包chart..."
tar czf authetication-deploy.tgz -C auth-server/src/main/charts .
mkdir -p package-build/target/article/authetication-deploy/package
mv authetication-deploy.tgz package-build/target/article/authetication-deploy/package

src_path="./sql/kcde/v${version}"
mkdir -p ./package-build/target/article/authetication-deploy
article_path="./package-build/target/article/authetication-deploy/sql/dl/${version}/"

if test -e ${src_path}; then
   rm -rf ${article_path}
   mkdir -p ${article_path}
   echo "======Copy sql to article======"
   echo "cp -r ${src_path}/* ${article_path}"
   cp -r ${src_path}/* ${article_path}
else
   echo "======Current version's sql is not exists, skip!======"
fi
