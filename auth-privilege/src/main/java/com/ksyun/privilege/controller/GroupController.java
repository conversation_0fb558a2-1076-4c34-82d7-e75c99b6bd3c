package com.ksyun.privilege.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthApp;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.service.GroupService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Response;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.exception.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.ksyun.auth.client.authentication.Authentication.ARAN;

/**
 * 群组Controller
 */
@RestController
@RequestMapping("/api/groups")
@Authorize(Authorize.Type.PLATFORM)
@Slf4j
public class GroupController extends BaseController {

    @Autowired
    private GroupService groupService;

    @Autowired
    private UserService userService;

    /**
     * 创建群组
     *
     * @param parameter GroupAddVo
     * @param authentication AuthUser
     * @return
     */
    @PostMapping()
    public Response add(@RequestBody @Validated GroupAddVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        log.info("Received Request Add Group: groupName={}", parameter.getName());
        Assert.isTrue(!Objects.equals(parameter.getName().trim(), ""), BusinessExceptionEnum.GROUP_NAME_NOT_EMPTY);
        parameter.setTenantId(0L);
        parameter.setCreateBy(authentication.getId());
        parameter.setUpdateBy(authentication.getId());
        groupService.addGroup(parameter);
        return Response.success();
    }

    /**
     * 更新群组
     *
     * @param parameter GroupUpdateVo
     * @param authentication AuthUser
     * @return
     */
    @Authorize(Authorize.Type.NONE)
    @PutMapping()
    public Response update(@RequestBody @Validated GroupUpdateVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        log.info("Received Request Update Group: groupId={}, groupName={}", parameter.getId(), parameter.getName());
        parameter.setTenantId(0L);
        parameter.setUpdateBy(authentication.getId());
        groupService.update(parameter);
        return Response.success();
    }

    /**
     * 删除群组
     *
     * @param groupId groupId
     * @param authentication AuthUser
     * @return
     */
    @DeleteMapping(value = "/{groupId}")
    public Response delete(@PathVariable Long groupId, @RequestAttribute(ARAN) AuthUser authentication) {
        log.info("Received Request Delete Group: groupId={}", groupId);
        groupService.delete(0L, groupId);
        return Response.success();
    }

    /**
     * 用户绑定群组
     *
     * @param parameter UserGroupBindVo
     * @param authentication AuthUser
     * @return
     */
    @PostMapping(value = "/bind")
    public Response bind(@RequestBody @Validated UserGroupBindVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        log.info("Bind Received Request Bind Group: users={}, groups={}", parameter.getUserIds(), parameter.getGroupIds());
        parameter.setTenantId(0L);
        groupService.addBind(parameter);
        return Response.success();
    }

    /**
     * 用户绑定群组
     *
     * @param userGroupBindVo UserGroupBindVo
     * @param authentication AuthUser
     * @return
     */
    @PostMapping(value = "/userBind")
    @Authorize(Authorize.Type.NONE)
    public Response userBind(@RequestBody @Validated UserGroupBindVo userGroupBindVo, @RequestAttribute(ARAN) AuthUser authentication) {
        Assert.isTrue(userGroupBindVo.getGroupId() != null, "群组id不能为空");
        Assert.isTrue(!userGroupBindVo.getUserIds().isEmpty(), "用户id不能为空");
        log.info("userBind Received Request Bind Group: users={}, groups={}", userGroupBindVo.getUserIds(), userGroupBindVo.getGroupId());
        groupService.addUserBind(0L, userGroupBindVo.getGroupId(), userGroupBindVo.getUserIds());
        return Response.success();
    }

    /**
     * 用户群组批量绑定
     * @param userGroupBindVo UserGroupBindVo
     * @param authentication AuthUser
     * @return
     */
    @PostMapping(value = "/groupBind")
    @Authorize(Authorize.Type.NONE)
    public Response groupBind(@RequestBody @Validated UserGroupBindVo userGroupBindVo, @RequestAttribute(ARAN) AuthUser authentication) {
        Assert.isTrue(userGroupBindVo.getUserId() != null || !userGroupBindVo.getGroupIds().isEmpty(), BusinessExceptionEnum.PARAM_INVALID);
        log.info("groupBind Received Request Bind Group: users={}, groups={}", userGroupBindVo.getUserIds(), userGroupBindVo.getGroupId());
        groupService.addGroupBind(0L, userGroupBindVo.getGroupId(), userGroupBindVo.getUserIds());
        return Response.success();
    }

    /**
     * 用户群组解绑
     * @param parameter UserGroupBindVo
     * @param authentication AuthUser
     * @return
     */
    @PostMapping(value = "/unbind")
    public Response unbind(@RequestBody @Validated UserGroupBindVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        log.info("Received Request Unbind Group: parameter={}", JSON.toJSONString(parameter));
        groupService.deleteBind(parameter);
        return Response.success();
    }

    /**
     * 分页查询群组
     *
     * @param parameter GroupQueryVo
     * @param authentication AuthUser
     * @return
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping(value = "/page")
    public Response page(@Validated GroupQueryVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        parameter.setTenantId(0L);
        Page<GroupVo> page = groupService.getForPage(parameter);
        return Response.success(Response.newPage(page.getRecords(), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal()));
    }

    /**
     * 查询所有简单分组信息
     */
    @AccessBy({AccessBy.Method.COOKIE, AccessBy.Method.APP_TOKEN})
    @GetMapping(value = "/all")
    public Response all(@Validated GroupQueryVo parameter, @RequestAttribute(ARAN) Authentication authentication) {
        parameter.setTenantId(0L);
        return Response.success(groupService.getAllGroupSimple(parameter));
    }

    /**
     * 取得群组数量
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping("/count")
    public Response count(@RequestAttribute(ARAN) AuthUser authentication) {
        return Response.success(groupService.getGroupCount(0L));
    }

    /**
     * 分页查询用户的群组信息
     */
    @Authorize(Authorize.Type.NONE)
    @AccessBy({AccessBy.Method.COOKIE, AccessBy.Method.APP_TOKEN})
    @GetMapping(value = "/getGroupsByUserIds")
    public Response getGroups(@Validated UserQueryVo parameter) {
        return Response.success(groupService.getGroupsByUserIds(parameter.getUserIds()));
    }

    /**
     * 查询绑定的用户
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping(value = "/userBound")
    public Response userBound(Long groupId, @RequestAttribute(ARAN) AuthUser authUser) {
        Assert.notNull(groupId, BusinessExceptionEnum.PARAM_INVALID);
        return Response.success(groupService.getUserBound(groupId, authUser));
    }
}
