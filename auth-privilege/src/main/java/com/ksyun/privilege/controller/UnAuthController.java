package com.ksyun.privilege.controller;

import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.service.PersonAccessTokenService;
import com.ksyun.common.constant.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 一些需要对外公开的接口, 不需要走认证,主要由AuthenticationInterceptor控制
 * 
 *  catch (InvalidAuthTokenException e) {
 *           if (request.getRequestURL().indexOf("/api/") >= 0 ) {
 *              return onAuthenticateFailed(response, e.getMessage());
 *           }
 *           return true;
 *  }
 */
@RestController
@RequestMapping("/unauth")
@RequiredArgsConstructor
public class UnAuthController {

    private final PersonAccessTokenService personAccessTokenService;

    /**
     * 验证token有效性
     * @param token JWT格式的个人访问令牌
     * @return 如果token有效返回true，否则返回false
     */
    @PostMapping("/validatePersonAccessToken")
    @Authorize(Authorize.Type.NONE)
    public Response validateToken(@RequestParam String token) {
        try {
            boolean isValid = personAccessTokenService.validateToken(token);
            return Response.success(isValid);
        } catch (Exception e) {
            return Response.failure().message("验证token时发生错误: " + e.getMessage());
        }
    }
}
