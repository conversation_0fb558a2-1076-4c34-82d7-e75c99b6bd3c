package com.ksyun.privilege.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.service.RsaKeyService;
import com.ksyun.common.constant.Response;
import com.ksyun.common.constant.ErrorCode;
import com.ksyun.common.entity.RsaKey;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.ksyun.auth.client.authentication.Authentication.ARAN;

@Slf4j
@RestController
@RequestMapping("/api/rsaKeys")
@Validated
public class RsaKeyController {

    private static final int MAX_KEY_LENGTH = 2048;

    @Resource
    private RsaKeyService rsaKeyService;

    /**
     * 分页查询密钥列表
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping
    public Response list(@RequestParam(defaultValue = "1") @Min(value = -1, message = "页码不能小于-1") Integer current,
                        @RequestParam(defaultValue = "10") @Min(value = 1, message = "每页大小不能小于1") Integer size,
                        @RequestParam(required = false) String name,
                        @RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始查询RSA密钥列表, current={}, size={}, name={}, authentication={}", current, size, name, authentication);
        String userId = getUserId(authentication);
        if (userId == null) {
            log.info("查询RSA密钥列表失败, 非法用户");
            return Response.failure(ErrorCode.getMessage(ErrorCode.ILLEGAL_USER));
        }

        LambdaQueryWrapper<RsaKey> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RsaKey::getUserId, userId);
        
        // 当current为-1时，返回所有数据
        if (current == -1) {
            long total = rsaKeyService.count(wrapper);
            log.info("查询所有RSA密钥, userId={}, total={}", userId, total);
            return Response.success(rsaKeyService.page(new Page<>(1, total), wrapper));
        }

        if (name != null && !name.isEmpty()) {
            wrapper.like(RsaKey::getName, name);
        }
        wrapper.orderByDesc(RsaKey::getCreateTime);
        Page<RsaKey> page = rsaKeyService.page(new Page<>(current, size), wrapper);
        log.info("RSA密钥列表查询成功, userId={}, total={}, current={}, size={}", userId, page.getTotal(), current, size);
        return Response.success(page);
    }

    /**
     * 生成新的RSA密钥对
     */
    @Authorize(Authorize.Type.NONE)
    @PostMapping("/generate")
    public Response generate(@RequestParam @NotBlank(message = "密钥名称不能为空") String name,
                           @RequestParam(defaultValue = "2048") @Min(value = 1024, message = "密钥长度不能小于1024") Integer keySize,
                           @RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始生成RSA密钥对, name={}, keySize={}, authentication={}", name, keySize, authentication);
        String userId = getUserId(authentication);
        if (userId == null) {
            log.info("生成RSA密钥对失败, 非法用户");
            return Response.failure(ErrorCode.getMessage(ErrorCode.ILLEGAL_USER));
        }
        
        // 验证密钥名称是否已存在
        if (isKeyNameExists(name, userId)) {
            log.info("生成RSA密钥对失败, 密钥名称已存在, name={}, userId={}", name, userId);
            return Response.failure(ErrorCode.getMessage(ErrorCode.KEY_NAME_EXISTS));
        }

        try {
            RsaKey rsaKey = rsaKeyService.generateKeyPair(name, keySize, userId);
            log.info("RSA密钥对生成成功, name={}, userId={}, keyId={}", name, userId, rsaKey.getId());
            return Response.success(rsaKey);
        } catch (Exception e) {
            log.error("生成RSA密钥对异常, name={}, userId={}", name, userId, e);
            return Response.failure().message("生成密钥对失败: " + e.getMessage());
        }
    }

    /**
     * 上传公钥
     */
    @Authorize(Authorize.Type.NONE)
    @PostMapping("/upload")
    public Response upload(@RequestParam @NotBlank(message = "密钥名称不能为空") String name,
                         @RequestParam @NotBlank(message = "公钥内容不能为空") 
                         @Size(max = MAX_KEY_LENGTH, message = "公钥长度不能超过" + MAX_KEY_LENGTH) String publicKey,
                         @RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始上传RSA公钥, name={}, authentication={}", name, authentication);
        String userId = getUserId(authentication);
        if (userId == null) {
            log.info("上传RSA公钥失败, 非法用户");
            return Response.failure(ErrorCode.getMessage(ErrorCode.ILLEGAL_USER));
        }

        // 验证密钥名称是否已存在
        if (isKeyNameExists(name, userId)) {
            log.info("上传RSA公钥失败, 密钥名称已存在, name={}, userId={}", name, userId);
            return Response.failure(ErrorCode.getMessage(ErrorCode.KEY_NAME_EXISTS));
        }

        // 验证公钥格式
        if (!rsaKeyService.validateSshPublicKey(publicKey)) {
            log.info("上传RSA公钥失败, 公钥格式不正确, name={}, userId={}", name, userId);
            return Response.failure(ErrorCode.getMessage(ErrorCode.INVALID_PUBLIC_KEY));
        }

        try {
            RsaKey rsaKey = rsaKeyService.uploadPublicKey(name, publicKey, userId);
            log.info("RSA公钥上传成功, name={}, userId={}, keyId={}", name, userId, rsaKey.getId());
            return Response.success(rsaKey);
        } catch (Exception e) {
            log.error("上传RSA公钥异常, name={}, userId={}", name, userId, e);
            return Response.failure().message("上传公钥失败: " + e.getMessage());
        }
    }

    /**
     * 验证SSH公钥格式是否正确
     */
    @PostMapping("/validate")
    public Response validateSshPublicKey(@RequestParam @NotBlank(message = "公钥内容不能为空") String publicKey) {
        log.info("开始验证RSA公钥格式");
        try {
            boolean isValid = rsaKeyService.validateSshPublicKey(publicKey);
            if (!isValid) {
                log.info("RSA公钥格式验证失败");
                return Response.failure(ErrorCode.getMessage(ErrorCode.INVALID_PUBLIC_KEY));
            } else {
                log.info("RSA公钥格式验证成功");
                return Response.success(isValid);
            }
        } catch (Exception e) {
            log.error("验证RSA公钥格式异常", e);
            return Response.failure().message("验证公钥失败: " + e.getMessage());
        }
    }

    /**
     * 删除密钥
     */
    @Authorize(Authorize.Type.NONE)
    @DeleteMapping("/{id}")
    public Response delete(@PathVariable @Min(value = 1, message = "密钥ID必须大于0") Long id,
                         @RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始删除RSA密钥, id={}, authentication={}", id, authentication);
        String userId = getUserId(authentication);
        if (userId == null) {
            log.info("删除RSA密钥失败, 非法用户");
            return Response.failure(ErrorCode.getMessage(ErrorCode.ILLEGAL_USER));
        }

        RsaKey rsaKey = rsaKeyService.getById(id);
        if (rsaKey == null) {
            log.info("删除RSA密钥失败, 密钥不存在, id={}, userId={}", id, userId);
            return Response.failure(ErrorCode.getMessage(ErrorCode.KEY_NOT_FOUND));
        }
        if (!userId.equals(rsaKey.getUserId())) {
            log.info("删除RSA密钥失败, 无权限, id={}, userId={}, keyUserId={}", id, userId, rsaKey.getUserId());
            return Response.failure(ErrorCode.getMessage(ErrorCode.NO_PERMISSION));
        }

        try {
            boolean success = rsaKeyService.removeById(id);
            if (success) {
                log.info("RSA密钥删除成功, id={}, userId={}", id, userId);
            } else {
                log.info("RSA密钥删除失败, id={}, userId={}", id, userId);
            }
            return Response.success(success);
        } catch (Exception e) {
            log.error("删除RSA密钥异常, id={}, userId={}", id, userId, e);
            return Response.failure().message("删除密钥失败: " + e.getMessage());
        }
    }

    /**
     * 更新密钥状态
     */
    @Authorize(Authorize.Type.NONE)
    @PutMapping("/{id}/status")
    public Response updateStatus(@PathVariable @Min(value = 1, message = "密钥ID必须大于0") Long id,
                               @RequestParam(required = true) Boolean isActive,
                               @RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始更新RSA密钥状态, id={}, isActive={}, authentication={}", id, isActive, authentication);
        String userId = getUserId(authentication);
        if (userId == null) {
            log.info("更新RSA密钥状态失败, 非法用户");
            return Response.failure(ErrorCode.getMessage(ErrorCode.ILLEGAL_USER));
        }

        RsaKey existingKey = rsaKeyService.getById(id);
        if (existingKey == null) {
            log.info("更新RSA密钥状态失败, 密钥不存在, id={}, userId={}", id, userId);
            return Response.failure(ErrorCode.getMessage(ErrorCode.KEY_NOT_FOUND));
        }
        if (!userId.equals(existingKey.getUserId())) {
            log.info("更新RSA密钥状态失败, 无权限, id={}, userId={}, keyUserId={}", id, userId, existingKey.getUserId());
            return Response.failure(ErrorCode.getMessage(ErrorCode.NO_PERMISSION));
        }

        try {
            RsaKey rsaKey = new RsaKey();
            rsaKey.setId(id);
            rsaKey.setIsActive(isActive);
            rsaKey.setUpdateTime(LocalDateTime.now());
            boolean success = rsaKeyService.updateById(rsaKey);
            if (success) {
                log.info("RSA密钥状态更新成功, id={}, userId={}, isActive={}", id, userId, isActive);
            } else {
                log.info("RSA密钥状态更新失败, id={}, userId={}, isActive={}", id, userId, isActive);
            }
            return Response.success(success);
        } catch (Exception e) {
            log.error("更新RSA密钥状态异常, id={}, userId={}, isActive={}", id, userId, isActive, e);
            return Response.failure().message("更新密钥状态失败: " + e.getMessage());
        }
    }

    /**
     * 从Authentication获取用户ID，如果不是AuthUser则抛出异常
     */
    private String getUserId(Authentication authentication) {
        if (authentication instanceof AuthUser) {
            Long id = ((AuthUser) authentication).getId();
            if (id == null) {
                return null;
            } else {
                if (id < 0) {
                    return null;
                } else {
                    return String.valueOf(id);
                }
            }
        }
        return null;
    }

    /**
     * 检查指定用户的密钥名称是否已存在
     */
    private boolean isKeyNameExists(String name, String userId) {
        LambdaQueryWrapper<RsaKey> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RsaKey::getName, name)
               .eq(RsaKey::getUserId, userId);
        return rsaKeyService.count(wrapper) > 0;
    }
}
