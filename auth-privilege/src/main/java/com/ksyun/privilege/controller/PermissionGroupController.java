package com.ksyun.privilege.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.service.PermissionGroupService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.service.listener.PrivilegeFileReceiveEventListener;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.PermissionGroup;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.enums.SystemTypeEnum;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.exception.BaseException;
import jodd.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

import static com.ksyun.auth.client.Constants.AUTH_TYPE;
import static com.ksyun.auth.client.Constants.SYSTEM_TYPE;
import static com.ksyun.auth.client.authentication.Authentication.ARAN;


/**
 * 权限组 Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/permissionGroups")
@Authorize(Authorize.Type.PLATFORM)
public class PermissionGroupController extends BaseController {

    @Resource
    private PermissionGroupService permissionGroupService;

    @Resource
    HttpServletResponse response;

    @Autowired
    private UserService userService;

    /**
     * 分页查询 权限组
     */
    @GetMapping(value = "/page")
    public Response page(@Validated PermissionGroupQueryVo vo) {
        Page<PermissionGroup> page = permissionGroupService.getPermissionGroups(vo);
        return Response.success(Response.newPage(page.getRecords(), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal()));
    }

    /**
     * 查询 权限组
     */
    @GetMapping(value = "/getPermissionGroupAll")
    @Authorize(Authorize.Type.NONE)
    public Response getPermissionGroupAll(@Validated PermissionGroupQueryVo vo) {
        List<CategoryVo> permissionGroupList = permissionGroupService.getPermissionGroupsAll(vo);
        return Response.success(permissionGroupList);
    }

    /**
     * 获取权限组分类
     */
    @GetMapping(value = "/getCategoryPermissionGroups")
    @Authorize(Authorize.Type.NONE)
    public Response getCategoryPermissionGroups(@RequestParam Long roleId) {
        return Response.success(permissionGroupService.getCategoryPermissionGroups(roleId));
    }

    /**
     * 根据id查询权限组
     */
    @GetMapping(value = "/{id}")
    public Response getPermissionGroup(@PathVariable Long id) {
        PermissionGroupVo pgs = permissionGroupService.getPermissionGroupsById(id);
        return Response.success(pgs);
    }

    /**
     * 根据角色id查询权限组
     */
    @GetMapping(value = "/getRolePermissionGroupIds")
    @Authorize(Authorize.Type.NONE)
    public Response getPermissionGroupIdsByRoleId(@RequestParam Long roleId) {
        List<Long> permissionGroupIdsList = permissionGroupService.getPermissionGroupIdsByRoleId(roleId);
        return Response.success(permissionGroupIdsList);
    }

    /**
     * 添加权限组
     */
    @PostMapping
    public Response add(@RequestBody @Validated PermissionGroupAddVo vo, @RequestAttribute(ARAN) AuthUser authUser) {
        vo.setCreateBy(authUser.getName());
        permissionGroupService.addPermissionGroup(vo);
        return Response.success();
    }

    /**
     * 更新权限组
     */
    @PutMapping
    public Response update(@RequestBody @Validated PermissionGroupAddVo vo, @RequestAttribute(ARAN) AuthUser authUser) {
        permissionGroupService.updatePermissionGroup(vo);
        return Response.success();
    }

    /**
     * 删除权限组
     */
    @DeleteMapping()
    public Response delete(@RequestBody @Validated PermissionGroupDeleteVo ids) {
        permissionGroupService.deletePermissionGroup(ids);
        return Response.success();
    }

    /**
     * 设置公开和不公开
     * @param params
     * @return
     */
    @PutMapping("/openOrClose")
    public Response openOrClose(@RequestBody @Validated PermissionGroupOpenCloseVo params) {
        permissionGroupService.openOrClose(params);
        return Response.success();
    }

    /**
     * 权限组分类展示
     */
    @GetMapping(value = "/categoryPage")
    public Response categoryPage() {
        return Response.success(permissionGroupService.getCategoryList());
    }

    /**
     * 添加权限组分类
     */
    @PostMapping(value = "/addCategory")
    public Response addCategory(@RequestBody @Validated CategoryAddVo vo) {
        permissionGroupService.addCategory(vo);
        return Response.success();
    }

    /**
     * 更新权限组分类
     */
    @PostMapping(value = "/updateCategory")
    public Response updateCategory(@RequestBody @Validated CategoryAddVo vo) {
        permissionGroupService.updateCategory(vo);
        return Response.success();
    }

    /**
     * 删除权限组分类
     */
    @DeleteMapping(value = "/deleteCategory")
    public Response deleteCategory(@RequestBody @Validated CategoryVo vo) {
        permissionGroupService.deleteCategory(vo);
        return Response.success();
    }

    /**
     * 绑定角色与权限组
     */
    @PostMapping(value = "/bindRolePermissionGroup")
    @Authorize(Authorize.Type.NONE)
    public Response bindRolePermissionGroup(@RequestBody @Validated GrantPermissionGroupVo vo, @RequestAttribute("ARAN") AuthUser authUser) {
//        String systemType = MDC.get(SYSTEM_TYPE);
//        String authType = MDC.get(AUTH_TYPE);
//        log.debug("bindRolePermissionGroup systemType header={}, authType header={} ", systemType, authType);
//        if (Objects.equals(systemType, SystemTypeEnum.BIGDATA.getCode())) {
//            Assert.isTrue(userService.getTenantCustomRoleFlag(authUser.getTenant().getId()), BusinessExceptionEnum.ROLE_CUSTOM_CREAT_CLOSE);
//        }
        permissionGroupService.addBindRolePermissionGroup(vo);
        return Response.success();
    }

    /**
     * 解绑角色与权限组绑定关系
     */
    @PostMapping(value = "/unbindRolePermissionGroup")
    public Response unbindRolePermissionGroup(@RequestBody @Validated GrantPermissionGroupVo vo) {
        permissionGroupService.deleteBindRolePermissionGroup(vo);
        return Response.success();
    }

    /**
     * 导入系统权限点
     */
    @PostMapping("/upload")
    @ResponseBody
    @Authorize(Authorize.Type.NONE)
    public Response upload(@RequestParam("file") MultipartFile file) throws IOException {
        String key = "auth_privilege_privilegeupload";
        try{
            String privilegeUploadLock = (String) CacheUtil.get(key);
            if (StringUtils.isNotEmpty(privilegeUploadLock)) {
                return Response.failure().message("处理进行中， 请稍后再试！");
            }
            CacheUtil.set(key, "1", 60 * 5);
            permissionGroupService.receive(file, PrivilegeFileReceiveEventListener.PrivilegeScope.Internal);

        }catch (Exception e){
            e.printStackTrace();
            throw new BaseException(e.getMessage());
        }finally {
            CacheUtil.del(key);
        }
        return Response.success();
    }

    /**
     * 导出系统权限点
     */
    @RequestMapping(value = "/download", method = {RequestMethod.GET, RequestMethod.POST})
    public void downLoad() throws IOException {
        //导出
        String export = permissionGroupService.exportPermissionGroup(PrivilegeFileReceiveEventListener.PrivilegeScope.Internal);
        //下载
        download(response, PropConfig.TMP_DIR, export);
        FileUtil.delete(PropConfig.TMP_DIR + File.separator + export);
    }

    /**
     * 下载系统模版
     */
    @RequestMapping(value = "/downloadTemplate", method = {RequestMethod.GET, RequestMethod.POST})
    public void downLoadTemplate() throws UnsupportedEncodingException {
        download(response, PropConfig.TMP_DIR, Constants.PRIVILEGE_TEMPLATE_FILE_NAME);
    }
}
