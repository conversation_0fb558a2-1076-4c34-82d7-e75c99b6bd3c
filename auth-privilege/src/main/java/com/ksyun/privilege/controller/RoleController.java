package com.ksyun.privilege.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.service.PrivilegeService;
import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.Role;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.exception.Assert;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static com.ksyun.auth.client.authentication.Authentication.ARAN;

/**
 * 角色服务类
 */
@RestController
@RequestMapping("/api/roles")
@Authorize(Authorize.Type.PLATFORM)
@Slf4j
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Autowired
    private PrivilegeService privilegeService;

    /**
     * 查询角色
     */
    @Authorize(Authorize.Type.NONE)
    @AccessBy
    @GetMapping(value = "/page")
    public Response page(RoleQueryVo parameter, @RequestAttribute(ARAN) Authentication authentication) {
        Page<Role> page = roleService.page(parameter, authentication);
        return Response.success(Response.newPage(page.getRecords(), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal()));
    }

    /**
     * 查询角色（不带分页）
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping("/list")
    public Response queryRoles(RoleQueryVo parameter, @RequestAttribute(ARAN) Authentication authentication) {
        return Response.success(roleService.getRoles(parameter, authentication));
    }

    /**
     * 租户控制台增加角色
     */
    @PostMapping(value = "")
    @Authorize(Authorize.Type.NONE)
    public Response addRole(@RequestBody @Validated RoleAddVo roleAddVo, @RequestAttribute(ARAN) AuthUser user) {
        ModelMapper mapper = new ModelMapper();
        RoleVo role = mapper.map(roleAddVo, RoleVo.class);
        role.setTagIds(RoleVo.serializeTagIds(roleAddVo.getTagIds()));
        role.setCreateBy(user.getName());
        roleService.addRole(role, user, "0.3");
        return Response.success();
    }

    /**
     * 租户控制台修改角色
     */
    @PutMapping(value = "")
    @Authorize(Authorize.Type.NONE)
    public Response updateRole(@RequestBody @Validated RoleUpdateVo vo, @RequestAttribute(ARAN) AuthUser user) {
        roleService.updateRoleByBigData(vo, user);
        return Response.success();
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/{roleId}")
    @Authorize(Authorize.Type.NONE)
    public Response detail(@PathVariable("roleId") Long roleId) {
        Assert.notNull(roleService.get(roleId), "未找到角色");
        Role role = roleService.get(roleId);
        return Response.success(role);
    }

    /**
     * 租户控制台删除角色
     */
    @DeleteMapping("/{roleId}")
    public Response deleteRole(@PathVariable("roleId") Long roleId, @RequestAttribute(ARAN) AuthUser authUser) {
        Assert.notNull(roleService.getRoleByCreateBy(roleId), "未找到该角色");
        roleService.deleteRoleById(roleId);
        return Response.success();
    }

    /**
     * 根据用户编号获用户角色
     */
    @GetMapping("/user/{userId}")
    @Authorize(Authorize.Type.NONE)
    public Response getRolesByUserId(@PathVariable Long userId) {
        return Response.success(roleService.getRolesByUserId(userId));
    }

    /**
     * 角色列表 - 绑定用户与角色
     */
    @PostMapping("/userBind")
    public Response userBind(@RequestBody @Validated UserRoleBindUnbindVo parameter) {
        roleService.addBindUserAndRoles(parameter.getUserIds(), parameter.getRoleIds());
        return Response.success();
    }

    /**
     * 角色列表 - 解绑用户与角色
     */
    @PostMapping("/userUnbind")
    public Response userUnbind(@RequestBody @Validated UserRoleBindUnbindVo parameter) {
        roleService.deleteBindUserAndRoles(parameter.getUserIds(), parameter.getRoleIds());
        return Response.success();
    }

    /**
     * 更改用户角色
     */
    @PutMapping("/userRole")
    public Response updateUserRoles(@RequestBody @Validated UserRoleBindUnbindVo parameter) {
        roleService.addBindOrDeleteBindUserAndRoles(parameter.getUserIds(), parameter.getRoleIds());
        return Response.success();
    }


    /**
     * 绑定权限与角色关系
     */
    @PostMapping("/bindPrivilegeRole")
    public Response bindPrivilegeRole(@RequestBody @Validated RolePrivilegeVo rolePrivilegeVo) {
        privilegeService.addBindPrivilegeRole(rolePrivilegeVo);
        return Response.success();
    }

    /**
     * 给角色分配权限
     */
    @PostMapping("/grantPrivilege")
    public Response grantPrivilege(@RequestBody @Validated GrantPrivilegeVo grantPrivilegeVo) {
        privilegeService.addGrantPrivilege(grantPrivilegeVo);
        return Response.success();
    }

    /**
     * 获取角色分配的权限
     */
    @GetMapping("/rolePrivileges")
    public Response privileges(@RequestParam("roleId") Long roleId) {
        Set<Long> rolePrivileges = privilegeService.getRolePrivileges(Collections.singleton(roleId));
        return Response.success(rolePrivileges);
    }

    /**
     * 查询当前角色id绑定的组
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping(value = "/group/{roleId}")
    public Response groupBound(@PathVariable("roleId") Long roleId, @RequestAttribute("ARAN") AuthUser authUser) {
        Assert.notNull(roleId, BusinessExceptionEnum.PARAM_INVALID);
        return Response.success(roleService.addGroupBound(roleId, authUser));
    }

//    /**
//     * 查询当前角色id绑定的用户
//     */
//    @Authorize(Authorize.Type.NONE)
//    @GetMapping(value = "/user/{roleId}")
//    public Response userBound(@PathVariable("roleId") Long roleId, @RequestAttribute("ARAN") AuthUser authUser) {
//        Assert.notNull(roleId, BusinessExceptionEnum.PARAM_INVALID);
//        return Response.success(roleService.addUserBound(roleId, authUser));
//    }

    /**
     * 绑定角色和权限组关系
     */
    @Authorize(Authorize.Type.NONE)
    @PostMapping(value = "/bindRolePermissionGroup/upload")
    @ResponseBody
    public Response bindRolePermissionGroup(@RequestParam("file") MultipartFile file) {
        roleService.addBindRolePermissionGroup(file);
        return Response.success();
    }

    /**
     * 获取角色标签
     */
    @GetMapping("/tags")
    @Authorize(Authorize.Type.NONE)
    public Response roleTags() {
        List<RoleTagsVo> tags = roleService.getAllTags();
        return Response.success(tags);
    }

    /**
     * 角色列表 绑定分组与角色
     */
    /*@PostMapping("/groupBind")
    public Response groupBind(@RequestBody @Validated GroupRoleBindUnbindVo parameter, @RequestAttribute(ARAN) AuthUser authUser) {
        roleService.addBindGroupAndRoles(parameter.getGroupIds(), parameter.getRoleIds(), authUser);
        return Response.success();
    }*/

    /**
     * 绑定分组与角色
     */
    @PostMapping("/groupBind")
    public Response groupBind(@RequestBody @Validated GroupRoleBindVo groupRoleBindVo) {
        roleService.addBindGroupsAndRole(groupRoleBindVo.getGroupIds(), groupRoleBindVo.getRoleId());
        return Response.success();
    }

    /**
     * 解绑分组和角色
     */
    @PostMapping("/groupUnbind")
    public Response groupUnbind(@RequestBody @Validated GroupRoleBindUnbindVo parameter) {
        roleService.deleteBindGroupAndRoles(parameter.getGroupIds(), parameter.getRoleIds());
        return Response.success();
    }

    /**
     * 更改分组角色
     */
    @PutMapping("/groupRole")
    public Response updateGroupRole(@RequestBody @Validated GroupRoleBindUnbindVo parameter, @RequestAttribute(ARAN) AuthUser authUser) {
        roleService.updateBindAndUnbindGroupAndRoles(parameter.getGroupIds(), parameter.getRoleIds(), authUser);
        return Response.success();
    }
}
