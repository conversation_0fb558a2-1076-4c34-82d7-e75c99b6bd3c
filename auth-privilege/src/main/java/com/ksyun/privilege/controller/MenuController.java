package com.ksyun.privilege.controller;

import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.service.PrivilegeService;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.common.constant.Response;
import com.ksyun.common.utils.CommonUtils;
import jodd.http.Cookie;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 获取大数据云平台导航菜单
 */
@RestController
@RequestMapping("/api/menus")
public class MenuController {

    @Autowired
    private PrivilegeService privilegeService;

    /**
     * 查询左侧导航菜单
     */
    @GetMapping("")
    public Response getMenus(HttpServletRequest request, @RequestAttribute(Authentication.ARAN) AuthUser user, String menuType) {
        Cookie cookie = CommonUtils.javaCookie2JoddCookie(AuthenticationHelper.getAuthenticationRelatedCookie(request).orElseGet(null));
        return Response.success(privilegeService.getIndexMenus(cookie, user, request,menuType));
    }
}