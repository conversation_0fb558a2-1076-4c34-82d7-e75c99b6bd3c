package com.ksyun.privilege.controller;

import com.ksyun.common.exception.BusinessException;
import com.ksyun.common.utils.TreeNode;
import com.ksyun.auth.vo.PrivilegeListItemVo;
import com.ksyun.auth.vo.PrivilegeVo;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020
 */
@Slf4j
public class BaseController {

    protected List<PrivilegeListItemVo> filterNodes(TreeNode<PrivilegeVo> node, Integer type, String name, String code) {
        List<PrivilegeListItemVo> menus = new ArrayList<>();
        for (TreeNode<PrivilegeVo> subNode : node.getChildren()) {
            boolean valid = subNode.containsNode(item -> (type == null || item.getType().equals(type))
                    && (name == null || item.getName().contains(name))
                    && (code == null || item.getCode().contains(code)));
            if (!valid) {
                continue;
            }
            PrivilegeVo entity = subNode.getData();
            List<PrivilegeListItemVo> subNodes = filterNodes(subNode, type, name, code);
            PrivilegeListItemVo vo = new PrivilegeListItemVo();
            vo.setId(entity.getId());
            vo.setCode(entity.getCode());
            vo.setType(entity.getType());
            vo.setName(entity.getName());
            vo.setIcon(entity.getIcon());
            vo.setUrl(entity.getUrl());
            vo.setAk(entity.getAk());
            vo.setParentId(entity.getParentId());
            vo.setSubNodes(subNodes);
            menus.add(vo);
        }
        return menus;
    }

    protected void download(HttpServletResponse response, String path, String fileName) throws UnsupportedEncodingException {
        File file = new File(path + File.separator + fileName);
        if (file.exists()) { //判断文件父目录是否存在
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            byte[] buffer = new byte[1024];
            FileInputStream fis = null;
            BufferedInputStream bis = null;
            OutputStream os = null;
            try {
                os = response.getOutputStream();
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer);
                    i = bis.read(buffer);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (bis != null) {
                    bis.close();
                    fis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            throw new BusinessException("file not found.");
        }
    }
}
