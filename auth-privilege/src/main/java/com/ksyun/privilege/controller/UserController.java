package com.ksyun.privilege.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.auth.utils.encrypt.AESEncryption;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Response;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.enums.SystemTypeEnum;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.utils.DisplayUtil;
import com.ksyun.common.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.ksyun.auth.client.authentication.Authentication.ARAN;

/**
 * 用户管理相关接口，包含用户信息的增删改查等
 */
@RestController
@RequestMapping("/api/users")
@Authorize(Authorize.Type.PLATFORM)
@Slf4j
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    /**
     * 分页查询用户信息
     */
    @Authorize(Authorize.Type.NONE)
    @GetMapping(value = "/page")
    public Response page(@Validated UserQueryVo parameter, @RequestAttribute(ARAN) Authentication authentication) {
//        if (authentication instanceof AuthUser) {
//            AuthUser authUser = (AuthUser) authentication;
//            parameter.setTenantId(authUser.getTenant().getId());
//        }
//        if (authentication instanceof AuthApp) {
//            Assert.isTrue(!Objects.isNull(parameter.getTenantId()), "租户id不能为空");
//        }
        Page<BaseUser> page = userService.page(parameter);
        //默认进行脱敏显示
        if (!page.getRecords().isEmpty()) {
            page.getRecords().forEach(baseUser -> {
                baseUser.setPhone(DisplayUtil.displayMobile(baseUser.getPhone()));
                baseUser.setEmail(DisplayUtil.displayEmail(baseUser.getEmail()));
            });
        }
        return Response.success(Response.newPage(page.getRecords(), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal()));
    }

    /**
     * 创建用户/租户信息
     */
    @PostMapping()
    public Response add(@RequestBody @Validated UserAddVo parameter, @RequestAttribute("ARAN") AuthUser user) {
        String password = parameter.getPassword();
        if (StringUtils.isNotEmpty(password)) {
            parameter.setPassword(new AESEncryption().decrypt(password, PropConfig.AES_SECRET_KEY));
        }
        if (StringUtils.isNotEmpty(parameter.getEmail())) {
            if (!Utils.isEmail(parameter.getEmail())) {
                return Response.failure(201, "邮箱码格式错误", "");
            }
        }
        if (StringUtils.isNotEmpty(parameter.getPhone())) {
            if (!Utils.isPhone(parameter.getPhone())) {
                return Response.failure(201, "手机号码格式错误", "");
            }
        }
        if (StringUtils.isNotEmpty(parameter.getPassword()) && StringUtils.isNotEmpty(parameter.getName()) && parameter.getPassword().contains(parameter.getName())) {
            return Response.failure(201, "密码不能包含用户名", "");
        }
        //parameter.setTenantUser(user);
        parameter.setSecretLevel(5);
        userService.addUser(parameter);

        log.info("Saved user: {}", parameter.getName());
        return Response.success();
    }

    /**
     * 修改用户/租户信息
     */
    @PutMapping()
    public Response update(@RequestBody @Validated UserVo parameter, @RequestAttribute("ARAN") AuthUser authentication) {
        //parameter.setTenantUser(authentication);
        String password = parameter.getPassword();
        if (StringUtils.isNotEmpty(password)) {
            if (!Utils.isRequirePassword(password)) {
                return Response.failure(201, "密码长度在[8-32]之间，并且必须包含数字、大写字母、小写字母", "");
            }
            parameter.setPassword(new AESEncryption().decrypt(password, PropConfig.AES_SECRET_KEY));
        }

        String email = parameter.getEmail();
        if (StringUtils.isNotEmpty(email)) {
            if (!Utils.isEmail(email)) {
                return Response.failure(201, "邮箱码格式错误", "");
            }
        }
        parameter.setEmail(email);

        String phone = parameter.getPhone();
        if (StringUtils.isNotEmpty(phone)) {
            if (!Utils.isPhone(phone)) {
                return Response.failure(201, "手机号码格式错误", "");
            }
        }
        parameter.setPhone(phone);
        userService.update(parameter);
        log.info("Updated user: {}", parameter.getAlias());
        return Response.success();
    }

    /**
     * 删除用户/租户信息
     */
    @DeleteMapping()
    public Response delete(@RequestBody @Validated UserDeleteVo userDeleteParameter, @RequestAttribute("ARAN") AuthUser authentication) {
        userService.delete(userDeleteParameter);
        return Response.success();
    }

    /**
     * 修改用户密码
     */
    @PutMapping(value = "/password")
    public Response changePassword(@Validated @RequestBody UserPasswordVo parameter, @RequestAttribute("ARAN") AuthUser authentication) {
        log.info("changePassword=> new:{} old:{}", parameter.getNewPassword(), parameter.getOldPassword());
        String newpwd = parameter.getNewPassword();//new RSAEncryption().decryptByPrivateKey(parameter.getNewPassword(), PropConfig.RSA_PRIVATE_KEY);
        if (!Utils.isRequirePassword(newpwd)) {
            return Response.failure(201, "密码长度在[8-32]之间，并且必须包含数字、大写字母、小写字母", "");
        }
        String oldPwd = parameter.getOldPassword();//new RSAEncryption().decryptByPrivateKey(parameter.getOldPassword(), PropConfig.RSA_PRIVATE_KEY);
        parameter.setNewPassword(newpwd);
        parameter.setOldPassword(oldPwd);
        log.info("changePassword jm=> new:{} old:{}", newpwd, oldPwd);
        parameter.setTenantId(authentication.getTenant().getId());
        parameter.setUserId(authentication.getId());
        userService.updatePassword(parameter, false);
        return Response.success();
    }

    //    /**
//     * 修改用户密码
//     */
//    @PutMapping(value = "/password")
//    public Response changePassword(@Validated @RequestBody UserPasswordVo parameter,@RequestAttribute(ARAN) Authentication authentication) {
//        userService.updatePassword(parameter, true);
//        return Response.success();
//    }

    /**
     * 修改普通用户角色
     */
    @Authorize(Authorize.Type.NONE)
    @PutMapping(value = "/updateCustomRoleStatus")
    public Response updateCustomRoleStatus(@Validated @RequestBody UpdateCustomRoleStatusVo updateCustomRoleStatusVo) {
        return Response.success(userService.updateCustomRoleStatus(updateCustomRoleStatusVo));
    }

    /**
     * 用户密级设置
     */
    @PostMapping("/secret_level")
    public Response setUserSecretLevel(@RequestBody @Validated SecretLevelSetVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        // 查询用户Id是否存在
        userService.getUserExist(parameter.getUserIds());
        // 检查密级等级是否有效
        Assert.isTrue(Arrays.asList(1, 2, 3, 4, 5).contains(parameter.getSecretLevel()), "用户密级等级无效");
        userService.updateUserSecretLevel(parameter);
        return Response.success();
    }

    /**
     * 查询用户信息
     */
    @GetMapping(value = "/{userId}")
    @Authorize(Authorize.Type.NONE)
    public Response get(@PathVariable Long userId, @RequestAttribute("ARAN") Authentication authentication) {
        Optional<UserVo> user = userService.getUserById(userId);
        Assert.isTrue(user.isPresent(), "未找到用户");
        return Response.success(user);
    }

    /**
     * 根据用户名称查询用户信息
     */
    @GetMapping(value = "/user")
    @Authorize(Authorize.Type.NONE)
    public Response getUserByName(@RequestParam() String userName) {
        Optional<List<UserVo>> user = userService.getUserByName(userName);
        Assert.isTrue(user.isPresent(), BusinessExceptionEnum.USER_NOT_FOUND);
        return Response.success(user);
    }

    /**
     * 子账号首次登陆时，重置密码
     */
    @PutMapping("/firstLogin/resetPassword")
    public Response subAccountResetPasswordFirstLogin(@RequestBody @Validated SubAccountPasswordResetVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        parameter.setUserId(authentication.getId());
        //parameter.setTenantId(authentication.getTenant().getId());
        userService.subAccountResetPasswordFirstLogin(parameter);
        return Response.success();
    }


    @PutMapping("/resetPassword")
    public Response resetPassword(@RequestBody @Validated SubAccountPasswordResetVo parameter, @RequestAttribute(ARAN) AuthUser authentication) {
        parameter.setUserId(parameter.getUserId());
        //parameter.setTenantId(authentication.getTenant().getId());
        String newPassword = new AESEncryption().decrypt(parameter.getNewPassword(), PropConfig.AES_SECRET_KEY);
        String confirmPassword = new AESEncryption().decrypt(parameter.getConfirmPassword(), PropConfig.AES_SECRET_KEY);
        parameter.setNewPassword(newPassword);
        parameter.setConfirmPassword(confirmPassword);
        userService.resetPassword(parameter);
        return Response.success();
    }

    @GetMapping("/{roleCode}/users")
    public Response getUsersByRoleCode (@PathVariable("roleCode") String roleCode) {
        Assert.notNull(roleService.getRoleByCode(roleCode, SystemTypeEnum.BIGDATA.getCode()), "未找到角色code");
        RoleVo role = roleService.getRoleByCode(roleCode, SystemTypeEnum.BIGDATA.getCode());
        Optional<List<UserVo>> list = userService.getUsersByRole(role.getId());
        return Response.success(list);
    }
}