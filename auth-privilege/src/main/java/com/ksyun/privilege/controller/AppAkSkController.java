package com.ksyun.privilege.controller;

import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.common.constant.Response;
import com.ksyun.auth.service.AppAkSkService;
import com.ksyun.auth.vo.AddAppAkSkVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

/**
 * @Date 2019-03-30 09:02
 * 应用级AKSK管理API
 **/
@RestController
@RequestMapping("/api/app-aksk")
@Authorize(Authorize.Type.PLATFORM)
@Slf4j
public class AppAkSkController extends BaseController {

    @Autowired
    private AppAkSkService appAkSkService;

    /**
     * 创建应用aksk
     *
     * @param parameter
     * @param user
     * @return
     */
    @PostMapping
    @AccessBy(AccessBy.Method.COOKIE)
    public Response register(@RequestBody @Validated AddAppAkSkVo parameter, @RequestAttribute(Authentication.ARAN) AuthUser user) {
        parameter.setCreateBy(user.getId());
        parameter.setAppSk(UUID.randomUUID().toString());

        log.info("Start to save AppAkSk, app-name={}, app-ak={}, app-sk={}", parameter.getAppName(), parameter.getAppAk(), parameter.getAppSk());
        appAkSkService.add(parameter);
        log.info("Finished Saving AppAkSk, app-name={}, app-ak={}, app-sk={}", parameter.getAppName(), parameter.getAppAk(), parameter.getAppSk());
        return Response.success(parameter.getAppSk());
    }

}
