package com.ksyun.privilege.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dto.PrivilegePropsDto;
import com.ksyun.auth.dto.PrivilegeQueryDto;
import com.ksyun.auth.service.PrivilegeService;
import com.ksyun.auth.utils.PrivilegeUtils;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.auth.vo.MenuTreeItemVo;
import com.ksyun.auth.vo.PrivilegeUpdateVo;
import com.ksyun.auth.vo.PrivilegeDeleteVo;
import com.ksyun.auth.vo.PrivilegeListItemVo;
import com.ksyun.auth.vo.PrivilegePropsVo;
import com.ksyun.auth.vo.PrivilegeQueryVo;
import com.ksyun.auth.vo.PrivilegeVo;
import com.ksyun.common.constant.Response;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.enums.PrivilegePropEnum;
import com.ksyun.common.enums.PrivilegeTypeEnum;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.utils.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ksyun.common.enums.PrivilegePropEnum.IS_INDEX;
import static com.ksyun.common.enums.PrivilegePropEnum.JUMP_OUT_ALONE;
import static com.ksyun.common.enums.PrivilegePropEnum.POSITION_DOWN;

/**
 * 权限管理：权限的增删改查及与用户组的绑定和解绑
 * 功能说明：在原有基础上进行改造； 在原有方法只有最下面3个； 权限组与权限点绑定在PermissionGroup
 */
@RestController
@RequestMapping("/api/privileges")
@Authorize(Authorize.Type.PLATFORM)
@Slf4j
public class PrivilegeController extends BaseController {

    @Autowired
    private PrivilegeService privilegeService;

    /**
     * 查询节点列表
     */
    @GetMapping("/nodes")
    public Response nodes(PrivilegeQueryVo queryVo) {

        List<PrivilegeVo> entities = privilegeService.getAllByAK(queryVo.getFrom());

        PrivilegeVo rp = new PrivilegeVo();
        rp.setId(0L);
        TreeNode<PrivilegeVo> root = new TreeNode<>(rp);
        PrivilegeUtils.buildTree(entities, root, 0L);
        List<PrivilegeListItemVo> menus = filterNodes(root, queryVo.getType(), queryVo.getName(), queryVo.getCode());

        Set<Long> privilegeIds = entities.stream()
                .filter(item -> item.getType() == PrivilegeTypeEnum.MENU.getValue())
                .map(PrivilegeVo::getId)
                .collect(Collectors.toSet());
        List<PrivilegePropsDto> privilegeProps = privilegeService.getPrivilegeProps(privilegeIds, PrivilegePropEnum.getMenuKeys());
        Map<Long, List<PrivilegePropsVo>> propsMap = privilegeProps.stream()
                .collect(Collectors.toMap(PrivilegePropsDto::getPrivilegeId, PrivilegePropsDto::getProps));

        attachMenuProps(menus, propsMap);

        return Response.success(menus);
    }

    /**
     * 增加权限
     */
    @PostMapping
    public Response save(@RequestBody @Validated PrivilegeUpdateVo privilegeUpdateVo) {
        privilegeService.add(privilegeUpdateVo, PropConfig.LEVEL);
        return Response.success();
    }

    /**
     * 删除权限
     */
    @DeleteMapping
    public Response delete(@RequestBody @Validated PrivilegeDeleteVo privilegeDeleteVo) {
        privilegeService.delete(privilegeDeleteVo);
        return Response.success();
    }

    /**
     * 修改权限
     */
    @PutMapping
    public Response update(@RequestBody @Validated PrivilegeUpdateVo privilegeUpdateVo) {
        privilegeService.update(privilegeUpdateVo, PropConfig.LEVEL);
        return Response.success();
    }

    /**
     * 查询权限,通过privilegeId查询
     */
    @GetMapping(value = "/{privilegeId}")
    public Response getPrivilegeById(@PathVariable("privilegeId") Long privilegeId) {
        PrivilegeQueryDto queryDto = new PrivilegeQueryDto();
        queryDto.setIds(Sets.newHashSet(privilegeId));
        List<PrivilegeVo> query = privilegeService.getPrivileges(queryDto);
        Collection<PrivilegePropsDto> privilegeProps = privilegeService.getPrivilegeProps(Collections.singleton(privilegeId), PrivilegePropEnum.getMenuKeys());
        PrivilegeVo privilegeEntity = query.get(0);
        if (privilegeEntity != null && !privilegeProps.isEmpty()) {
            privilegeProps.forEach(props -> privilegeEntity.setProps(props.getProps()));
        }
        return Response.success(query.get(0));
    }

    /**
     * 菜单列表
     * 树形结构
     * type:  null:菜单+权限点，1:菜单
     */
    /*@GetMapping("/menus")
    public Response menus(@RequestParam(name = "type", required = false) Integer type,
                          @RequestParam(name = "name", required = false) String name,
                          @RequestParam(name = "from", required = false) String from) {
        Assert.isTrue(!(type != null && type != PrivilegeTypeEnum.MENU.getValue()), BusinessExceptionEnum.PARAM_INVALID);
        TreeNode<PrivilegeVo> root = getPrivilegeMenu(type,from);
        List<MenuTreeItemVo> menus = PrivilegeUtils.getMenus(root, name);
        return Response.success(menus);
    }*/


    /**
     * 上移       前端已经将功能点隐藏，待后续前端界面规划；
     */
    @GetMapping("/moveUp")
    public Response moveUpPrivilege(@RequestParam("id") int id) {
        PrivilegeVo privilegeEntity = privilegeService.get(id);
        Assert.notNull(privilegeEntity, BusinessExceptionEnum.PRIVILEGE_NOT_FOUND);
        log.info("Request GetPrivilege ={}", JSON.toJSONString(privilegeEntity));
        privilegeService.moveUpPrivilege(privilegeEntity);
        return Response.success();
    }

    /**
     * 下移       前端已经将功能点隐藏，待后续前端界面规划；
     */
    @GetMapping("/moveDown")
    public Response moveDownPrivilege(@RequestParam("id") int id) {
        PrivilegeVo privilegeEntity = privilegeService.get(id);
        if (privilegeEntity == null) {
            log.info("privilegeEntity is null");
            return Response.failure();
        }
        log.info("Request GetPrivilege ={}", JSON.toJSONString(privilegeEntity));
        privilegeService.moveDownPrivilege(privilegeEntity);
        return Response.success();

    }

    /**
     * 置顶       前端已经将功能点隐藏，待后续前端界面规划；
     */
    @GetMapping("/moveTop")
    public Response moveTopPrivilege(@RequestParam("id") int id) {
        PrivilegeVo privilegeEntity = privilegeService.get(id);
        if (privilegeEntity == null) {
            log.info("privilegeEntity is null");
            return Response.failure();
        }
        log.info("Request GetPrivilege ={}", JSON.toJSONString(privilegeEntity));
        privilegeService.setTopPrivilege(privilegeEntity);
        return Response.success();
    }

    /**
     * 置底 前端已经将功能点隐藏，待后续前端界面规划；
     */
    @GetMapping("/moveButtom")
    public Response moveButtonPrivilege(@RequestParam("id") int id) {
        PrivilegeVo privilegeEntity = privilegeService.get(id);
        if (privilegeEntity == null) {
            log.info("privilegeEntity is null");
            return Response.failure();
        }
        log.info("Request GetPrivilege ={}", JSON.toJSONString(privilegeEntity));
        privilegeService.setButtomPrivilege(privilegeEntity);
        return Response.success();
    }

    /**
     * 前端调用该接口查询用户拥有的可操作权限
     */
    @GetMapping(value = "privilegeForFrontPage")
    public Response getPrivileges(@Validated PrivilegeQueryVo parameter, @RequestAttribute("ARAN") AuthUser authentication, HttpServletRequest request) {
        return Response.success(privilegeService.getPrivilegeByUser(parameter, authentication, request));
    }

    /**
     * 获取应用列表
     */
    @GetMapping("/appList")
    public Response appList() {
        return Response.success(privilegeService.lookupAppList());
    }


    private TreeNode<PrivilegeVo> getPrivilegeMenu(Integer type,String from) {
        PrivilegeQueryDto queryDto = new PrivilegeQueryDto();
        queryDto.setType(type);
        queryDto.setAk(from);
        List<PrivilegeVo> entities = privilegeService.getPrivileges(queryDto);
        PrivilegeVo privilegeVo = new PrivilegeVo();
        privilegeVo.setId(0L);
        TreeNode<PrivilegeVo> root = new TreeNode<>(privilegeVo);
        PrivilegeUtils.buildTree(entities, root, 0L);
        return root;
    }

    private void attachMenuProps(List<PrivilegeListItemVo> menus, Map<Long, List<PrivilegePropsVo>> propsMap) {
        for (PrivilegeListItemVo item : menus) {
            Collection<PrivilegePropsVo> props = propsMap.get(item.getId());
            if (item.getType() == PrivilegeTypeEnum.PRIVILEGE.getValue() || props == null) {
                continue;
            }
            item.setProps(props.stream().filter(x -> Arrays.asList(IS_INDEX.getKey(), JUMP_OUT_ALONE.getKey(), POSITION_DOWN.getKey()).contains(x.getPropKey())).map(PrivilegePropsVo::getPropKey).collect(Collectors.toList()));
            attachMenuProps(item.getSubNodes(), propsMap);
        }
    }


}