package com.ksyun.privilege.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.AuthApp;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.dto.BaseUser;
import com.ksyun.auth.service.TenantService;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Response;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static com.ksyun.auth.client.authentication.Authentication.ARAN;


/**
 * 租户管理，包括增删改查
 */
@RestController
@RequestMapping("/api/tenants")
@Authorize(Authorize.Type.PLATFORM)
@Slf4j
public class TenantController {
    @Autowired
    private TenantService tenantService;

    /**
     * 添加租户
     */
    @PostMapping()
    public Response add(@RequestBody @Validated KcdeTenantAddVo parameter, @RequestAttribute(ARAN) Authentication authentication) {
        if (authentication instanceof AuthUser) {
            AuthUser authUser = (AuthUser) authentication;
            parameter.setCreatedBy(authUser.getId());
        }
        if (authentication instanceof AuthApp) {
            Assert.isTrue(!Objects.isNull(parameter.getCreatedBy()), "租户id不能为空");
        }
        tenantService.add(parameter);
        log.info("Saved tenant: {}", parameter.getName());
        return Response.success();
    }

    /**
     * 更新租户
     */
    @PutMapping()
    public Response update(@RequestBody @Validated KcdeTenantUpdateVo parameter) {
        tenantService.update(parameter);
        log.info("Updated tenant: {}", parameter.getId());
        return Response.success();
    }

    /**
     * 删除租户
     */
    @DeleteMapping()
    public Response delete(@RequestBody @Validated KcdeTenantDeleteVo parameter) {
        tenantService.delete(parameter.getTenantId());
        log.info("Deleted tenant: {}", parameter.getTenantId());
        return Response.success();
    }

    /**
     * 租户列表全量查询，不带分页
     */
    @GetMapping()
    public Response list(@Validated KcdeTenantQueryVo parameter) {
        parameter.setPageGlobal(false);
        List<KcdeTenantVo> tenants = tenantService.list(parameter);
        return Response.success(tenants);
    }

    /**
     * 租户列表分页查询
     */
    @GetMapping(value = "/page")
    public Response page(@Validated KcdeTenantQueryVo parameter) {
        Page<KcdeTenantVo> page = tenantService.page(parameter);
        return Response.success(Response.newPage(page.getRecords(), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal()));
    }

    /**
     * 根据租户名称查询
     */
    @GetMapping(value = "/tenant")
    public Response getTenantByName(@RequestParam String tenantName) {
        KcdeTenantVo tenant = tenantService.getTenantByName(tenantName);
        Assert.isTrue(tenant != null, "未找到租户");
        
//        List<UserTenant> tenantUsers = tenantService.getTenantUsers(tenant.getId());
        
//        KcdeTenantVo tenantVo = new KcdeTenantVo(tenant, tenantUsers);
        return Response.success(tenant);
    }

    /**
     * 根据租户id查询
     */
    @GetMapping(value = "/{tenantId}")
    public Response getTenantById(@PathVariable Long tenantId) {
        KcdeTenantVo tenant = tenantService.getTenantById(tenantId);
        
        Assert.isTrue(tenant != null, "未找到租户");
        
//        List<UserTenant> tenantUsers = tenantService.getTenantUsers(tenant.getId());
        
//        KcdeTenantVo tenantVo = new KcdeTenantVo(tenant, tenantUsers);
        return Response.success(tenant);
    }

    /**
     * 创建租户用户
     */
    @PostMapping(value = "/{tenantId}/users/{userId}")
    public Response addTenantUser(@PathVariable Long tenantId, @PathVariable Long userId) {
        tenantService.addTenantUser(tenantId, userId);
        return Response.success();
    }

    /**
     * 删除租户用户
     */
    @DeleteMapping(value = "/{tenantId}/users/{userId}")
    public Response deleteTenantUser(@RequestAttribute(Authentication.ARAN) Authentication authentication,
                                     @PathVariable Long tenantId, @PathVariable Long userId) {
        if (authentication instanceof AuthUser) {
            AuthUser user = (AuthUser) authentication;
            if (user.getId().equals(userId)) {
                throw new BusinessException("无法将自身移出租户用户");
            }
        }
        tenantService.deleteTenantUser(tenantId, userId);
        return Response.success();
    }

    /**
     * 批量删除租户用户
     */
    @DeleteMapping(value = "/{tenantId}/users")
    public Response batchDeleteTenantUser(@RequestAttribute(Authentication.ARAN) Authentication authentication,
                                          @PathVariable Long tenantId, @RequestBody @Validated KcdeTenantUserDeleteVo parameter) {
        if (authentication instanceof AuthUser) {
            AuthUser user = (AuthUser) authentication;
            for (Long uid : parameter.getUserIds()) {
                if (user.getId().equals(uid)) {
                    throw new BusinessException("无法将自身移出租户用户");
                }
            }
        }
        parameter.setTenantId(tenantId);
        tenantService.batchDeleteTenantUser(parameter);
        return Response.success();
    }

    /**
     * 租户用户列表，不带分页
     */
    @GetMapping(value = "/{tenantId}/users")
    public Response tenantUsers(@PathVariable Long tenantId, KcdeTenantUserQueryVo parameter) {
        parameter.setTenantId(tenantId);
        parameter.setPageGlobal(false);
        List<BaseUser> users = tenantService.tenantUsers(parameter);
        return Response.success(users);
    }

    /**
     * 租户用户列表
     */
    @GetMapping(value = "/{tenantId}/users/page")
    public Response tenantUserPage(@PathVariable Long tenantId, KcdeTenantUserQueryVo parameter) {
        parameter.setTenantId(tenantId);
        Page<BaseUser> page = tenantService.tenantUserPage(parameter);
        return Response.success(Response.newPage(page.getRecords(), (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal()));
    }
}
//public class TenantController {
//
//    @Autowired
//    public UserService userService;
//
//    /**
//     * 添加租户
//     */
//    @PostMapping()
//    @Authorize(Authorize.Type.NONE)
//    public Response add(@RequestBody @Validated TenantAddVo parameter) {
//        userService.addTenant(parameter);
//        log.info("Saved tenant: {}", parameter.getName());
//        return Response.success();
//    }
//
//    /**
//     * 更新租户
//     */
//    @PutMapping(value = "/update")
//    public Response update(@RequestBody @Validated TenantUpdateVo parameter,
//                           @RequestAttribute(ARAN) AuthUser authentication) {
//        Assert.isTrue(Objects.equals(parameter.getId(), authentication.getId()), BusinessExceptionEnum.AK_SK_CAN_NOT_BE_OPERATING);
//        UserVo userUpdateParameter = new UserVo();
//        userUpdateParameter.setId(parameter.getId());
//        userUpdateParameter.setEmail(parameter.getEmail());
//        userUpdateParameter.setPhone(parameter.getPhone());
//        userUpdateParameter.setTenantUser(authentication);
//        userService.update(userUpdateParameter);
//        log.info("Updated tenant: {}", userUpdateParameter.getId());
//        return Response.success();
//    }
//
//}
