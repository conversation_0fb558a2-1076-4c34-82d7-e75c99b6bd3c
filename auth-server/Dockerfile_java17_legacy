FROM hub.kce.ksyun.com/kcde_base/centos:centos7-jdk17-x86
#RUN yum install -y fontconfig
RUN mkdir -p /data/apps/kcde-auth-server
ADD auth-server/target/kcde-auth-server-0.0.1-SNAPSHOT.jar /data/apps/kcde-auth-server
COPY auth-server/bin/entrypoint.sh /data/apps/kcde-auth-server/bin/entrypoint.sh
RUN chown root:root /data/apps/kcde-auth-server/bin/entrypoint.sh && chmod 775 /data/apps/kcde-auth-server/bin/entrypoint.sh
#ENTRYPOINT  ["java", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.time=ALL-UNNAMED", "--add-opens=java.base/java.time=ALL-UNNAMED", "-Dspring.config.location=classpath:application.properties,file:/data/apps/kcde-auth-server/config/application-prod.properties","-jar","/data/apps/kcde-auth-server/kcde-auth-server-0.0.1-SNAPSHOT.jar"]
ENTRYPOINT [ "/data/apps/kcde-auth-server/bin/entrypoint.sh" ]
