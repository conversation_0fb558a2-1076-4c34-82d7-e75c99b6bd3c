package com.ksyun.auth.server.mapper;

import com.ksyun.auth.dao.TokenMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import jakarta.annotation.Resource;

/**
 * <AUTHOR> zhang
 * @Mail <EMAIL>
 * @Date 2019-03-17 17:56
 * @Version
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class TokenMapperTest {

    @Resource
    private TokenMapper mapper;

    @Test
    public void test() {
        System.out.println(mapper.lookupLatestDsk(2));
        System.out.println(mapper.lookupTenantAkSk("auth-server"));
    }

}
