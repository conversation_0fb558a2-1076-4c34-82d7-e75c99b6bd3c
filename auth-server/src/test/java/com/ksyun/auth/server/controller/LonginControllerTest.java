package com.ksyun.auth.server.controller;

import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @description:
 * @author: wuzhenliang
 * @date: 2020-06-22
 */


@RunWith(PowerMockRunner.class)
@PrepareForTest(LonginControllerTest.class)
@SpringBootTest(classes = LonginControllerTest.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class LonginControllerTest {

}
