package com.ksyun.auth.server;

import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;

import java.util.Date;

//@SpringBootTest
//@RunWith(SpringRunner.class)
public class JwtTest {
    private static String salt = "xxoo";
    private static Algorithm algorithm = Algorithm.HMAC256(salt);

    public static void main(String... args) throws Exception {
        String payload = JSON.toJSONString("Hello World");


        String xx = JWT.create().withSubject(payload).withExpiresAt(dateAddSeconds(new Date(), 30L)).sign(algorithm);
        String xxxx = JWT.create().withSubject(payload).withExpiresAt(dateAddSeconds(new Date(), 50L)).sign(algorithm);

        System.out.println(xx);
        System.out.println(xxxx);

        Thread.sleep(20 * 1000);

        DecodedJWT v1 = JWT.require(algorithm).build().verify(xx);
        System.out.println("xx");
    }

//    private String newJwt(String message, ) {
//
//    }


    private static Date dateAddSeconds(Date date, Long seconds) {
        return new Date(date.getTime() + seconds * 1000);
    }

}
