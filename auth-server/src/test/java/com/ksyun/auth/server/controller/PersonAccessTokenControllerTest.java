package com.ksyun.auth.server.controller;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.server.AuthServerApplication;
import com.ksyun.auth.service.PersonAccessTokenService;
import com.ksyun.common.entity.PersonAccessToken;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(
        classes = AuthServerApplication.class,
        properties = {
                "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,"
                        + "org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration,"
                        + "org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration"
        }
)
public class PersonAccessTokenControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Resource
    private PersonAccessTokenController personAccessTokenController;

    @MockBean
    private PersonAccessTokenService personAccessTokenService;

    private AuthUser mockAuthUser;

    @BeforeEach
    void setUp() {
        // 仅测试单个controller，其所依赖的服务对象需要MockBean注入 主要用于屏蔽 拦截器方便接口测试
        mockMvc = MockMvcBuilders.standaloneSetup(personAccessTokenController)
                .defaultResponseCharacterEncoding(StandardCharsets.UTF_8)
                .build();
        // 基于整个上下文进行测试，对象不需要mock 依赖于WebApplicationContext 注入context
//        mockMvc = MockMvcBuilders.webAppContextSetup(context)
//                .defaultResponseCharacterEncoding(StandardCharsets.UTF_8)
//                .build();

        mockAuthUser = mock(AuthUser.class);
        when(mockAuthUser.getId()).thenReturn(1L);
    }

    @Test
    @WithMockUser(username = "testUser")
    public void testCreateToken() throws Exception {
        // 准备测试数据
        PersonAccessToken token = new PersonAccessToken();
        token.setTokenValue("test-token");
        token.setCreatedAt(new Date());
        token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000)); // 1天后过期

        when(personAccessTokenService.createToken(anyString(), anyLong(), anyString())).thenReturn(token);

        // 执行测试
        mockMvc.perform(post("/api/createPersonAccessToken")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result.tokenValue").value("test-token"))
                .andExpect(jsonPath("$.result.createdAt").exists())
                .andExpect(jsonPath("$.result.expiresAt").exists());
    }

    @Test
    @WithMockUser(username = "testUser")
    public void testListTokens() throws Exception {
        // 准备测试数据
        PersonAccessToken token = new PersonAccessToken();
        token.setTokenValue("test-token");
        token.setCreatedAt(new Date());
        token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000));

        when(personAccessTokenService.getTokens(anyLong()))
                .thenReturn(Collections.singletonList(token));

        // 执行测试
        mockMvc.perform(get("/api/listPersonAccessToken")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result[0].tokenValue").value("test-token"))
                .andExpect(jsonPath("$.result[0].createdAt").exists())
                .andExpect(jsonPath("$.result[0].expiresAt").exists());
    }

    @Test
    @WithMockUser(username = "testUser")
    public void testGetTokenById() throws Exception {
        // 准备测试数据
        Long userId = 1L;
        PersonAccessToken token = new PersonAccessToken();
        token.setUserId(userId);
        token.setTokenValue("test-token");
        token.setCreatedAt(new Date());
        token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000));

        when(personAccessTokenService.getTokenByUserId(userId)).thenReturn(token);

        // 执行测试 - 成功场景
        mockMvc.perform(get("/api/getPersonAccessTokenById")
                .param("user_id", userId.toString())
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result.tokenValue").value("test-token"))
                .andExpect(jsonPath("$.result.createdAt").exists())
                .andExpect(jsonPath("$.result.expiresAt").exists());

        // 执行测试 - 用户不存在场景
        when(personAccessTokenService.getTokenByUserId(999L)).thenReturn(null);
        mockMvc.perform(get("/api/getPersonAccessTokenById")
                .param("user_id", "999"))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result").doesNotExist());
    }
}
