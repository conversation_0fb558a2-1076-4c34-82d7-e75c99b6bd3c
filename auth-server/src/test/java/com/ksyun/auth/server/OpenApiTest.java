package com.ksyun.auth.server;

import jodd.util.PropertiesUtil;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2019/9/19 21:52
 * @Description
 * @Version V1
 **/
public class OpenApiTest {

    @Test
    public void testAppHeaderToken() throws IOException {
        System.out.println(InetAddress.getLocalHost().getHostAddress());
        InputStream inputStream = PropertiesUtil.class.getClassLoader().getResourceAsStream("application.properties");

        //InputStream inputStream = PropertiesUtil.class.getClassLoader().getResourceAsStream("../com.ksyun.cam.kdbp.config/application.properties");

        Properties props = new Properties();
        props.load(inputStream);
        String port = props.getProperty("server.port");
        System.out.println(port);

        //PropertiesUtil.class.getClassLoader()
    }

    @Test
    public void getLocalIP() {
        String ip = "";
        try {
            Enumeration<?> e1 = (Enumeration<?>) NetworkInterface.getNetworkInterfaces();
            while (e1.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface) e1.nextElement();
                if (!ni.getName().equals("eth0")) {
                    continue;
                } else {
                    Enumeration<?> e2 = ni.getInetAddresses();
                    while (e2.hasMoreElements()) {
                        InetAddress ia = (InetAddress) e2.nextElement();
                        if (ia instanceof Inet6Address)
                            continue;
                        ip = ia.getHostAddress();
                    }
                    break;
                }
            }
        } catch (SocketException e) {
            //RUN_LOG.error("SocketException error message={}", e.getMessage());
            System.exit(-1);
        }
        System.out.println(ip);
    }

    @Test
    public void getMacAddr() {
        String MacAddr = "";
        String str = "";
        try {
            NetworkInterface NIC = NetworkInterface.getByName("eth0");
            byte[] buf = NIC.getHardwareAddress();
            for (int i = 0; i < buf.length; i++) {
                str = str + byteHEX(buf[i]);
            }
            MacAddr = str.toUpperCase();
        } catch (SocketException e) {
            e.printStackTrace();
            System.exit(-1);
        }

        System.out.println(MacAddr);
    }

    /* 一个将字节转化为十六进制ASSIC码的函数 */
    public static String byteHEX(byte ib) {
        char[] Digit = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a','b', 'c', 'd', 'e', 'f' };
        char[] ob = new char[2];
        ob[0] = Digit[(ib >>> 4) & 0X0F];
        ob[1] = Digit[ib & 0X0F];
        String s = new String(ob);
        return s;
    }
}
