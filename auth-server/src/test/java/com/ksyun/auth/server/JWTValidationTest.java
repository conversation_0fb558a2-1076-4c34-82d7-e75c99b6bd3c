package com.ksyun.auth.server;

import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSelector;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @since v1.8.0-2024/10/28
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class JWTValidationTest {

    @Autowired
    public JWKSource<SecurityContext> jwkSource;

    @Test
    public void testGenerateIdTokenAndValidate() {

        JWKSet jwkSet = ((ImmutableJWKSet)jwkSource).getJWKSet();
        List<JWK> keys =  jwkSet.getKeys();
        try {
            PublicKey publicKey = ((RSAKey)keys.get(0)).toRSAPublicKey();
            PrivateKey privateKey = ((RSAKey)keys.get(0)).toRSAPrivateKey();
            KeyPair keyPair = new KeyPair(publicKey, privateKey);
            String idToken = createIdToken(keyPair);
            System.out.println(idToken);
            String expected = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
            Assert.assertEquals(expected, idToken);
//            System.out.println(idToken);
            Assert.assertTrue("验证失败", verifyIdToken(idToken, keyPair)) ;
        } catch (JOSEException | ParseException e) {
            throw new RuntimeException(e);
        }
    }

    // 私钥创建id_token
    private static String createIdToken(KeyPair keyPair) throws JOSEException, ParseException {
        RSAPrivateKey privateKey = getPrivateKey(keyPair);

        // 创建 JWT Claims Set
//        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
//                .subject("testSubject")
//                .issuer("testIssuer")
//                .build();

        String json = "{\n" +
                "    \"api_key\": \"RgYBZcX6SaneMrmXKyfPKXKXr79qHxjz\",\n" +
                "    \"account_id\": \"hanhai\",\n" +
                "    \"user_id\": \"data\",\n" +
                "    \"user_name\": \"data\",\n" +
                "    \"user_full_name\": \"kas\",\n" +
                "    \"user_department\": \"AI产品研发中心/平台部\",\n" +
                "    \"permissions\": [\n" +
                "      \"VIEW_PAGE\",\n" +
                "      \"VIEW_TRAIN\",\n" +
                "      \"TRAIN_MGMT\"\n" +
                "    ],\n" +
                "    \"id\": 1,\n" +
                "    \"state\": 0,\n" +
                "    \"created_at\": \"2025-01-10 18:00:29\",\n" +
                "    \"updated_at\": \"2025-01-10 18:00:29\",\n" +
                "    \"iss\": \"kcde-for-kas\",\n" +
                "    \"jti\": \"9268856f-b998-4bd3-9ad8-1b4a02f030ef\",\n" +
                "    \"iat\": **********,\n" +
                "    \"exp\": **********,\n" +
                "    \"data_version\": \"240301\",\n" +
                "    \"key_version\": \"240301\"\n" +
                "  }";
        JWTClaimsSet claimsSet =  JWTClaimsSet.parse(json);

        // 创建 SignedJWT 对象
        SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.RS256), claimsSet);

        // 签名 JWT
        JWSSigner signer = new RSASSASigner(privateKey);
        signedJWT.sign(signer);

        return signedJWT.serialize();
    }

    // 公钥验证token
    private static boolean verifyIdToken(String idToken, KeyPair keyPair) throws JOSEException, ParseException {
        RSAPublicKey publicKey = getPublicKey(keyPair);

        // 解析 ID Token
        SignedJWT signedJWT = SignedJWT.parse(idToken);

        // 创建 JWSVerifier
        JWSVerifier verifier = new RSASSAVerifier(publicKey);

        // 验证签名
        if (signedJWT.verify(verifier)) {
            // 验证 Claims
            JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
            System.out.println("Subject: " + claimsSet.getSubject());
            System.out.println("Issuer: " + claimsSet.getIssuer());

            //TODO: 自行扩展自定义规则
            return true;
        } else {
            return false;
        }
    }

    public static KeyPair generateRsaKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        return keyPairGenerator.generateKeyPair();
    }

    public static RSAPublicKey getPublicKey(KeyPair keyPair) {
        return (RSAPublicKey) keyPair.getPublic();
    }

    public static RSAPrivateKey getPrivateKey(KeyPair keyPair) {
        return (RSAPrivateKey) keyPair.getPrivate();
    }

}
