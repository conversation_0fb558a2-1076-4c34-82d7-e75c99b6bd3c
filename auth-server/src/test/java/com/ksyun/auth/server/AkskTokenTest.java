package com.ksyun.auth.server;

import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.authentication.Authentication;
import jodd.http.HttpRequest;
import jodd.http.HttpResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> zhang
 * @Mail <EMAIL>
 * @Date 2019-03-24 08:30
 * 发送一个AKSK类型请求，测试AKSK生成策略
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class AkskTokenTest {

    @Value("${app.ak}")
    private String ak;

    @Value("${app.sk}")
    private String sk;

    @Value("${server.servlet.context-path}")
    private String context;


    @Test
    public void testAppHeaderToken() {
        String authServerUrl = "http://localhost:8080/auth";
        AuthenticationHelper.initialize(ak, sk, authServerUrl);

        String token = AuthenticationHelper.getToken();
        String tokenUrl = authServerUrl.concat("/api/aksk/1");
        HttpResponse response = HttpRequest.get(tokenUrl).header(Authentication.HEADER_X_AUTH_APP, ak).header(Authentication.HEADER_X_AUTH_TOKEN, token).send();

        String authToken = response.bodyText();
        assert authToken.contains(token) && (authToken.startsWith(token) || authToken.endsWith(token));
        System.out.println(String.format(" %s - %s", token, authToken));
    }

    @Test
    public void testTenantHeaderToken() {
        String authServerUrl = "http://localhost:8080/auth";
        AuthenticationHelper.initialize(ak, sk, authServerUrl);

        String token = AuthenticationHelper.getTenantToken("admin");
        String tokenUrl = authServerUrl.concat("/tenant-token/admin");
        HttpResponse response = HttpRequest.get(tokenUrl).header(Authentication.HEADER_X_AUTH_TENANT, "admin").header(Authentication.HEADER_X_AUTH_TOKEN, token).send();

        String authToken = response.bodyText();
        assert authToken.contains(token) && (authToken.startsWith(token) || authToken.endsWith(token));
        System.out.println(String.format(" %s - %s", token, authToken));
    }
}
