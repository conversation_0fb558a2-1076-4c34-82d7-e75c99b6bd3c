package com.ksyun.auth.server;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.server.interceptor.AuthServerCookieProcessor;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.servlet.http.Cookie;
import java.util.Optional;

/**
 * @description:
 * @author: wuzhenliang
 * @date: 2020-06-24
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest(AuthServerCookieProcessor.class)
@SpringBootTest(classes = AuthServerCookieProcessorTest.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class AuthServerCookieProcessorTest {

    @Mock
    private AuthServerCookieProcessor authServerCookieProcessor;

    @Test
    public void authCookie() {
        String cookieValue = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJ7XCJhS1NLSWRlbnRpZmljYXRpb25cIjpbXSxcImFsaWFzXCI6XCJ1YXRfdGVzdFwiLFwiY3JlYXRlVGltZVwiOjE1NjU5Mzk5NDEwMDAsXCJncm91cHNcIjpbXSxcImlkXCI6MjQsXCJpc1RlbmFudFwiOnRydWUsXCJuYW1lXCI6XCJ1YXRfdGVzdFwiLFwicmVzZXRQd2RXaGVuRmlyc3RMb2dpblwiOmZhbHNlLFwicm9sZVRhZ3NcIjpbXCIxMVwiLFwiMVwiLFwiMTJcIixcIjJcIixcIjNcIixcIjRcIixcIjVcIixcIjZcIixcIjdcIixcIjhcIixcIjlcIixcIjEwXCJdLFwic2VjcmV0TGV2ZWxcIjowLFwic291cmNlXCI6XCJMT0NBTFwiLFwic3RhdHVzXCI6XCJOT1JNQUxcIixcInRlbmFudFwiOntcImlkXCI6MjQsXCJuYW1lXCI6XCJ1YXRfdGVzdFwifSxcInR5cGVcIjpcIlVTRVJfQVVUSEVOVElDQVRJT05cIixcInVwZGF0ZVRpbWVcIjoxNTY1OTM5OTQxMDAwfSJ9.3EHORV1oUcsimb85PZSqJXR_e2ghmnuoYtAccukUyvI";
        Cookie cookie = new Cookie("sso-token", cookieValue);
        Cookie[] cookies = {cookie};
        // 进行cookie认证（此测试用例是走不通的原因是,最终解析cookie的工具类JwtUtils的decode方法在Junit模式下会报错）
        Optional<Authentication> authentication = authServerCookieProcessor.authCookie(cookies);
        AuthUser user;
        user = authentication.map(value -> (AuthUser) value).orElseGet(AuthUser::new);
        AuthUser authUser = new AuthUser();
        authUser.setId(24L);
        Assert.assertEquals(authUser.getId(), user.getId());
    }
}

