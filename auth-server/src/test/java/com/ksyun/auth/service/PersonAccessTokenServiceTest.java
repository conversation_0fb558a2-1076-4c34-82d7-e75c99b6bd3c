package com.ksyun.auth.service;

import com.ksyun.auth.dao.PersonAccessTokenMapper;
import com.ksyun.auth.service.impl.PersonAccessTokenServiceImpl;
import com.ksyun.auth.utils.PersonAccessTokenGenerator;
import com.ksyun.common.entity.PersonAccessToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PersonAccessTokenServiceTest {

    @Mock
    private PersonAccessTokenMapper tokenMapper;

    @Mock
    private PersonAccessTokenGenerator tokenGenerator;

    private PersonAccessTokenService tokenService;

    @BeforeEach
    void setUp() {
        tokenService = new PersonAccessTokenServiceImpl(tokenMapper, tokenGenerator);
    }

    @Test
    void testCreateToken() throws Exception {
        // 准备测试数据
        Long userId = 1L;
        String username = "test";
        String tokenValue = "test-token-value";
        when(tokenGenerator.generateToken(anyString(), anyString(), anyString(), anyLong())).thenReturn(tokenValue);

        // 执行测试
        PersonAccessToken token = tokenService.createToken(anyString(), userId, username);

        // 验证结果
        assertNotNull(token);
        assertEquals(userId, token.getUserId());
        assertEquals(tokenValue, token.getTokenValue());
        assertNotNull(token.getCreatedAt());
        assertNotNull(token.getExpiresAt());
        assertTrue(token.getExpiresAt().after(new Date()));

        // 验证调用
        verify(tokenMapper).deleteAllByUserId(userId);
        verify(tokenMapper).insert(any(PersonAccessToken.class));
    }

    @Test
    void testGetTokens() {
        // 准备测试数据
        Long userId = 1L;
        PersonAccessToken token = new PersonAccessToken();
        token.setUserId(userId);
        token.setTokenValue("test-token");
        when(tokenMapper.findValidTokensByUserId(userId))
                .thenReturn(Collections.singletonList(token));

        // 执行测试
        List<PersonAccessToken> tokens = tokenService.getTokens(userId);

        // 验证结果
        assertNotNull(tokens);
        assertEquals(1, tokens.size());
        assertEquals(userId, tokens.get(0).getUserId());
        assertEquals("test-token", tokens.get(0).getTokenValue());

        // 验证调用
        verify(tokenMapper).findValidTokensByUserId(userId);
    }
}
