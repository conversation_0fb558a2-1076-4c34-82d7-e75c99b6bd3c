package com.ksyun.auth.service;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

public class SshKeyGeneratorDemo {
    
    public static void main(String[] args) {
        try {
            // 指定生成密钥的目录
            String userHome = System.getProperty("user.home");
            Path sshDir = Paths.get(userHome, ".ssh1");
            
            // 如果.ssh目录不存在，创建它
            if (!Files.exists(sshDir)) {
                Files.createDirectories(sshDir);
            }
            
            // 生成密钥对
            JSch jsch = new JSch();
            KeyPair keyPair = KeyPair.genKeyPair(jsch, KeyPair.RSA, 2048);
            
            // 生成公钥文件 (id_rsa.pub)
            Path pubKeyPath = sshDir.resolve("id_rsa.pub");
            try (FileOutputStream fos = new FileOutputStream(pubKeyPath.toFile())) {
                keyPair.writePublicKey(fos, "Generated by KCDE");
            }
            
            // 生成私钥文件 (id_rsa)
            Path privKeyPath = sshDir.resolve("id_rsa");
            try (FileOutputStream fos = new FileOutputStream(privKeyPath.toFile())) {
                keyPair.writePrivateKey(fos);
            }
            
            // 设置正确的文件权限
            File privKeyFile = privKeyPath.toFile();
            privKeyFile.setReadable(false, false);  // 移除所有人的读权限
            privKeyFile.setReadable(true, true);    // 只给所有者读权限
            privKeyFile.setWritable(false, false);  // 移除所有人的写权限
            privKeyFile.setWritable(true, true);    // 只给所有者写权限
            privKeyFile.setExecutable(false, false); // 移除所有人的执行权限
            
            // 清理密钥对
            keyPair.dispose();
            
            System.out.println("SSH密钥对已生成：");
            System.out.println("公钥文件: " + pubKeyPath);
            System.out.println("私钥文件: " + privKeyPath);
            
            // 显示公钥内容
            System.out.println("\n公钥内容:");
            String publicKey = new String(Files.readAllBytes(pubKeyPath));
            System.out.println(publicKey);
            
            // 验证公钥格式
            System.out.println("\n验证公钥:");
            boolean isValid = validateSshPublicKey(publicKey);
            System.out.println("公钥格式是否有效: " + isValid);
            
            // 测试一些无效的公钥
            System.out.println("\n测试无效的公钥:");
            String[] invalidKeys = {
                "invalid-key",
                "ssh-rsa invalid-base64",
                "ssh-dsa AAAAB3NzaC1yc2EAAAADA...", // 错误的key type
                "ssh-rsa AAAAB3NzaC1kc2EAAAADA..." // 格式错误的base64
            };
            
            for (String key : invalidKeys) {
                System.out.println("测试: " + key);
                System.out.println("是否有效: " + validateSshPublicKey(key));
            }

            String[] validKeys = {
                "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDak6ksX5YL8vXR2JymWqJT9Znz0iNurO8sIkOvMOca5cemX6n8EPfnLQxrMZEw6sZ/KTFI2uRwnQJu4fcbcog2zYGFAD2TwOozhnduVhZCSIDZofcayCZzoggs+LHsB5Io/fKn1WjixuqrO42njb9DYOrFcPRroWoPnEOzVG4lI9jQVyBv191bI89Z5Nwvi6XV3zCutElltL/O1PLuXw34tlqCLkUqZe2bf2zOFo6j2ocrHdC0fuXk65XLDH05vQdXjOXdyGSCci0Nm9r33m1Y2So1XIANl3WLsc/JEhugQ2CNHYgpmhMg5SCycTz/frGdKFXh9IeVxnmgZg8DVyeM+7WdudBCaa4x+buYDxHuiJmd/MD+ivEiaoBZ+GgrZUu0Fb8QL9vLTqSzadObsi6T8k0rv42zbnROlcipdcd4zl25yzm2l1OxvApEVC4sCoghuN99k8zh4O5XFfFHYUtS8MZzFgO7e8nPaYKYfCeBfRLY6dSY7IHaIVz9mYykPms= ac"
            };
            for (String key : validKeys) {
                System.out.println("测试: " + key);
                System.out.println("是否有效: " + validateSshPublicKey(key));
            }


        } catch (Exception e) {
            System.err.println("生成SSH密钥对时发生错误：");
            e.printStackTrace();
        }
    }
    
    /**
     * 验证SSH公钥格式是否正确
     * 标准格式为: ssh-rsa AAAAB3NzaC1yc2EAAAADA... comment
     */
    public static boolean validateSshPublicKey(String publicKey) {
        if (publicKey == null || publicKey.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = publicKey.trim().split("\\s+", 3);
        if (parts.length < 2) {
            return false;
        }
        
        // 验证密钥类型
        String keyType = parts[0];
        if (!"ssh-rsa".equals(keyType)) {
            return false;
        }
        
        // 验证Base64部分
        String keyData = parts[1];
        try {
            // 尝试解码Base64
            byte[] decoded = Base64.getDecoder().decode(keyData);
            
            // 验证解码后的数据是否符合SSH公钥格式
            // SSH公钥格式: [length][ssh-rsa][length][exponent][length][modulus]
            if (decoded.length < 10) { // 最小长度检查
                return false;
            }
            
            // 验证密钥类型字段
            byte[] typeLength = new byte[4];
            System.arraycopy(decoded, 0, typeLength, 0, 4);
            int length = ((typeLength[0] & 0xFF) << 24) |
                        ((typeLength[1] & 0xFF) << 16) |
                        ((typeLength[2] & 0xFF) << 8) |
                        (typeLength[3] & 0xFF);
                        
            if (length <= 0 || length > decoded.length - 4) {
                return false;
            }
            
            return true;
        } catch (IllegalArgumentException e) {
            // Base64解码失败
            return false;
        }
    }
    
    /**
     * 设置文件权限为仅所有者可读写
     */
    private static void setFilePermissions(Path path) throws IOException {
        File file = path.toFile();
        // 600 权限：仅所有者可读写
        file.setReadable(false, false);   // 移除所有人的读权限
        file.setReadable(true, true);     // 给所有者读权限
        file.setWritable(false, false);   // 移除所有人的写权限
        file.setWritable(true, true);     // 给所有者写权限
        file.setExecutable(false, false); // 移除所有执行权限
    }
}
