package com.ksyun.auth.service.impl;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;
import com.ksyun.auth.server.AuthServerApplication;
import com.ksyun.auth.service.RsaKeyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = AuthServerApplication.class)
public class RsaKeyServiceImplTest {

    @Autowired
    private RsaKeyService rsaKeyService;

    @Test
    public void testValidateSshPublicKey() {
        // 测试有效的 SSH 公钥
        String validKey = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDak6ksX5YL8vXR2JymWqJT9Znz0iNurO8sIkOvMOca5cemX6n8EPfnLQxrMZEw6sZ/KTFI2uRwnQJu4fcbcog2zYGFAD2TwOozhnduVhZCSIDZofcayCZzoggs+LHsB5Io/fKn1WjixuqrO42njb9DYOrFcPRroWoPnEOzVG4lI9jQVyBv191bI89Z5Nwvi6XV3zCutElltL/O1PLuXw34tlqCLkUqZe2bf2zOFo6j2ocrHdC0fuXk65XLDH05vQdXjOXdyGSCci0Nm9r33m1Y2So1XIANl3WLsc/JEhugQ2CNHYgpmhMg5SCycTz/frGdKFXh9IeVxnmgZg8DVyeM+7WdudBCaa4x+buYDxHuiJmd/MD+ivEiaoBZ+GgrZUu0Fb8QL9vLTqSzadObsi6T8k0rv42zbnROlcipdcd4zl25yzm2l1OxvApEVC4sCoghuN99k8zh4O5XFfFHYUtS8MZzFgO7e8nPaYKYfCeBfRLY6dSY7IHaIVz9mYykPms= <EMAIL>";
        assertTrue(rsaKeyService.validateSshPublicKey(validKey), "有效的SSH公钥应该验证通过");

        // 测试各种无效的情况
        // 1. null值
        assertFalse(rsaKeyService.validateSshPublicKey(null), "null值应该验证失败");

        // 2. 空字符串
        assertFalse(rsaKeyService.validateSshPublicKey(""), "空字符串应该验证失败");

        // 3. 格式错误的字符串
        assertFalse(rsaKeyService.validateSshPublicKey("invalid-key"), "无效格式的字符串应该验证失败");

        // 4. 错误的密钥类型
        assertFalse(rsaKeyService.validateSshPublicKey("ssh-dsa AAAAB3NzaC1yc2EAAAADA..."), "非RSA类型的密钥应该验证失败");

        // 5. 缺少Base64部分
        assertFalse(rsaKeyService.validateSshPublicKey("ssh-rsa"), "缺少Base64部分的密钥应该验证失败");

        // 6. 无效的Base64编码
        assertFalse(rsaKeyService.validateSshPublicKey("ssh-rsa ABC!@#"), "无效的Base64编码应该验证失败");

        // 7. 格式正确但内容无效的Base64
        assertFalse(rsaKeyService.validateSshPublicKey("ssh-rsa AAAA"), "格式正确但内容无效的Base64应该验证失败");

        // 8. 错误的密钥结构
        assertFalse(rsaKeyService.validateSshPublicKey("ssh-rsa AAAAB3NzaC1kc2EAAAADA..."), "错误的密钥结构应该验证失败");
    }

    @Test
    public void testValidateWithGeneratedKey() throws Exception {
        // 使用JSch生成一个新的密钥对
        JSch jsch = new JSch();
        KeyPair keyPair = KeyPair.genKeyPair(jsch, KeyPair.RSA, 2048);
        
        // 将公钥转换为字符串
        java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
        keyPair.writePublicKey(baos, "test");
        String publicKey = baos.toString();
        
        // 验证生成的公钥
        assertTrue(rsaKeyService.validateSshPublicKey(publicKey), "使用JSch生成的公钥应该验证通过");
        
        // 清理资源
        keyPair.dispose();
    }
}
