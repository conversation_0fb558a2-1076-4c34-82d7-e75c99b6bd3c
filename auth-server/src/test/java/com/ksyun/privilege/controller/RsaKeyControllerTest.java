package com.ksyun.privilege.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.server.AuthServerApplication;
import com.ksyun.auth.service.RsaKeyService;
import com.ksyun.common.entity.RsaKey;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 单测参考
 * properties配置可以屏蔽 spring security各种配置
 */
@SpringBootTest(
    classes = AuthServerApplication.class,
    properties = {
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,"
            + "org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration,"
            + "org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration"
    }
)
class RsaKeyControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Resource
    private RsaKeyController rsaKeyController;

    @MockBean
    private RsaKeyService rsaKeyService;

    private static final String VALID_SSH_PUBLIC_KEY = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDak6ksX5YL8vXR2JymWqJT9Znz0iNurO8sIkOvMOca5cemX6n8EPfnLQxrMZEw6sZ/KTFI2uRwnQJu4fcbcog2zYGFAD2TwOozhnduVhZCSIDZofcayCZzoggs+LHsB5Io/fKn1WjixuqrO42njb9DYOrFcPRroWoPnEOzVG4lI9jQVyBv191bI89Z5Nwvi6XV3zCutElltL/O1PLuXw34tlqCLkUqZe2bf2zOFo6j2ocrHdC0fuXk65XLDH05vQdXjOXdyGSCci0Nm9r33m1Y2So1XIANl3WLsc/JEhugQ2CNHYgpmhMg5SCycTz/frGdKFXh9IeVxnmgZg8DVyeM+7WdudBCaa4x+buYDxHuiJmd/MD+ivEiaoBZ+GgrZUu0Fb8QL9vLTqSzadObsi6T8k0rv42zbnROlcipdcd4zl25yzm2l1OxvApEVC4sCoghuN99k8zh4O5XFfFHYUtS8MZzFgO7e8nPaYKYfCeBfRLY6dSY7IHaIVz9mYykPms= <EMAIL>";

    private AuthUser mockAuthUser;

    @BeforeEach
    void setUp() {
        // 仅测试单个controller，其所依赖的服务对象需要MockBean注入 主要用于屏蔽 拦截器方便接口测试
        mockMvc = MockMvcBuilders.standaloneSetup(rsaKeyController)
                .defaultResponseCharacterEncoding(StandardCharsets.UTF_8)
                .build();
        // 基于整个上下文进行测试，对象不需要mock 依赖于WebApplicationContext 注入context
//        mockMvc = MockMvcBuilders.webAppContextSetup(context)
//                .defaultResponseCharacterEncoding(StandardCharsets.UTF_8)
//                .build();

        mockAuthUser = mock(AuthUser.class);
        when(mockAuthUser.getId()).thenReturn(1L);
    }

    @Test
    void testList() throws Exception {
        // 准备测试数据
        RsaKey key1 = new RsaKey();
        key1.setId(1L);
        key1.setName("test-key-1");
        key1.setPublicKey(VALID_SSH_PUBLIC_KEY);
        key1.setUserId("1");
        key1.setCreateTime(LocalDateTime.now());

        RsaKey key2 = new RsaKey();
        key2.setId(2L);
        key2.setName("test-key-2");
        key2.setPublicKey(VALID_SSH_PUBLIC_KEY);
        key2.setUserId("1");
        key2.setCreateTime(LocalDateTime.now());

        Page<RsaKey> page = new Page<>(1, 10);
        page.setRecords(Arrays.asList(key1, key2));
        page.setTotal(2);

        when(rsaKeyService.page(any(), any())).thenReturn(page);

        mockMvc.perform(get("/api/rsaKeys")
                .param("current", "1")
                .param("size", "10")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.records").isArray())
                .andExpect(jsonPath("$.result.total").value(2));
    }

    @Test
    void testGenerate() throws Exception {
        RsaKey generatedKey = new RsaKey();
        generatedKey.setId(1L);
        generatedKey.setName("test-key");
        generatedKey.setPublicKey(VALID_SSH_PUBLIC_KEY);
        generatedKey.setUserId("1");
        generatedKey.setCreateTime(LocalDateTime.now());

        when(rsaKeyService.generateKeyPair(eq("test-key"), eq(2048), eq("1"))).thenReturn(generatedKey);

        mockMvc.perform(post("/api/rsaKeys/generate")
                .param("name", "test-key")
                .param("keyLength", "2048")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result.name").value("test-key"));
    }

    @Test
    void testUpload() throws Exception {
        RsaKey uploadedKey = new RsaKey();
        uploadedKey.setId(1L);
        uploadedKey.setName("test-key");
        uploadedKey.setPublicKey(VALID_SSH_PUBLIC_KEY);
        uploadedKey.setUserId("1");
        uploadedKey.setCreateTime(LocalDateTime.now());

        when(rsaKeyService.validateSshPublicKey(VALID_SSH_PUBLIC_KEY)).thenReturn(true);
        when(rsaKeyService.uploadPublicKey(eq("test-key"), eq(VALID_SSH_PUBLIC_KEY), eq("1"))).thenReturn(uploadedKey);

        mockMvc.perform(post("/api/rsaKeys/upload")
                .param("name", "test-key")
                .param("publicKey", VALID_SSH_PUBLIC_KEY)
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200));
    }

    @Test
    void testValidate() throws Exception {
        when(rsaKeyService.validateSshPublicKey(VALID_SSH_PUBLIC_KEY)).thenReturn(true);
        when(rsaKeyService.validateSshPublicKey("invalid-key")).thenReturn(false);

        // 测试有效的公钥
        mockMvc.perform(post("/api/rsaKeys/validate")
                .param("publicKey", VALID_SSH_PUBLIC_KEY))
                .andDo(result -> {
                    System.out.println("\n=== Request Details ===");
                    System.out.println("Request Method: " + result.getRequest().getMethod());
                    System.out.println("Request URI: " + result.getRequest().getRequestURI());
                    System.out.println("Request Parameters: " + result.getRequest().getParameterMap());
                    System.out.println("Request Headers: ");
                    Collections.list(result.getRequest().getHeaderNames()).forEach(headerName ->
                            System.out.println("\t" + headerName + ": " + result.getRequest().getHeader(headerName))
                    );

                    System.out.println("\n=== Response Details ===");
                    System.out.println("Response Status: " + result.getResponse().getStatus());
                    System.out.println("Response Content: " + result.getResponse().getContentAsString());
                })
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").value(true));

        // 测试无效的公钥
        mockMvc.perform(post("/api/rsaKeys/validate")
                .param("publicKey", "invalid-key"))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").value(false));
    }

    @Test
    void testDelete() throws Exception {
        RsaKey existingKey = new RsaKey();
        existingKey.setId(1L);
        existingKey.setUserId("1");

        when(rsaKeyService.getById(1L)).thenReturn(existingKey);
        when(rsaKeyService.removeById(1L)).thenReturn(true);
        when(rsaKeyService.getById(2L)).thenReturn(null);

        // 测试成功删除
        mockMvc.perform(delete("/api/rsaKeys/1")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").value(true));

        // 测试密钥不存在
        mockMvc.perform(delete("/api/rsaKeys/2")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201));
    }

    @Test
    void testUpdateStatus() throws Exception {
        RsaKey existingKey = new RsaKey();
        existingKey.setId(1L);
        existingKey.setUserId("1");

        when(rsaKeyService.getById(1L)).thenReturn(existingKey);
        when(rsaKeyService.updateById(any())).thenReturn(true);
        when(rsaKeyService.getById(2L)).thenReturn(null);

        // 测试成功更新
        mockMvc.perform(put("/api/rsaKeys/1/status")
                .param("isActive", "true")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString();
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(200))
                .andExpect(jsonPath("$.result").value(true));

        // 测试无权操作
        when(mockAuthUser.getId()).thenReturn(2L);
        RsaKey key = new RsaKey();
        key.setId(1L);
        key.setUserId("1"); // 设置不同的用户ID来模拟无权限场景
        when(rsaKeyService.getById(eq(1L))).thenReturn(key);
        mockMvc.perform(put("/api/rsaKeys/1/status")
                .param("isActive", "true")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("无权操作此密钥"));

        // 测试密钥不存在
        when(mockAuthUser.getId()).thenReturn(1L);
        when(rsaKeyService.getById(999L)).thenReturn(null);
        mockMvc.perform(put("/api/rsaKeys/999/status")
                .param("isActive", "true")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("密钥不存在"));
    }

    @Test
    void testErrorCases() throws Exception {
        // 测试非法用户
        when(mockAuthUser.getId()).thenReturn(null);
        mockMvc.perform(get("/api/rsaKeys")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("非法用户"));

        // 测试密钥名称已存在
        when(mockAuthUser.getId()).thenReturn(1L);
        when(rsaKeyService.count(any())).thenReturn(1L);

        mockMvc.perform(post("/api/rsaKeys/generate")
                .param("name", "test-key")
                .param("keyLength", "2048")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("密钥名称已存在"));

        // 测试公钥格式错误
        when(rsaKeyService.validateSshPublicKey("invalid-key")).thenReturn(false);
        mockMvc.perform(post("/api/rsaKeys/validate")
                .param("publicKey", "invalid-key"))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("公钥格式错误，必须是以'ssh-rsa'开头的SSH格式公钥"));

        // 测试无权操作此密钥
        RsaKey key = new RsaKey();
        key.setId(1L);
        key.setUserId("2"); // 设置不同的用户ID来模拟无权限场景
        when(rsaKeyService.getById(eq(1L))).thenReturn(key);
        mockMvc.perform(put("/api/rsaKeys/1/status")
                .param("isActive", "true")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("无权操作此密钥"));

        // 测试密钥不存在
        when(rsaKeyService.getById(eq(999L))).thenReturn(null);
        mockMvc.perform(delete("/api/rsaKeys/999")
                .requestAttr(Authentication.ARAN, mockAuthUser))
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    System.out.println("Response content: " + content);
                })
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(201))
                .andExpect(jsonPath("$.message").value("密钥不存在"));
    }
}
