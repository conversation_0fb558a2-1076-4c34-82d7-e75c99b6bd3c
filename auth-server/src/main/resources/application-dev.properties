spring.datasource.url=***********************************************************************************************************************************************************************
spring.datasource.username=privilege_rw
spring.datasource.password=P7DjOCYW*Yi72RZVr
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

mybatis-plus.mapper-locations=classpath*:mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

cookie.salt=secret.dev

auth.server.url=http://127.0.0.1:7070/authserver/

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.max-file-size=10MB

auth-server.map.bigdata.url=http://127.0.0.1:7070/authserver/
auth-server.map.bigdata.cookieNames=sso-token
auth-server.map.bigdata.cacheExpire=30
auth-server.map.bigdata.loginCacheTime=60
auth-server.map.bigdata.domain=ec.kcde.kscbigdata.cloud
auth-server.map.bigdata.logoutDomain=*

app.ak=auth-server
app.sk=3adcb390-064a-4e28-83a6-5afd88941271

server.servlet.context-path=/authserver
server.port=7070
logging.file.path=/data/log/kcde-auth-server
logging.level.com.ksyun.auth.server.mapper=debug
logging.level.com.ksyun.privilege=debug
logging.level.com.ksyun.privilege.dao.mapper=debug
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %c{36} %t - %m%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %c{36} %t - %m%n
logging.config=classpath:logback-spring.xml
spring.http.encoding.charset=UTF-8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
upload.tmp.dir=/data/temp
oauth2.on-off=TRUE
oauth2.client-id=kcde
oauth2.client-secret=kcde
oauth2.login-url=http://ec.kcde.kscbigdata.cloud/uni-auth/platform/redirect/kcde?nonceId=%s
oauth2.admin=6313b274-0f3a-4718-9265-88c5b48de607
oauth2.authentication-expiration-time=10800
token.expire.seconds=10800
forest.variables.oauth2TokenUrl=http://kcde-uni-auth-platform.vela-system:8003/uni-auth/oauth2Inner
forest.variables.velaApiUrl=http://vela-api-server-velacp.vela-system:8081
forest.connect-timeout=1800000
forest.read-timeout=1800000
#auth serverå®¹å¨åå°å.
forest.variables.kcdeAuthServerUrl=http://127.0.0.1:7070/authserver
#é»è®¤è·³è½¬kcdeé¦é¡µ
kcde.mg.index-url=${server.domain}/#/main/infrastructure
kcde.dls.index-url=${server.domain}/dlstudio/#/main/database
#ç»å½é¡µ
spring.security.oauth2.authserver.login-page-url=${server.domain}/#/login
#ç»å½è¯·æ±æ¥å£
spring.security.oauth2.authserver.login-processing-url=/api/userLogin
#ç»å½æ¥å£,postè¯·æ±
spring.security.oauth2.authserver.logoutPageUrl=/api/logout
spring.security.oauth2.authserver.staticResourceWhiteList=/**
#æ éè®¤è¯çæ¥å£ç½åå
spring.security.oauth2.authserver.fromRequestWhiteList=/api/code,/api/checkLoginType,/api/checkLogin,/api/oAuth2Login,/api/logout,/api/authenticate,/api/privilegeAuthentication,/key,/oauthLoginUrl,/app-token/*,/tenant-aksk/*,/tenant-token/*,/connect/register,/api/clientLogout,/permit/**,/actuator/**,/api/tenants/**
spring.security.oauth2.authserver.issuer=http://auth.vela-system.svc:7070/authserver
#authserveræå¡åç«¯å°å
spring.security.oauth2.authserver.authServerUrl=${server.domain}
#authserveræå¡ngå°å
spring.security.oauth2.authserver.ngServerUrl=http://authserver.kcdeapi.sdns.kscbigdata.cloud
#é¡µé¢å±ç¤ºç»å½æ¹å¼
login.types=PASSWORD,CAS
server.domain=http://ec.kcde.kscbigdata.cloud
#rediséç½®
spring.redis.sentinel.master=redis-cluster
spring.redis.sentinel.nodes=10.5.5.241:26379,10.5.5.21:26379,10.5.5.143:26379
spring.redis.sentinel.password=Zu5GpNWDO&wlHxgS
spring.redis.client-type=jedis
spring.redis.database=6
spring.jedis.pool.enabled=true

#çæ§éç½®
management.metrics.tags.application=AUTHSERVER
management.endpoints.web.exposure.include=prometheus,health
management.health.readinessstate.enabled=true

rsa.public.key.path=classpath:public_key.pem
rsa.private.key.path=classpath:private_key.pem