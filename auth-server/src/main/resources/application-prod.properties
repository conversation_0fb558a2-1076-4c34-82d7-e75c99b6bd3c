# æ¬å°è¿æ¥æ°æ®æº
spring.datasource.url=***********************************************************************************************************************************************
spring.datasource.username=privilege_rw
spring.datasource.password=P7DjOCYWYi72RZVr
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

mybatis-plus.mapper-locations=classpath*:mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# çæ Cookie å¹²é¢å¼
cookie.salt=secret.dev

# è®¤è¯ä¸­å¿
auth.server.url=http://127.0.0.1:7070/authserver

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.max-file-size=10MB

auth-server.map.bigdata.url=http://127.0.0.1:7070/authserver
auth-server.map.bigdata.cookieNames=sso-token
auth-server.map.bigdata.cacheExpire=30
auth-server.map.bigdata.loginCacheTime=60
auth-server.map.bigdata.domain=.kscbigdata.cloud
auth-server.map.bigdata.logoutDomain=.kscbigdata.cloud

upload.tmp.dir=/data/temp

logging.file.path=/data/log/auth-server

# ????
oauth2.on-off=false
oauth2.client-id=kcde
oauth2.client-secret=kcde
oauth2.login-url=http://************:9092/platform/redirect/kcde
oauth2.admin=6313b274-0f3a-4718-9265-88c5b48de607

forest.variables.oauth2TokenUrl=http://************:9092/oauth2Inner
forest.variables.kcdeUrl=http://************:8081
forest.connect-timeout=1800000
forest.read-timeout=1800000

login.types=PASSWORD,CAS

rsa.public.key.path=classpath:public_key.pem
rsa.private.key.path=classpath:private_key.pem
