{"sourceTypeId": null, "sourceType": "mysql", "sourceTypeName": "mysql数据库", "type": null, "source": null, "config": [{"key": "dsName", "value": "${dsName}", "fileVale": null, "type": "input", "name": "数据源名称", "regex": "^[a-zA-Z]{1}[\\w]*$", "placeholder": "输入数据源名", "length": null, "hint": "仅支持字母、数字、下划线组合，且不能以数字和下划线开头，最大100字符", "msg": null, "required": true, "modifiable": false, "display": true, "env": null, "props": []}, {"key": "manageStatus", "value": "32767", "fileVale": null, "type": "radio", "name": "数据管理模式", "regex": null, "placeholder": null, "length": null, "hint": "", "msg": "", "required": true, "modifiable": false, "display": true, "env": null, "props": [{"name": "完全托管", "value": "32767", "msg": "完全托管元数据请配置管理员账号，允许平台创建库表并管理数据权限", "display": true, "modifiable": true, "config": null}, {"name": "非托管私有", "value": "10", "msg": "只同步元数据，其他组件无使用权限", "display": true, "modifiable": true, "config": null}, {"name": "非托管只读", "value": "20", "msg": "用户可以申请读取数据（select）的权限", "display": true, "modifiable": true, "config": null}, {"name": "非托管读写", "value": "30", "msg": "用户可以申请读数据或者写数据的权限即select/insert/update/delete", "display": true, "modifiable": true, "config": null}]}, {"key": "envConfig", "value": "test", "fileVale": null, "type": "radio", "name": "数据源环境", "regex": "", "placeholder": "", "length": null, "hint": "", "msg": null, "required": true, "modifiable": false, "display": false, "env": null, "props": [{"name": "测试环境", "value": "test", "msg": null, "display": false, "modifiable": true, "config": [{"key": null, "value": null, "fileVale": null, "type": "line", "name": "测试环境", "regex": null, "placeholder": null, "length": null, "hint": null, "msg": null, "required": false, "modifiable": true, "display": false, "env": null, "props": []}, {"key": "host", "value": "${host}", "fileVale": null, "type": "input", "name": "地址", "regex": "", "placeholder": "输入连接地址", "length": null, "hint": "", "msg": null, "required": true, "modifiable": true, "display": true, "env": "test", "props": []}, {"key": "port", "value": "3306", "fileVale": null, "type": "input", "name": "端口", "regex": "", "placeholder": "输入端口", "length": null, "hint": "仅支持数字", "msg": null, "required": true, "modifiable": true, "display": true, "env": "test", "props": []}, {"key": "username", "value": "root", "fileVale": null, "type": "input", "name": "用户名", "regex": "", "placeholder": "输入用户名", "length": null, "hint": "", "msg": null, "required": true, "modifiable": true, "display": true, "env": "test", "props": []}, {"key": "password", "value": "root", "fileVale": null, "type": "password", "name": "密码", "regex": "", "placeholder": "输入密码", "length": null, "hint": "", "msg": null, "required": true, "modifiable": true, "display": true, "env": "test", "props": []}, {"key": "instanceName", "value": "", "fileVale": null, "type": "input", "name": "实例", "regex": "", "placeholder": "输入实例名", "length": null, "hint": "", "msg": null, "required": false, "modifiable": false, "display": true, "env": "test", "props": []}, {"key": "netProxyEnv", "value": "opennet", "fileVale": null, "type": "radio", "name": "网络代理环境", "regex": null, "placeholder": null, "length": null, "hint": "", "msg": null, "required": true, "modifiable": false, "display": false, "env": "test", "props": [{"name": "外联网", "value": "extranet", "msg": null, "display": true, "modifiable": true, "config": null}, {"name": "互联网", "value": "internet", "msg": null, "display": true, "modifiable": true, "config": null}, {"name": "开放区", "value": "opennet", "msg": null, "display": true, "modifiable": true, "config": null}]}, {"key": "caseSensitive", "value": "false", "fileVale": null, "type": "radio", "name": "是否开启大小写敏感", "regex": null, "placeholder": null, "length": null, "hint": "", "msg": "", "required": true, "modifiable": false, "display": true, "env": "test", "props": [{"name": "是", "value": "true", "msg": "开启大小写敏感", "display": true, "modifiable": true, "config": null}, {"name": "否", "value": "false", "msg": "不开启大小写敏感", "display": true, "modifiable": true, "config": null}]}]}]}, {"key": "manageMeta", "value": "true", "fileVale": null, "type": "radio", "name": "是否托管", "regex": null, "placeholder": null, "length": null, "hint": "", "msg": "", "required": true, "modifiable": false, "display": false, "env": null, "props": [{"name": "是", "value": "true", "msg": "托管元数据请配置管理员账号，允许平台创建库表并管理数据权限", "display": true, "modifiable": true, "config": null}, {"name": "否", "value": "false", "msg": "不托管元数据平台不能创建库表并管理数据权限", "display": true, "modifiable": true, "config": null}]}, {"key": "description", "value": "", "fileVale": null, "type": "textarea", "name": "数据源描述", "regex": null, "placeholder": null, "length": null, "hint": "", "msg": null, "required": false, "modifiable": true, "display": true, "env": null, "props": []}]}