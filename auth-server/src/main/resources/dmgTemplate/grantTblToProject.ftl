{
    "applyDataList":[
        <#list tableNames as item>
            <#if !item_has_next>
                       {
                            "projectId":7,
                            "dataSourceId":10012,
                            "metadataPrivsList":[
                                {
                                    "dbName":"dmg_test_dev20",
                                    "tableName":"${item}",
                                    "privSet":[
                                        "INSERT",
                                        "UPDATE",
                                        "DELETE",
                                        "SELECT"
                                    ],
                                    "privLevel":"TABLE"
                                }
                            ]
                        }
            </#if>
            <#if item_has_next>
                       {
                            "projectId":7,
                            "dataSourceId":10012,
                            "metadataPrivsList":[
                                {
                                    "dbName":"dmg_test_dev20",
                                    "tableName":"${item}",
                                    "privSet":[
                                        "INSERT",
                                        "UPDATE",
                                        "DELETE",
                                        "SELECT"
                                    ],
                                    "privLevel":"TABLE"
                                }
                            ]
                        },
            </#if>
        </#list>
    ],
    "grantObjList":[
        <#list projectIds as item>
            <#if !item_has_next>
               {
                    "grantObjType":"PROJECT_TYPE",
                    "grantObj":${item}
                }
            </#if>
            <#if item_has_next>
               {
                    "grantObjType":"PROJECT_TYPE",
                    "grantObj":${item}
                },
            </#if>
        </#list>
    ],
    "privType":"ACCESS",
    "env":"TEST",
    "remark":"jmeter_test_mysql"
}