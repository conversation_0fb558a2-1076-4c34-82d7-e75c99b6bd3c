# ????
spring.profiles.active=dev
server.port=7070
server.servlet.context-path=/authserver
server.servlet.encoding.charset=UTF-8
logging.level.com.ksyun.auth.server.mapper=debug
logging.level.com.ksyun.privilege=debug
logging.level.com.ksyun.privilege.dao.mapper=debug
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%X{traceId}] [%thread] %logger{90}: %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%X{traceId}] [%thread] %logger{90}: %msg%n
logging.config=classpath:logback-spring.xml
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
app.ak=auth-server
app.sk=3adcb390-064a-4e28-83a6-5afd88941271