<?xml version="1.0" encoding="utf-8"?>
<assembly>
    <id>assembly</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>conf</outputDirectory>
            <includes>
                <include>application-prod.properties</include>
            </includes>
            <fileMode>0644</fileMode>
            <filtered>true</filtered><!-- 是否进行属性替换 -->
        </fileSet>

        <fileSet>
            <directory>src/main/deploy/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>app.sh</include>
            </includes>
            <fileMode>0755</fileMode>
            <!--如果是脚本，一定要改为unix.如果是在windows上面编码，会出现dos编写问题-->
            <lineEnding>unix</lineEnding>
            <filtered>true</filtered><!-- 是否进行属性替换 -->
        </fileSet>

        <fileSet>
            <directory>target</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>

</assembly>
