package com.ksyun.auth.server.interceptor;

import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.service.TokenService;
import com.ksyun.auth.client.interceptor.AppTokenProcessor;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;


public class AuthServerTenantTokenProcessor extends AppTokenProcessor {
    private static final int ORDER = 6;
    private TokenService tokenService;

    public AuthServerTenantTokenProcessor(TokenService tokenService) {
        super(null, null, null);
        this.tokenService = tokenService;
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        return AccessBy.Method.TENANT_TOKEN;
    }

    @Override
    protected String getHeaderKey(HttpServletRequest request) {
        return request.getHeader(Authentication.HEADER_X_AUTH_TENANT);
    }

    @Override
    protected String getTokenFromServer(String headerKey) {
        return StringUtils.collectionToCommaDelimitedString(tokenService.getTenantTokens(headerKey));
    }
}
