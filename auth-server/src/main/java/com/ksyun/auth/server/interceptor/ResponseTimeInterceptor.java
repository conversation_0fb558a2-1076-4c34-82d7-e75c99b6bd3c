package com.ksyun.auth.server.interceptor;

import com.ksyun.auth.client.Constants;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


@Slf4j
public class ResponseTimeInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        log.debug("{}={}", Constants.TRACE_ID, MDC.get(Constants.TRACE_ID));

        Long startTime = System.currentTimeMillis();
        request.setAttribute(Constants.START_TIME, startTime);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) {
        Long startTime = (Long) request.getAttribute(Constants.START_TIME);
        log.debug("{}={}, {}={}ms", Constants.TRACE_ID, MDC.get(Constants.TRACE_ID), Constants.RESPONSE_TIME, System.currentTimeMillis() - startTime);
    }
}