package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.server.support.MemorySecurityContextRepository;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.auth.utils.encrypt.AESEncryption;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.authentication.session.SessionAuthenticationStrategy;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 该过滤器的作用类似UsernamePasswordAuthenticationFilter，用于处理表单认证请求，将其注册到UsernamePasswordAuthenticationFilter前
 *
 * <AUTHOR>
 * @since v1.8.0-2024/4/22
 */
public class UniLoginAuthenticationProcessingFilter extends AbstractAuthenticationProcessingFilter {

    /**
     * 表示仅处理POST请求
     */
    private boolean postOnly = true;

    private MemorySecurityContextRepository memorySecurityContextRepository;

    /**
     * 构造函数传入 loginProcessingUrl 和认证管理器，表示该filter支持认证处理接口，通常为登录页，提交用户名密码的POST接口地址
     * 这意味着，该接口并不需要真的存在，已经存在该接口，可以将接口的逻辑迁移到Provider中
     *
     * @param loginProcessingUrl 登录逻辑处理地址，即表单提交地址
     * @param authenticationManager 认证管理器，主要实现为ProviderManager 内部组装了多个Provider，
     *                              根据 Authentication的对象类型确定由谁处理
     */
    public UniLoginAuthenticationProcessingFilter(String loginProcessingUrl, AuthenticationManager authenticationManager,MemorySecurityContextRepository memorySecurityContextRepository) {
        super(new AntPathRequestMatcher(loginProcessingUrl, "POST"), authenticationManager);
        this.memorySecurityContextRepository = memorySecurityContextRepository;
    }

    /**
     * 认证方法，该方法尝试认证
     * @param request
     * @param response
     * @return
     * @throws AuthenticationException
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException, ServletException {
        // 非POST请求抛出认证异常
        if (this.postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }

        //初始认证信息，主要是从请求中获取表单，包装为UniLoginAuthenticationToken
        UniLoginAuthenticationToken authRequest = new UniLoginAuthenticationToken(this.getAuthParams(request));
        // details作用暂时不详
        setDetails(request, authRequest);
        //调用认证管理器进行验证（即调用UniLoginAuthenticationProvider）
        return this.getAuthenticationManager().authenticate(authRequest);
    }

    /**
     *调用requiresAuthentication方法，以确定请求是否用于身份验证，是否应由此筛选器处理。
     * 如果是身份验证请求，则会调用attemptAuthentication来执行身份验证。然后有三种可能的结果：
     * 返回一个身份验证对象。将调用配置的SessionAuthenticationStrategy（以处理任何与会话相关的行为，如创建新会话以防止会话固定攻击），然后调用successfulAuthentication（HttpServlet请求、HttpServlet响应、FilterChain、Authentication）方法
     * 身份验证过程中发生AuthenticationException。将调用未成功的身份验证方法
     * 返回Null，表示身份验证过程未完成。然后，该方法将立即返回，假设子类已经完成了任何必要的工作（如重定向）以继续身份验证过程。
     * 假设在返回的Authentication对象不为null的情况下，此方法将接收稍后的请求。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        doFilter((HttpServletRequest) request, (HttpServletResponse) response, chain);
    }

    private void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (!requiresAuthentication(request, response)) {
            chain.doFilter(request, response);
            return;
        }
        //暂时只能这么传.配置那边给的不生效.不知道为什么.导致无法存入内存中
        setSecurityContextRepository(memorySecurityContextRepository);
        try {
            Authentication authenticationResult = attemptAuthentication(request, response);
            if (authenticationResult == null) {
                // return immediately as subclass has indicated that it hasn't completed
                return;
            }

            //需要它保存缓存信息
            successfulAuthentication(request, response, chain, authenticationResult);
        }
        catch (InternalAuthenticationServiceException failed) {
            this.logger.error("An internal error occurred while trying to authenticate the user.", failed);
            unsuccessfulAuthentication(request, response, failed);
        }
        catch (AuthenticationException ex) {
            // Authentication failed
            unsuccessfulAuthentication(request, response, ex);
        }
    }

    /**
     * 获取登录表单参数Map
     *
     * @param request 登录请求
     * @return 登录表单参数Map
     */
    private Map<String, String> getAuthParams(HttpServletRequest request) {
        //提取全部form参数
        Map<String, String[]> originRequestParamMap = request.getParameterMap();
        Assert.notEmpty(originRequestParamMap, "Uni Login Auth Params should not empty!");
        //转换后的form参数
        Map<String, String> convertRequestParamMap = new HashMap<>(originRequestParamMap.size());
        //转换form参数
        for (Map.Entry<String, String[]> entry : originRequestParamMap.entrySet()) {
            String paramName = entry.getKey();
            String[] paramVals = entry.getValue();
            if (null == paramVals || 0 >= paramVals.length || !StringUtils.hasText(paramVals[0])) {
                continue;
            }
            // 请求中获取到password后直接改为原文
            if (paramName.equals("password")) {
                // 用AES解密算法，将密码改回原文
                paramVals[0] = new AESEncryption().decrypt(paramVals[0], PropConfig.AES_SECRET_KEY);
            }
            convertRequestParamMap.put(paramName, paramVals[0]);
        }
        return convertRequestParamMap;
    }

    /**
     * 通常是一个 WebAuthenticationDetails 对象 内部有两个属性
     * remoteAddress
     * sessionId
     * TODO：暂时没发现有什么用
     *
     * @param request
     * @param authRequest
     */
    protected void setDetails(HttpServletRequest request, UniLoginAuthenticationToken authRequest) {
        authRequest.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }

}
