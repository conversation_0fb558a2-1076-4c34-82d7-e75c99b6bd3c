package com.ksyun.auth.server.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.callback.OnLoadCookie;
import com.dtflys.forest.http.ForestCookie;
import com.dtflys.forest.http.ForestCookies;
import com.dtflys.forest.http.ForestRequest;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.inter.KcdeLoginClient;
import com.ksyun.auth.inter.config.Oauth2Config;
import com.ksyun.auth.inter.response.Oauth2LogoutResp;
import com.ksyun.auth.inter.response.Oauth2TokenResp;
import com.ksyun.auth.inter.response.Oauth2UserinfoResp;
import com.ksyun.auth.inter.service.Oauth2Service;
import com.ksyun.auth.server.config.Oauth2ServerProps;
import com.ksyun.auth.server.constant.SecurityConstants;
import com.ksyun.auth.server.utils.Captcha;
import com.ksyun.auth.server.utils.CookieUtils;
import com.ksyun.auth.server.utils.NonceIdUtils;
import com.ksyun.auth.service.PrivilegeService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.utils.PrivilegeUtils;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.auth.utils.encrypt.AESEncryption;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.User;
import com.ksyun.common.entity.UserLoginVo;
import com.ksyun.common.enums.UserSourceEnum;
import com.ksyun.common.utils.CodeUtils;
import com.ksyun.common.utils.DomainUtils;
import com.ksyun.common.utils.IpCheckUtils;
import com.ksyun.common.utils.Md5Utils;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.io.IOException;
import java.security.interfaces.RSAPrivateKey;
import java.time.Instant;
import java.util.List;
import java.util.*;


/**
 * 用户登录服务类
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class LoginController {

    private final UserService userService;
    private final PrivilegeService privilegeService;

    @Autowired
    JWKSource jwkSource;

    @Autowired
    Oauth2Service oauth2Service;

    @Autowired
    Oauth2Config oauth2Config;

    @Value("${login.types}")
    private String loginTypes;

    @Autowired
    private KcdeLoginClient kcdeLoginClient;

    @Value("${kcde.mg.index-url}")
    private String kcdeMGIndexUrl;
    @Value("${kcde.dls.index-url}")
    private String kcdeDlsIndexUrl;

    @Autowired
    private Oauth2ServerProps oauth2ServerProps;
    /**
     * 获取验证码
     *
     * @param request
     * @param response
     * @return
     */
    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/code")
    public Response code(HttpServletRequest request, HttpServletResponse response) {
        String code = CodeUtils.getRandomString(PropConfig.KAPT_CHACHAR_LENGTH);
        log.info("验证码 code：{}", code);
        String codeId = request.getSession().getId();
        CacheUtil.set(Constants.CODE_PREFIX + codeId, code, 600);
        response.setHeader("Access-Code", codeId);

        JSONObject rtn = new JSONObject();
        rtn.put("prefix", "data:image/png;base64,");
        rtn.put("base64", Captcha.toBase64(code.toCharArray(), 130, 48, new Font("Verdana", Font.PLAIN, 32)));
        rtn.put("codeId", codeId);
        return Response.success(rtn);
    }


    /**
     * 检查登录类型
     * 平台账密登录 passwordLogin
     * 小米人登录  xiaomiCasLogin
     *
     * @param request
     * @param response
     * @return
     */
    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/checkLoginType")
    public Response checkLoginType(HttpServletRequest request, HttpServletResponse response) {
        //生成唯一nonceId,确保全局唯一认证状态
        String nonceId = NonceIdUtils.getNonce(request);
        CookieUtils.setNonceIdCookie(request, response, "1", nonceId);
        log.debug("checkLoginType set cookie: nonceId = [{}]", nonceId);
        JSONObject rtn = new JSONObject();
        rtn.put("loginTypes", loginTypes);
        return Response.success(rtn);
    }

    /**
     * 判断用户是否登录
     *
     * @param parameter
     * @param request
     * @return
     */
    @AccessBy(AccessBy.Method.NULL)
    @PostMapping(value = "/checkLogin")
    public Response checkLogin(@RequestBody @Validated LoginVo parameter, HttpServletRequest request) {
        String sessionId = request.getSession().getId();
        //1. 校验登陆验证码是否正确
        String code = (String) CacheUtil.get(Constants.CODE_PREFIX + sessionId);
        if (code == null) {
            return Response.success(PrivilegeUtils.checkResult("001", "验证码输入错误,请重新输入验证码", null, "0"));
        }
        if (StringUtils.equalsIgnoreCase(parameter.getCode(), code)) {
            CacheUtil.del(Constants.CODE_PREFIX + sessionId);
        } else {
            return Response.success(PrivilegeUtils.checkResult("001", "验证码输入错误,请重新输入验证码", null, "0"));
        }
        Map<String, Object> resultMap = userService.checkLogin(parameter, request.getHeader("Access-Code"));
        return Response.success(resultMap);
    }

    /**
     * 用户登录
     *
     * @param parameter
     * @param request
     * @param response
     * @return
     */
//    @AccessBy(AccessBy.Method.NULL)
//    @Authorize(Authorize.Type.NONE)
//    @PostMapping(value = "/userLogin")
//    @ConditionalOnExpression("${oauth2.on-off}")
//    public Response userLogin(@RequestBody @Validated LoginVo parameter, HttpServletRequest request, HttpServletResponse response) {
//        try {
//            String sessionId = request.getSession().getId();
//            //1. 校验登陆验证码是否正确
//
//            String code = (String) CacheUtil.get(Constants.CODE_PREFIX + sessionId);
//            if (!"888".equalsIgnoreCase(parameter.getCode()) && (code == null || !StringUtils.equalsIgnoreCase(parameter.getCode(), code))) {
//                Assert.isTrue(false,"验证码输入错误,请重新输入验证码");
//            }
//            CacheUtil.del(Constants.CODE_PREFIX + sessionId);
//            log.info("userLogin params：{}",JSON.toJSON(parameter));
//            AuthUser authUser = doUserLogin(parameter, request, response,null);
//            return Response.success(authUser);
//        } catch (RuntimeException e) {
//            e.printStackTrace();
//            log.error("LoginController#userLogin error", e);
//            return Response.failure().message(e.getMessage());
//        }
//    }
    @AccessBy(AccessBy.Method.NULL)
    @Authorize(Authorize.Type.NONE)
    @GetMapping(value = "/oAuth2Login")
    public void oAuth2Login(@RequestParam("code") String code,
                            @RequestParam("nonceId") String nonceId,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            if (!oauth2Config.getOnOff()) {
                Assert.isTrue(false, "对接统一认证服务[onOff]未开启");
            }
            if (StringUtils.isEmpty(code)) {
                Assert.isTrue(false, "参数[code]不能为空");
            }
            if (StringUtils.isEmpty(nonceId)) {
                log.error("code = [{}] 的 参数[nonceId]不能为空", code);
                Assert.isTrue(false, "参数[nonceId]不能为空");
            }
            Oauth2TokenResp oauth2TokenResp = oauth2Service.getOauth2Token(code);
            log.info("get token:{}", oauth2TokenResp.getAccess_token());

            String token = oauth2TokenResp.getAccess_token();
            Oauth2UserinfoResp userinfo = oauth2Service.getOauth2Userinfo(code, token);
            log.info("oAuth2Login userinfo:{}", userinfo);
            UserAddOidcVo userAddOidcVo = new UserAddOidcVo();
            BeanUtils.copyProperties(userinfo, userAddOidcVo);
            userAddOidcVo.setOidcId(userinfo.getId());
            //oidId 配置一个管理员
            if (oauth2Config.getAdmin().equalsIgnoreCase(userinfo.getId())) {
                userAddOidcVo.setRoleName("管理员");
            } else {
                userAddOidcVo.setRoleName("访客");
            }
            //判断是否需要新建用户
            Map<String, Object> map = userService.addUser(userAddOidcVo);
            User user = (User) map.get("user");
            UserLoginVo userLoginVo = (UserLoginVo) map.get("userLoginVo");
            userLoginVo.setNonceId(nonceId);
            userLoginVo.setSource(UserSourceEnum.CAS.name());
            //3.0 pre 新增密码加密
            String encryptPassword = new AESEncryption().encrypt(userLoginVo.getPassword(), PropConfig.AES_SECRET_KEY);
            userLoginVo.setPassword(encryptPassword);

            AuthUser authUser = doUserLogin(user.getOidcId(), request, response, token, nonceId);
            //应该不需要
            request.getSession().setAttribute(SecurityConstants.NONCE_HEADER_NAME, nonceId);
            //帮助用户进行认证
            authorization(nonceId, request, userLoginVo, authUser);
            //获取最后重定向的地址
            String target = getTargetUrl(nonceId);
            response.sendRedirect(target);
            //return Response.success(authUser);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("LoginController#oAuth2Login error", e);
            //return Response.failure().message(e.getMessage());
        }
    }

    private void authorization(String nonceId, HttpServletRequest request, UserLoginVo userLoginVo, AuthUser authUser) {
        String domain = DomainUtils.getDomain(request, PropConfig.AUTH_SERVER_DOMAIN, "1");
        //认证请求时.带上nonceIdcookie
        Response.RichResponse<AuthUser> authUserOauthResponse = kcdeLoginClient.kcdeLogin(userLoginVo, nonceId, new OnLoadCookie() {
            @Override
            public void onLoadCookie(ForestRequest req, ForestCookies cookies) {
                cookies.strict(Boolean.FALSE)
                        .addCookie(new ForestCookie(SecurityConstants.NONCE_HEADER_NAME, nonceId).setDomain(domain));
            }
        });
        //验证登录结果.
        //Assert.isTrue(StringUtils.equalsIgnoreCase("success", result), "登录失败");
        log.info("oauth登录结果状态 = [{}],oauth登录结果信息 = [{}],oauth登录用户信息[{}]", authUserOauthResponse.getStatus(), authUserOauthResponse.getMessage(), authUserOauthResponse.getResult().toString());
        if (authUserOauthResponse.getStatus() == 200) {
            log.info("设置旧过滤器放行参数");
            request.setAttribute(Authentication.ARAN, authUser);
            SecurityContext sc = SecurityContextHolder.getContext();
            sc.setAuthentication(authUser);
        }
    }

    private String getTargetUrl(String nonceId) {
        String target = (String) CacheUtil.get(SecurityConstants.NONCE_TARGET_URL_PREFIX_KEY + nonceId);
        String serverLogout = (String) CacheUtil.get(SecurityConstants.NONCE_SERVER_LOGOUT_PREFIX_KEY + nonceId);
        if (StringUtils.isNotEmpty(target)) {
            log.debug("重定向地址: [{}]", target);
            return target;
        } else if (StringUtils.isNotEmpty(serverLogout) && "dls".equalsIgnoreCase(serverLogout)) {
            target = kcdeDlsIndexUrl;
            log.debug("重定向地址: [{}]", target);
            return target;
        } else {
            target = kcdeMGIndexUrl;
            log.debug("重定向地址: [{}]", target);
            return target;
        }
    }

    /**
     * 退出
     *
     * @param request
     * @param response
     * @return
     */
    @AccessBy(AccessBy.Method.COOKIE)
    @Authorize(Authorize.Type.NONE)
    @DeleteMapping(value = "/logout")
    public Response logout(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = AuthenticationHelper.getAuthenticationRelatedCookies(request);
        Objects.requireNonNull(cookies, "cookie is null");
        String logoutDomain = DomainUtils.getLogoutDomain(request, PropConfig.AUTH_SERVER_LOGOUTDOMAIN);
        LogoutVo logoutVo = new LogoutVo();
        for (Cookie cookie : cookies) {
            if (com.ksyun.common.constant.Constants.SSO_TOKEN_COOKIE_NAME.equalsIgnoreCase(cookie.getName())) {
                CacheUtil.del(com.ksyun.common.constant.Constants.AUTH_LOGIN_USER_PREFIX + cookie.getValue());
                CacheUtil.del(com.ksyun.common.constant.Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(cookie.getValue()));

                //如果开启了统一认证 退出时调退出接口
                if (oauth2Config.getOnOff()) {
                    Oauth2LogoutResp resp = oauth2Service.logoutOauth2(cookie.getValue());
                    logoutVo.setLogoutUrl(resp.getLogOutUrl());
                }
            }
            Cookie newCooke = CommonUtils.newJavaCookie(cookie.getName(), cookie.getValue(), logoutDomain, 0);
            response.addCookie(newCooke);
            log.info("Set Logout Cookie: cookie={},domain={}", cookie, newCooke.getDomain());
        }
        if (oauth2Config.getOnOff()) {
            logoutVo.setLoginUrl(oauth2Config.getLoginUrl());
            return Response.success(logoutVo);
        }
        return Response.success();
    }

    @AccessBy(AccessBy.Method.COOKIE)
    @Authorize(Authorize.Type.NONE)
    @GetMapping(value = "/clientLogout")
    public void clientLogout(HttpServletRequest request, HttpServletResponse response) {
        String target = request.getParameter("target");
        String nonceId = request.getSession(Boolean.FALSE).getId();
        CacheUtil.set(SecurityConstants.NONCE_TARGET_URL_PREFIX_KEY + nonceId, target, SecurityConstants.DEFAULT_TARGATURL_EXPIRATION_TIME);
        String loginPageUrl = oauth2ServerProps.getLoginPageUrl();
        loginPageUrl += "?target=" + target + "&nonceId=" + nonceId;
        log.info("clientLogout 重定向地址: [{}]", loginPageUrl);
        try {
            response.sendRedirect(loginPageUrl);
        } catch (IOException e) {
            log.error("clientLogout 重定向失败");
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取登录用户信息
     *
     * @param authentication
     * @return
     */
    @AccessBy(AccessBy.Method.COOKIE)
    @GetMapping(value = "/authenticate")
    public Response authenticate(@RequestAttribute(Authentication.ARAN) AuthUser authentication) {
        log.info("authServer Version env = {},profile={}", "0.3", PropConfig.PROFILE);
        return Response.success(authentication, "0.3", PropConfig.PROFILE);
    }

    @GetMapping(value = "/privilegeAuthentication")
    public Response checkPrivilege(HttpServletRequest request, @RequestParam(required = false) Long projectId,
                                   Long userId, String url, String ak, @RequestParam(required = false) String network) {
        Assert.notNull(url, "url不能为空");
        Assert.notNull(userId, "userId不能为空");
        //兼容url包含两个/的情况
        url = url.replace("//", "/");
        CheckPrivilegeResultVo checkPrivilegeResultVo = userService.getCurrentUserIsPrivilege(ak, projectId, userId, url, network);

        return new Response.RichResponse<>(Response.CODE_SUCCESS, checkPrivilegeResultVo.getMessage(), checkPrivilegeResultVo.isPassed());
    }

//    private AuthUser doUserLogin(LoginVo parameter, HttpServletRequest request, HttpServletResponse response, String token) {
//        try {
//            AuthUser user = userService.getUserByLoginVo(parameter.getUsername(), parameter.getPassword(), "");
//            if (user != null) {
//                // 1. 判断允许当前用户登陆最大次数
//                /*String md5LoginCache = Md5Utils.getLoginCacheByIdAndName(user.getId(), user.getName());
//                Set<String> prefixKeyValue = CacheUtil.get(Constants.AUTH_LOGIN_USER_PREFIX.concat(md5LoginCache));
//                log.info("userLogin redis name={}, id={},cacheResult={}", user.getName(), user.getId(), prefixKeyValue);
//                if (prefixKeyValue.size() >= PropConfig.USER_LOGIN_MAX) {
//                    for (String key : prefixKeyValue) {
//                        // 强制清除当前已登陆的所有用户
//                        CacheUtil.del(key);
//                        log.info("remove user token name={}, id={}, key={}, userCachePrefix={}", user.getName(), user.getId(), key, Constants.AUTH_LOGIN_USER_PREFIX.concat(md5LoginCache));
//                    }
//                }*/
//                // 2.浏览器种植cookie
//                setSsoTokenCookie(request, response, user, "1", token);
//
//                log.info("start rewriteSessionId....");
//                CodeUtils.rewriteSessionId(request);
//            }
//            return user;
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("请求统一用户权限服务登陆查询接口失败!", e);
//            throw new RuntimeException(e.getMessage());
//        }
//    }

    private AuthUser doUserLogin(String oidcId, HttpServletRequest request, HttpServletResponse response, String token, String nonceId) {
        try {
            AuthUser user = userService.getUserBySourceAndOidcId(UserSourceEnum.CAS.name(), oidcId);
            if (user != null) {
                // 2.浏览器种植cookie
                CookieUtils.setSsoTokenCookie(request, response, user, "1", token);
                CookieUtils.setNonceIdCookie(request, response, "1", nonceId);
                log.info("start rewriteSessionId....");
                CodeUtils.rewriteSessionId(request);
            }
            return user;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("请求统一用户权限服务登陆查询接口失败!", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private void setSsoTokenCookie(HttpServletRequest request, HttpServletResponse response, AuthUser user, String type, String token, String nonceId) {
        log.info("start setSsoTokenCookie....");
        if (token == null) {
            token = CodeUtils.generateToken(user.getId(), user.getName());
        }
        CacheUtil.set(Constants.AUTH_LOGIN_USER_PREFIX.concat(token), JSON.toJSONString(user), PropConfig.TOKEN_EXPIRE_SECONDS);
        // 设置客户端Cookie，保存令牌ID
        String cookieName = Constants.SSO_TOKEN_COOKIE_NAME;
        String domain = DomainUtils.getDomain(request, PropConfig.AUTH_SERVER_DOMAIN, type);
        response.addCookie(CommonUtils.newJavaCookie(cookieName, token, domain, PropConfig.AUTH_SERVER_CACHE_EXPIRE));
        log.info("setSsoTokenCookie：cookie={}，token={}，domain={}", cookieName, token, domain);
        try {
            Set<String> prefixKeyValue = null;//CacheUtil.getPrefixKeyValue(Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(Md5Utils.getLoginCacheByIdAndName(user.getId(), user.getName())).concat("-"));
            if (prefixKeyValue != null) {
                for (String key : prefixKeyValue) {
                    // 强制清除当前已登陆的所有用户
                    CacheUtil.del(key);
                }
            }
            // 保存客户端cookie，IP和UA，用户越权访问校验
            String ipAddress = IpCheckUtils.getClientIpAddr(request);
            log.info("setSsoTokenCookie: 缓存越权数据 key={}, value={}", Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(token), JSON.toJSONString(Arrays.asList(ipAddress, "")));
            CacheUtil.set(Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(token), Md5Utils.encodeByMD5(JSON.toJSONString(Arrays.asList(ipAddress, ""))), PropConfig.TOKEN_EXPIRE_SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存越权访问令牌失败", e);
        }
    }

    /**
     * 获取JWT格式的ID Token
     */
    @GetMapping(value = "/idToken")
    @Authorize(Authorize.Type.NONE)
    public Response getIdToken(@RequestAttribute("ARAN") Authentication authentication) throws JOSEException {
        if (authentication instanceof AuthUser) {
            AuthUser authUser = (AuthUser)authentication;

            UserVo userVo = userService.getUserById(authUser.getId()).get();
            List<String> privilegeCode = privilegeService.getPrivilegeByUser(null, authUser, null);

            Instant now = Instant.now();
            // 创建JWT Claims
            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .jwtID(UUID.randomUUID().toString())
                    .subject(authUser.getName())
                    .issuer(oauth2ServerProps.getIssuer())
                    .issueTime(Date.from(now))
                    .expirationTime(Date.from(now.plusSeconds(1800)))
                            .claim("user_id", String.valueOf(userVo.getId()))
                            .claim("user_name", userVo.getName())
                            .claim("user_full_name", userVo.getAlias())
                            .claim("permissions", privilegeCode)
                            .claim("data_version", "-1")
                            .claim("key_version", "-1")
                            .build();


            // 从jwkSource获取签名密钥
            JWKSet jwkSet = ((ImmutableJWKSet)jwkSource).getJWKSet();
            JWK jwk = jwkSet.getKeys().get(0);
            RSAPrivateKey privateKey = (RSAPrivateKey)jwk.toRSAKey().toPrivateKey();

            // 创建并签名JWT
            SignedJWT signedJWT = new SignedJWT(
                    new JWSHeader.Builder(JWSAlgorithm.RS256)
                            .keyID(jwk.getKeyID())
                            .build(),
                    claimsSet);
            signedJWT.sign(new RSASSASigner(privateKey));
            return Response.success(signedJWT.serialize());
        }
        return Response.failure("未登录");
    }

}
