package com.ksyun.auth.server.config;

import com.ksyun.auth.server.support.MemorySecurityContextRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * SpringSecurity OAuth2登录配置
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
//@EnableConfigurationProperties(Oauth2ServerProps.class)
public class OAuth2SecurityConfig implements WebMvcConfigurer {

    private Oauth2ServerProps oauth2ServerProps;
    private MemorySecurityContextRepository memorySecurityContextRepository;
    public OAuth2SecurityConfig(Oauth2ServerProps oauth2ServerProps, MemorySecurityContextRepository memorySecurityContextRepository) {
        this.oauth2ServerProps = oauth2ServerProps;
        this.memorySecurityContextRepository = memorySecurityContextRepository;
    }

    @Bean
    @Order(1)
    public SecurityFilterChain oauth2SecurityFilterChain(HttpSecurity http) throws Exception {
        // 异常转发处理器，当判断出未登录时转发到/login 认证入口
        // 指定认证信息存储在内存中.后续也从内存中读取认证信息.key为nonceId
        http.
                securityContext(context -> context.securityContextRepository(memorySecurityContextRepository));
        http
                .authorizeHttpRequests((authorize) -> {
                    authorize.requestMatchers("/permit/**","/login","/userLogin")
                            .permitAll()
                            .anyRequest()
                            .authenticated();
                }).exceptionHandling((exceptions) -> exceptions
                        .defaultAuthenticationEntryPointFor(
                                new LoginUrlAuthenticationEntryPoint("/login"),
                                new MediaTypeRequestMatcher(MediaType.TEXT_HTML)
                        )
                )
                // Accept access tokens for User Info and/or Client Registration
                .oauth2ResourceServer((resourceServer) -> resourceServer.jwt(withDefaults()))

                // oauth2Login配置 各客户端统一登录
/*                .oauth2Login(oauth2Login -> {
                    // 登录逻辑处理url 此处应该时/api/userLogin
                    oauth2Login
                            // 此处应改为 kcde-manager/dls的统一登录页 可以在配置文件中统一配置
                            .loginPage(oauth2ServerProps.getLoginPageUrl())
                            // 登录逻辑处理url 此处应该时/api/userLogin
                            .loginProcessingUrl(oauth2ServerProps.getLoginProcessingUrl())
//                            .successHandler(authenticationSuccessHandler())
//                            .failureHandler(authenticationFailureHandler())
                    ;
                })*/
                // oauth2Client配置
                .oauth2Client(withDefaults())
        ;
        return http.build();
    }


}
