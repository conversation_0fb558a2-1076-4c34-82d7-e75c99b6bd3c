package com.ksyun.auth.server.anno;

import com.ksyun.auth.server.config.AuthorizationServerConfig;
import com.ksyun.auth.server.config.FormSecurityConfig;
import com.ksyun.auth.server.config.OAuth2SecurityConfig;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;


/**
 * 启用OIDC Authorization Server
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-02-21 10:35
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import({AuthorizationServerConfig.class,
        FormSecurityConfig.class,
//        OAuth2SecurityConfig.class
})
public @interface EnableOidcAuthorizationServer {

}