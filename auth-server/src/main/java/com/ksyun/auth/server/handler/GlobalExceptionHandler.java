package com.ksyun.auth.server.handler;

import com.ksyun.common.constant.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

@RestControllerAdvice()
public class GlobalExceptionHandler {
    private static Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Response handleHttpMessageNotReadableException(MethodArgumentNotValidException e) {
        logger.error("请求参数校验失败", e);
        BindingResult result = e.getBindingResult();
        List<ObjectError> errors = result.getAllErrors();
        String firstError = errors.isEmpty() ? null : errors.iterator().next().getDefaultMessage();
        String errorMessage = StringUtils.isEmpty(firstError) ? Response.MESSAGE_FAILURE : firstError;
        return Response.failure().message(errorMessage);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(RuntimeException.class)
    public Response handleHttpMessageNotReadableException(RuntimeException e) {
        logger.error("运行时异常", e);
        String errorMessage = e.getMessage();
        return Response.failure().message(errorMessage);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BindException.class)
    public Response handleHttpMessageBindException(Exception e) {
        logger.error("请求参数转换出错", e);
        return Response.failure().message("请求参数转换出错");
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public Response handleHttpMessageNotReadableException(Exception e) {
        logger.error("服务器内部错误", e);
        return Response.failure().message(e.getMessage());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Response handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        logger.error("json转换错误", e);
        return Response.failure().message("json字符串格式不正。");
    }
}
