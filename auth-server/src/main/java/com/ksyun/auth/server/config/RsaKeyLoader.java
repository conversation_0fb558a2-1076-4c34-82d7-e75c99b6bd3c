package com.ksyun.auth.server.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.*;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * 生成私钥
 * openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:2048
 *
 * 从私钥中提取公钥
 * openssl rsa -pubout -in private_key.pem -out public_key.pem
 *
 */
@Component
public class RsaKeyLoader {

    @Value("${rsa.private.key.path}")
    private String privateKeyFile;
    @Value("${rsa.public.key.path}")
    private String publicKeyFile;

    @Autowired
    private ResourceLoader resourceLoader;

    public KeyPair loadRsaKeyPair() throws Exception {
        PublicKey publicKey = loadPublicKey();
        PrivateKey privateKey = loadPrivateKey();
        return new KeyPair(publicKey, privateKey);
    }

    private PublicKey loadPublicKey() throws Exception {
        Resource resource = resourceLoader.getResource(publicKeyFile);
        try (InputStream inputStream = resource.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

            StringBuilder publicKeyContent = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.startsWith("-----")) {
                    publicKeyContent.append(line);
                }
            }
            byte[] privateKeyBytes = Base64.getDecoder().decode(publicKeyContent.toString());
            X509EncodedKeySpec spec = new X509EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(spec);
        }
    }

    private PrivateKey loadPrivateKey() throws Exception {
        Resource resource = resourceLoader.getResource(privateKeyFile);
        try (InputStream inputStream = resource.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

            StringBuilder privateKeyContent = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.startsWith("-----")) {
                    privateKeyContent.append(line);
                }
            }
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyContent.toString());
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(spec);
        }
    }
}
