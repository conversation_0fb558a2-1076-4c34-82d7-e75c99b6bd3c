package com.ksyun.auth.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("kcde")
                .allowedOriginPatterns("*")
                .allowedHeaders("*")
                .allowCredentials(true)
                //.allowedMethods("GET", "POST", "DELETE", "PUT", "PATCH")
                .allowedMethods("*")
                .maxAge(3600);
   }

}