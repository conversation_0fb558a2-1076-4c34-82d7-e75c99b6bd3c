package com.ksyun.auth.server.controller;

import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.inter.config.Oauth2Config;
import com.ksyun.auth.service.TokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;

@RestController
public class TokenController {

    private static final Logger RUN_LOG = LoggerFactory.getLogger(TokenController.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    Oauth2Config oauth2Config;

    /**
     * 获取动态码
     * 该请求不需要做权限验证，因为其它应用要先拿到动态码才能加密出 TOKEN，所以如果这个请求验证 TOKEN，会产生死循环
     */
    @GetMapping("/key")
    @AccessBy(AccessBy.Method.NULL)
    public String getDynamicKey() {
        try {
            return tokenService.getDsk();
        } catch (Exception e) {
            RUN_LOG.error("Load Dsk Error");
            return null;
        }
    }

    @GetMapping("/oauthLoginUrl")
    @AccessBy(AccessBy.Method.NULL)
    public String getOauthLoginUrl(@RequestParam String nonceId) {
        try {
            if (oauth2Config.getOnOff() && !StringUtils.isEmpty(oauth2Config.getLoginUrl())) {
                String loginUrl = String.format(oauth2Config.getLoginUrl(), nonceId);
                RUN_LOG.debug("Load oauth login url, loginUrl={}", loginUrl);
                return loginUrl;
            }else {
                RUN_LOG.error("未开启oauth认证或oauth认证地址为空");
                return "未开启oauth认证或oauth认证地址为空";
            }

        } catch (Exception e) {
            RUN_LOG.error("Load oauth login url Error");
            return "获取oauth地址异常";
        }
    }

    /**
     * 获取指定APP的TOKEN，该 TOKEN 是通过指定当前动态码和APP对应的SK即时生成的
     */
    @GetMapping("/app-token/{app}")
    @AccessBy({AccessBy.Method.APP_TOKEN, AccessBy.Method.TENANT_TOKEN, AccessBy.Method.COOKIE})
    public String syncToken(@PathVariable @NotNull String app) {
        try {
            return StringUtils.collectionToDelimitedString(tokenService.getAppTokens(app), ",");
        } catch (Exception e) {
            RUN_LOG.error("Load App Tokens Error, app={}", app);
            return null;
        }
    }

    /**
     * 获取指定Tenant的TOKEN，该 TOKEN 是通过指定当前动态码和Tenant对应的SK即时生成的
     */
    @GetMapping("/tenant-aksk/{tenant}")
    @AccessBy({AccessBy.Method.APP_TOKEN, AccessBy.Method.TENANT_TOKEN})
    public String syncTenantAkSk(@PathVariable @NotNull String tenant) {
        try {
            return tokenService.getTenantAkSk(tenant);
        } catch (Exception e) {
            RUN_LOG.error("Load Tenant Tokens Error, tenant={}", tenant);
            return null;
        }
    }

    /**
     * 获取指定Tenant的TOKEN，该 TOKEN 是通过指定当前动态码和Tenant对应的SK即时生成的
     */
    @GetMapping("/tenant-token/{tenant}")
    @AccessBy({AccessBy.Method.TENANT_TOKEN})
    public String syncTenantToken(@PathVariable @NotNull String tenant) {
        try {
            return StringUtils.collectionToDelimitedString(tokenService.getTenantTokens(tenant), ",");
        } catch (Exception e) {
            RUN_LOG.error("Load Tenant Tokens Error, tenant={}", tenant);
            return null;
        }
    }
}
