package com.ksyun.auth.server.config;

import com.ksyun.auth.inter.Oauth2Client;
import com.ksyun.auth.inter.config.Oauth2Config;
import com.ksyun.auth.server.handler.CustomLogoutSuccessHandler;
import com.ksyun.auth.server.handler.LoginTargetAuthenticationEntryPoint;
import com.ksyun.auth.server.security.handler.*;
import com.ksyun.auth.server.support.MemorySecurityContextRepository;
import com.ksyun.auth.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsProcessor;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.IOException;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * SpringSecurity 表单登录配置
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
@Slf4j
@EnableConfigurationProperties(Oauth2ServerProps.class)
public class FormSecurityConfig implements WebMvcConfigurer {

    /**
     * OAuth2认证服务器端配置属性
     */
    private Oauth2ServerProps oauth2ServerProps;
    /**
     * Client注册信息DAO
     */
    private RegisteredClientRepository registeredClientRepository;

    private MemorySecurityContextRepository memorySecurityContextRepository;

    @Autowired
    private Oauth2Client oauth2Client;

    @Autowired
    private Oauth2Config oauth2Config;


    public FormSecurityConfig(Oauth2ServerProps oauth2ServerProps, RegisteredClientRepository registeredClientRepository, MemorySecurityContextRepository memorySecurityContextRepository) {
        this.oauth2ServerProps = oauth2ServerProps;
        this.registeredClientRepository = registeredClientRepository;
        this.memorySecurityContextRepository = memorySecurityContextRepository;
    }


    @Bean
    public CorsFilter corsFilter() {
        log.debug("FormSecurityConfig CorsFilter init");
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        // 允许cookies跨域
        corsConfiguration.setAllowCredentials(true);
        // #允许向该服务器提交请求的URI，*表示全部允许，自定义可以添加多个
        //匹配规则是精确匹配
        corsConfiguration.addAllowedOrigin("kcde");
        //基于模式匹配
        corsConfiguration.addAllowedOriginPattern("*");
        // #允许访问的头信息,*表示全部，可以添加多个
        corsConfiguration.addAllowedHeader("*");
        // 预检请求的缓存时间（秒），即在这个时间段里，对于相同的跨域请求不会再预检了
        corsConfiguration.setMaxAge(3600L);
        // 允许提交请求的方法，*表示全部允许，一般OPTIONS,GET,POST三个够了
        corsConfiguration.addAllowedMethod("*");
        source.registerCorsConfiguration("/**", corsConfiguration);
//        CorsFilter corsFilter = new CorsFilter(source);
//        corsFilter.setCorsProcessor(new MyCustomCorsProcessor());
//        return corsFilter;
        return new CorsFilter(source);
    }

    /**
     * 配置form认证（自定义登录、登出等）
     */
    @Bean
    public SecurityFilterChain formSecurityFilterChain(HttpSecurity http, UniLoginUserDetailsService uniLoginUserDetailsService) throws Exception {
        // 指定认证信息存储在内存中.后续也从内存中读取认证信息.key为nonceId
        http.
                securityContext(context -> context.securityContextRepository(memorySecurityContextRepository));
//        http.cors(AbstractHttpConfigurer::disable);
//        http.addFilter(corsFilter());
        http
                //认证配置
                .authorizeHttpRequests(authorize ->
                                authorize
                                        .requestMatchers(this.oauth2ServerProps.getFromRequestWhiteList())
                                        .permitAll()
//                        .requestMatchers(this.oauth2ServerProps.getLoginPageUrl(), this.oauth2ServerProps.getLogoutRedirectDefaultUrl()).permitAll()
//                        .requestMatchers(this.oauth2ServerProps.getStaticResourceWhiteList()).permitAll()
                                        .anyRequest().authenticated()
                )
                //忽略白名单接口的CSRF验证
                .csrf(csrf -> csrf.ignoringRequestMatchers(this.oauth2ServerProps.getStaticResourceWhiteList()))
                //form表单登录配置
                .formLogin(form ->

                                form
                                        .loginPage(this.oauth2ServerProps.getLoginPageUrl())
                                        .loginProcessingUrl(this.oauth2ServerProps.getLoginProcessingUrl())
//                        .successHandler(authenticationSuccessHandler())
//                        .failureHandler(authenticationFailureHandler())
                )
                //发现未登录时。重定向登录地址
                .exceptionHandling((exceptions) -> exceptions
                        .defaultAuthenticationEntryPointFor(
                                //              new LoginTargetAuthenticationEntryPoint(this.oauth2ServerProps.getLoginPageUrl()),
                                new LoginTargetAuthenticationEntryPoint(this.oauth2ServerProps.getLoginPageUrl(),this.oauth2ServerProps.getAuthServerUrl(),this.oauth2ServerProps.getNgServerUrl()),
                                new MediaTypeRequestMatcher(MediaType.TEXT_HTML)
                        ))
                //登出配置
                .logout(logout -> logout
                        .logoutSuccessHandler(new CustomLogoutSuccessHandler(this.oauth2ServerProps.getLoginPageUrl(),oauth2Client,oauth2Config))
                        //登出请求URL  如果启用了CSRF保护（默认），则请求也必须是POST
                        .logoutUrl(this.oauth2ServerProps.getLogoutPageUrl())
                        //.logoutSuccessUrl(this.oauth2ServerProps.getLoginPageUrl()) //登出成功后跳转的页面
//                        .deleteCookies("sso-token")
                )
                //.sessionManagement(session -> session
                //        .sessionFixation(fixation -> fixation
                //                //.changeSessionId()
                //                .none()
                //        )
                //        //.sessionAuthenticationStrategy()
                //)
                //.rememberMe(rememberMe -> rememberMe
                //        .alwaysRemember(false)
                //        //登录表单对应的记住我参数名（默认remember-me，对应值true|yes|on|1）
                //        .rememberMeParameter("remember-me")
                //        //记住我的浏览器端cookie名（默认remember-me）
                //        .rememberMeCookieName("remember-me")
                //        //记住我token失效时间（默认2周）
                //        .tokenValiditySeconds(1209600)
                //
                //        //.rememberMeServices(new TokenBasedRememberMeServices(key, userDetailServices))
                //        //设置使用DB进行持久化记住我Token（若不设置则默认使用TokenBased（即在浏览器端记录hash cookie））
                //        //.tokenRepository(new JdbcTokenRepositoryImpl())
                //
                //)
                //通用登录模型配置
                .with(new UniLoginAuthenticationSecurityConfig(this.oauth2ServerProps, uniLoginUserDetailsService, memorySecurityContextRepository), withDefaults())
        ;

        return http.build();
    }

    /**
     * 添加view映射（登录页面、登出页面、默认登出重定向页面）
     *
     * @param registry view注册中心
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {

        //是否自动配置登录页面：登录页面URL -> 登录页面View
        if (this.oauth2ServerProps.getAutoConfigLoginPage()) {
            registry.addViewController(this.oauth2ServerProps.getLoginPageUrl())
                    .setViewName(this.oauth2ServerProps.getLoginPageView());
        }
        //登出页面URL -> 登出页面View
        registry.addViewController(this.oauth2ServerProps.getLogoutPageUrl()).setViewName(this.oauth2ServerProps.getLogoutPageView());
        //登出后默认跳转页面URL -> 登出后默认跳转页面页面View
        registry.addViewController(this.oauth2ServerProps.getLogoutRedirectDefaultUrl()).setViewName(this.oauth2ServerProps.getLogoutRedirectDefaultView());
    }


//    /**
//     * 默认定义root/123456用户
//     */
//    @Bean
//    @ConditionalOnMissingBean
//    UserDetailsService users() {
//        UserDetails user = User.withUsername("root")
//                .password("{bcrypt}$2a$10$iVpQQcCZeA.9iRBEtfjX0.TTMeVJdasYZ.4eh.DFv2oqzWFdoOi0y")
//                .authorities("all")
//                .build();
//        return new InMemoryUserDetailsManager(user);
//    }

    /**
     * 定义统一用户查询及验证服务
     *
     * @param userDetailsService
     * @param userService
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    UniLoginUserDetailsService uniLoginUserDetailsService(UserDetailsService userDetailsService, UserService userService) {
        return new UniLoginUserDetailsPasswordMatcherService(userDetailsService, userService);
    }

    private AuthenticationSuccessHandler authenticationSuccessHandler() {
        return new UniLoginRespJsonAuthenticationSuccessHandler();
    }

    private AuthenticationFailureHandler authenticationFailureHandler() {
        return new UniLoginRespJsonAuthenticationFailureHandler();
    }

}
