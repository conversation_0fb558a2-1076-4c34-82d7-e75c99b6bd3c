package com.ksyun.auth.server.constant;

public class SecurityConstants {
    /**
     * 临时id
     */
    public static final String NONCE_HEADER_NAME = "nonceId";
    /**
     * 登录方式
     */
    public static final String LOGIN_TYPE_NAME = "loginType";
    /**
     * 登录方式——账号密码登录
     */
    public static final String PASSWORD_LOGIN_TYPE = "passwordLogin";
    /**
     * 登录方式——小米CAS登录
     */
    public static final String XIAO_MI_CAS_LOGIN_TYPE = "xiaomiCasLogin";
    /**
     * 认证信息存储前缀
     */
    public static final String SECURITY_CONTEXT_PREFIX_KEY = "security_context:";
    //target 存储前缀
    public static final String NONCE_TARGET_URL_PREFIX_KEY = "nonce_target_url:";
    public static final String NONCE_SERVER_LOGOUT_PREFIX_KEY = "nonce_server_logout:";
    /**
     * 默认过期时间，默认24小时
     */
    public static final long DEFAULT_TARGATURL_EXPIRATION_TIME = 60L * 60 * 24;
    public static final long DEFAULT_SERVER_LOGOUT_EXPIRATION_TIME = 60L * 60;
    public static final long DEFAULT_AUTH_EXPIRATION_TIME = 60L * 60 ;

}
