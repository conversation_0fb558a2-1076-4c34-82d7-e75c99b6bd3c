package com.ksyun.auth.server.controller;

import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.vo.RoleVo;
import com.ksyun.auth.vo.UserVo;
import com.ksyun.common.constant.Response;
import com.ksyun.common.enums.SystemTypeEnum;
import com.ksyun.common.exception.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/permit")
@RequiredArgsConstructor
public class PermitController {
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @GetMapping("/{roleCode}/users")
    public Response getUsersByRoleCode (@PathVariable("roleCode") String roleCode) {
        Assert.notNull(roleService.getRoleByCode(roleCode, SystemTypeEnum.BIGDATA.getCode()), "未找到角色code");
        RoleVo role = roleService.getRoleByCode(roleCode, SystemTypeEnum.BIGDATA.getCode());
        Optional<List<UserVo>> list = userService.getUsersByRole(role.getId());
        return Response.success(list);
    }
}
