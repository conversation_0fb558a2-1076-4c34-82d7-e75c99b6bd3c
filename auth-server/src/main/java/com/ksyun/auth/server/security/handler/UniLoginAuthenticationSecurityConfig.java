package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.server.config.Oauth2ServerProps;
import com.ksyun.auth.server.support.MemorySecurityContextRepository;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * 通用登录 - Security配置适配器
 * 支持自定义 UniLoginUserDetailsService 配置
 * 支持 Oauth2ServerProps 配置
 *
 * <AUTHOR>
 * @since v1.8.0-2024/4/22
 */
public class UniLoginAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilter<PERSON>hain, HttpSecurity> {

    private final UniLoginUserDetailsService uniLoginUserDetailsService;
    private final Oauth2ServerProps oauth2ServerProps;
    private final MemorySecurityContextRepository memorySecurityContextRepository;

    public UniLoginAuthenticationSecurityConfig(Oauth2ServerProps oauth2ServerProps, UniLoginUserDetailsService uniLoginUserDetailsService,MemorySecurityContextRepository memorySecurityContextRepository) {
        this.uniLoginUserDetailsService = uniLoginUserDetailsService;
        this.oauth2ServerProps = oauth2ServerProps;
        this.memorySecurityContextRepository = memorySecurityContextRepository;
    }

    @Override
    public void configure(HttpSecurity http) {
        //认证过滤器
        UniLoginAuthenticationProcessingFilter uniLoginAuthenticationProcessingFilter = new UniLoginAuthenticationProcessingFilter(
                this.oauth2ServerProps.getLoginProcessingUrl(),//只有通过这个url的请求,才会走这个过滤器
                http.getSharedObject(AuthenticationManager.class),
                memorySecurityContextRepository
        );

        //自定义登录成功、失败处理器（支持Ajax Json响应结果）
        uniLoginAuthenticationProcessingFilter.setAuthenticationSuccessHandler(new UniLoginRespJsonAuthenticationSuccessHandler());
        uniLoginAuthenticationProcessingFilter.setAuthenticationFailureHandler(new UniLoginRespJsonAuthenticationFailureHandler());

        //认证处理器
        UniLoginAuthenticationProvider uniLoginAuthenticationProvider = new UniLoginAuthenticationProvider(this.uniLoginUserDetailsService);

        //security配置
        http.authenticationProvider(uniLoginAuthenticationProvider)
                .addFilterBefore(uniLoginAuthenticationProcessingFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
