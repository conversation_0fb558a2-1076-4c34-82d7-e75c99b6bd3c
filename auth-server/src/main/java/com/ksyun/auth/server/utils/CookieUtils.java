package com.ksyun.auth.server.utils;

import com.alibaba.fastjson.JSON;
import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.server.constant.SecurityConstants;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.utils.CodeUtils;
import com.ksyun.common.utils.DomainUtils;
import com.ksyun.common.utils.IpCheckUtils;
import com.ksyun.common.utils.Md5Utils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;

@Slf4j
public class CookieUtils {
    public static void setNonceIdCookie(HttpServletRequest request, HttpServletResponse response, String type, String nonce) {
        String cookieName = SecurityConstants.NONCE_HEADER_NAME;
        String domain = DomainUtils.getDomain(request, PropConfig.AUTH_SERVER_DOMAIN, type);
        response.addCookie(CommonUtils.newJavaCookie(cookieName, nonce, domain, -1));
        log.info("setNonceIdCookie：cookie={}，nonceId={}，domain={}", cookieName, nonce, domain);
    }

    public static void setSsoTokenCookie(HttpServletRequest request, HttpServletResponse response, AuthUser user, String type, String token) {
        log.info("start setSsoTokenCookie....");
        if (StringUtils.isEmpty(token)) {
            token = CodeUtils.generateToken(user.getId(), user.getName());
            log.debug("token is null . create new token = [{}]", token);
        }
        CacheUtil.set(Constants.AUTH_LOGIN_USER_PREFIX.concat(token), JSON.toJSONString(user), PropConfig.TOKEN_EXPIRE_SECONDS);
        // 设置客户端Cookie，保存令牌ID
        String cookieName = Constants.SSO_TOKEN_COOKIE_NAME;
        String domain = DomainUtils.getDomain(request, PropConfig.AUTH_SERVER_DOMAIN, type);
        response.addCookie(CommonUtils.newJavaCookie(cookieName, token, domain, PropConfig.AUTH_SERVER_CACHE_EXPIRE));
        log.info("setSsoTokenCookie：cookie={}，token={}，domain={}", cookieName, token, domain);
        try {
            Set<String> prefixKeyValue = null;//CacheUtil.getPrefixKeyValue(Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(Md5Utils.getLoginCacheByIdAndName(user.getId(), user.getName())).concat("-"));
            if (prefixKeyValue != null) {
                for (String key : prefixKeyValue) {
                    // 强制清除当前已登陆的所有用户
                    CacheUtil.del(key);
                }
            }
            // 保存客户端cookie，IP和UA，用户越权访问校验
            String ipAddress = IpCheckUtils.getClientIpAddr(request);
            log.info("setSsoTokenCookie: 缓存越权数据 key={}, value={}", Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(token), JSON.toJSONString(Arrays.asList(ipAddress, "")));
            CacheUtil.set(Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(token), Md5Utils.encodeByMD5(JSON.toJSONString(Arrays.asList(ipAddress, ""))), PropConfig.TOKEN_EXPIRE_SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存越权访问令牌失败", e);
        }
    }
}
