package com.ksyun.auth.server.controller;

import com.google.common.collect.Lists;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.server.utils.FreemarkerUtil;
import com.ksyun.common.constant.Response;
import freemarker.template.Template;
import jodd.http.HttpRequest;
import jodd.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 服务启动健康类
 * <AUTHOR>
 * @Date 2019/9/2 11:11
 * @Description
 * @Version V1
 **/
@RestController
@Slf4j
public class HealthController {

    Map<Integer,String> tableParams = new HashMap<>();

    /**
     * 获取当前版本
     */
    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/ping")
    public Response ping() {
        log.info("authServer ping={}", "ping");
        return Response.success("ping");
    }


    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/testCreateDraftTable")
    public String testCreateDraftTable(int index) throws Exception{
        log.info("testCreateDraftTable={}", index);
        String res = createDraftTable(index);
        return res;
    }

    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/getCreateDraftTableParams")
    public Response getCreateDraftTableParams(int index) throws Exception{
        log.info("getCreateDraftTableParams={}", index);
        String res = tableParams.get(index);
        return Response.success(res);
    }

    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/createDraftTableParams")
    public Response testCreateDraftTable(Integer min,Integer max) throws Exception{
        log.info("生成表参数，min:{},max:{}", min,max);
        if(min == null || max == null){
            return Response.failure("min和max不能为空");
        }
        tableParams.clear();
        createTableParams(min,max);
        return Response.success();
    }

    @AccessBy(AccessBy.Method.NULL)
    @GetMapping(value = "/grantProject")
    public Response grantProject(Integer min,Integer max) throws Exception{
        granTblToProject();
        return Response.success();
    }



    public static void granTblToProject() throws Exception{
        //String url = "http://ec.console.kscbigdata.cloud/i/right-mg/datamanage/right/grant/1";
        String url = "http://10.69.65.181:9091/datamanage/right/grant/project/batch";
        String cookie = "sso-token=AA2200FF222255440099DDEEEEBB2299-4b30ddeb2625446ab5a5321b3ff49c90; JSESSIONID=ACED9EC85E737EF7F56EF244B8138867; sign=041768932fb9ac0259699ea3751389cd6ba69e7c24454f02b66e7b3af1e62ec81a1f79e3158f55ea87a604a6c71fbf607e36ae88202a369c0c4c0b71c609c2db295bd4ec2d0a4b34185dc724efd0b6908ab7b0e6172fc61a862cefac0c0cc253f2e21dd13d7bfbb3daca26be4f18b205862e3db1ecc4eaace9589e78cc3b9dc1127f0761cf9d51164f4cf84aae6a98acfc5e078575040faed96863fadfef0b18027518f8222d6aecbd0a91df384efd167ca88d879b959afbfad6c616a02ae3b4cf9400549b5610";
        //String table = "dmg_test20_log8156,dmg_test20_log7463,dmg_test20_log6374,dmg_test20_log7625,dmg_test20_log6139,dmg_test20_log7504,dmg_test20_log7988,dmg_test20_log7746,dmg_test20_log8277,dmg_test20_log8035,dmg_test20_log7867,dmg_test20_log7108,dmg_test20_log6536,dmg_test20_log6932,dmg_test20_log5049,dmg_test20_log5325,dmg_test20_log8157,dmg_test20_log6778,dmg_test20_log5567,dmg_test20_log7464,dmg_test20_log5842,dmg_test20_log5204,dmg_test20_log6657,dmg_test20_log5446,dmg_test20_log5688,dmg_test20_log7067,dmg_test20_log6253,dmg_test20_log6415,dmg_test20_log5963,dmg_test20_log7229,dmg_test20_log6899,dmg_test20_log5169,dmg_test20_log6375,dmg_test20_log6018,dmg_test20_log5721,dmg_test20_log7900,dmg_test20_log6138,dmg_test20_log7626,dmg_test20_log6933,dmg_test20_log7505,dmg_test20_log5324,dmg_test20_log8278,dmg_test20_log7868,dmg_test20_log7747,dmg_test20_log5048,dmg_test20_log6537,dmg_test20_log8036,dmg_test20_log7109,dmg_test20_log8158,dmg_test20_log5841,dmg_test20_log7465,dmg_test20_log7068,dmg_test20_log5566,dmg_test20_log5600,dmg_test20_log5445,dmg_test20_log6779,dmg_test20_log6254,dmg_test20_log6376,dmg_test20_log5168,dmg_test20_log6658,dmg_test20_log5962,dmg_test20_log5203,dmg_test20_log6017,dmg_test20_log6811,dmg_test20_log7221,dmg_test20_log5720,dmg_test20_log6416,dmg_test20_log6934,dmg_test20_log6137,dmg_test20_log8279,dmg_test20_log7506,dmg_test20_log7627,dmg_test20_log5323,dmg_test20_log7869,dmg_test20_log5047,dmg_test20_log7989,dmg_test20_log7748,dmg_test20_log8037,dmg_test20_log6538,dmg_test20_log7188,dmg_test20_log8159,dmg_test20_log5565,dmg_test20_log5840,dmg_test20_log7466,dmg_test20_log7222,dmg_test20_log6659,dmg_test20_log6377,dmg_test20_log5961,dmg_test20_log5167,dmg_test20_log5687,dmg_test20_log7069,dmg_test20_log5444,dmg_test20_log6016,dmg_test20_log6417,dmg_test20_log5729,dmg_test20_log6255,dmg_test20_log6812,dmg_test20_log7628,dmg_test20_log5202,dmg_test20_log6770";
        Template delTblNameTemplate = FreemarkerUtil.getTemplate("dmgTemplate/grantTableName.ftl");
        String table = FreeMarkerTemplateUtils.processTemplateIntoString(delTblNameTemplate, new HashMap<>());

        String[] tables = table.split(",");
        List<String> tableNames = Arrays.asList(tables);
        List<String> projectIds = Lists.newArrayList();
        //projectIds.add("7");
        for(int i=12;i<213;i++){
            projectIds.add(i+"");
        }
        Template delTblTemplate = FreemarkerUtil.getTemplate("dmgTemplate/grantTblToProject.ftl");
        Map<String,Object> map = new HashMap<>();
        map.put("tableNames",tableNames);
        map.put("projectIds",projectIds);
        String params = FreeMarkerTemplateUtils.processTemplateIntoString(delTblTemplate, map);
        log.info(params);
        log.info("批量授权开始：{}",new Date());
        HttpResponse response = HttpRequest.post(url).header("Content-Type","application/json;charset=UTF-8").header("Cookie",cookie).body(params).timeout(24 * 60 * 60 * 1000).send();
        log.info(response.bodyText());
        log.info("批量授权结束：{}",new Date());
    }

    public static void main(String[] args) throws Exception {
//        granTblToProject();
        createTableParams(0,201);
    }

    public String createDraftTable(int index) throws Exception {
        String url = "http://ec.console.kscbigdata.cloud/i/md-mg/techMeta/draft/1";
        String cookie = "sso-token=AA2200FF222255440099DDEEEEBB2299-3c37185f9eb04241bd8a2592b26436bc; JSESSIONID=619823747D1806FADCE8684AE0E090EF; sign=0414c1bf1600da161556ad76cb8747d67b03f7519874202318f39f072731803ec07d1018550746cc192b75749f788f9dcd3b5df7ffe9b0317b98460917bf37ab0cd2cdde28d417167afea8776e11a15767ae7a2470c7fc9830831cd2c2c86d6b03450e88d4d30bdce2ceac55f7289603fdedbd6fee1e89ccc0ad24d70c4780b8f1a944cc7a191958511c5af3b6e20303284f55387fca6c92446a4d02000b47a16d84e60021b1f467dbdb5a5aa5370aedfa363cdc5be6cb634b213bcadcdac88714dff5cb838831";
        String tableNameParams = tableParams.get(index);
        log.info("请求参数：" + tableNameParams);
        if(StringUtils.isEmpty(tableNameParams)){
            return "参数为空";
        }
        HttpResponse response = HttpRequest.post(url).header("Content-Type","application/json;charset=UTF-8").header("Cookie",cookie).body(tableNameParams).send();
        String res = response.bodyText();
        log.info("创建设计态返回：{}",res);
        return res;
    }


    private static void createTableParams(int min,int max) {
        try{
            for(int i=min;i<max;i++){
                String[] type = {"STRING","NUMBER","DATE","DATETIME","TIME"};
                List<Object> cols = Lists.newArrayList();
                Template colTemplate = FreemarkerUtil.getTemplate("dmgTemplate/createDraftTableCol.ftl");
                for(int j=0; j<300; j++){
                    int random = random();
                    Map<String,String> map = new HashMap<>();
                    map.put("colName","aa" + j);
                    map.put("colType",type[random]);
                    map.put("length","50");
                    String col = FreeMarkerTemplateUtils.processTemplateIntoString(colTemplate, map);
                    cols.add(col);
                }
                Template tableTemplate = FreemarkerUtil.getTemplate("dmgTemplate/createDraftTable.ftl");
                Map<String,Object> map = new HashMap<>();
                map.put("tblName","test_batch_add_20221123" + i);
                map.put("chsName","test" + i);
                map.put("tblComment","test tblComment " + i);
                map.put("cols",cols);
                String tableNameParams = FreeMarkerTemplateUtils.processTemplateIntoString(tableTemplate, map);
                log.info("请求参数：" + tableNameParams);
                //tableParams.put(i,tableNameParams);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private static int random(){
        Random r=new Random();
        int random = r.nextInt(4);
        return random;
    }

}