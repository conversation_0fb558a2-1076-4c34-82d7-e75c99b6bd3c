package com.ksyun.auth.server.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.inter.Oauth2Client;
import com.ksyun.auth.inter.config.Oauth2Config;
import com.ksyun.auth.inter.response.Oauth2LogoutResp;
import com.ksyun.auth.server.constant.SecurityConstants;
import com.ksyun.auth.server.utils.HttpRequestUtils;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.common.enums.UserSourceEnum;
import com.ksyun.common.utils.DomainUtils;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jodd.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CustomLogoutSuccessHandler extends AbstractAuthenticationTargetUrlRequestHandler
        implements LogoutSuccessHandler {


    private String logoutUrl;
    private Oauth2Client oauth2Client;

    private Oauth2Config oauth2Config;

    public CustomLogoutSuccessHandler(String logoutUrl, Oauth2Client oauth2Client, Oauth2Config oauth2Config) {
        this.logoutUrl = logoutUrl;
        this.oauth2Client = oauth2Client;
        this.oauth2Config = oauth2Config;
    }

    /**
     * 登出成功后.重定向到kcdemg登录页
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        JSONObject requestParamMap = JSONObject.parseObject(HttpRequestUtils.getRequestPostStr(request));
        if (ObjectUtils.isNotEmpty(requestParamMap) && StringUtils.isNotEmpty(requestParamMap.getString("serverLogout"))) {
            //保存从哪个服务登录的.dls登出需要跳回到dls
            String serverLogout = requestParamMap.getString("serverLogout");
            CacheUtil.set(SecurityConstants.NONCE_SERVER_LOGOUT_PREFIX_KEY, serverLogout, SecurityConstants.DEFAULT_SERVER_LOGOUT_EXPIRATION_TIME);
        }
        String tmpLogoutUrl = logoutUrl;
        String nonceId = request.getSession(true).getId();
        tmpLogoutUrl += "?"+SecurityConstants.NONCE_HEADER_NAME+"="+ nonceId;
        log.debug("logout start");
        // 1.删除sping
        clearAuthenticationAttributes(request);
        log.debug("logout clearCookie");
        //2. 删除旧认证的信息
        String token = clearCookie(request, response);
        //3.开启第三方登录后.如果是第三方账号退出.走第三方退出.
        AuthUser user = (AuthUser) authentication;
        if (ObjectUtils.isNotEmpty(user)) {
            log.debug("登出用户 user [{}]", user);
            String source = user.getSource();
            if (oauth2Config.getOnOff() && StringUtils.isNotEmpty(token) && !UserSourceEnum.LOCAL.name().equals(source)) {
                log.debug("logout 第三方登出");
                Oauth2LogoutResp oauth2LogoutResp = oauth2Client.logoutOauth2(token);
                if (StringUtils.isNotEmpty(oauth2LogoutResp.getLogOutUrl())) {
                    tmpLogoutUrl = oauth2LogoutResp.getLogOutUrl();

                }
            }
        }
        //4.返回数据
        log.info("登出地址: [{}]", tmpLogoutUrl);
        bulidResponse(request, response, tmpLogoutUrl);
    }

    public void bulidResponse(HttpServletRequest request, HttpServletResponse response, String logoutUrl) throws IOException {
        response.setStatus(HttpStatus.HTTP_OK);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        Map<String, Object> data = new HashMap<String, Object>() {{
            put("status", 200);
            put("message", "登出成功");
            put("data", logoutUrl);
        }};
        response.getWriter().write(JSON.toJSONString(data));
    }

    private String clearCookie(HttpServletRequest request, HttpServletResponse response) {
        String token = null;
        String logoutDomain = DomainUtils.getLogoutDomain(request, PropConfig.AUTH_SERVER_DOMAIN);
        Cookie[] cookies = AuthenticationHelper.getAuthenticationRelatedCookies(request);
        for (Cookie cookie : cookies) {
            if (com.ksyun.common.constant.Constants.SSO_TOKEN_COOKIE_NAME.equalsIgnoreCase(cookie.getName())) {
                CacheUtil.del(com.ksyun.common.constant.Constants.AUTH_LOGIN_USER_PREFIX + cookie.getValue());
                CacheUtil.del(com.ksyun.common.constant.Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(cookie.getValue()));
                deleteCookie(request, response, cookie.getName(), logoutDomain);
                token = cookie.getValue();
            }else if (SecurityConstants.NONCE_HEADER_NAME.equalsIgnoreCase(cookie.getName())){
                CacheUtil.del(SecurityConstants.SECURITY_CONTEXT_PREFIX_KEY + cookie.getValue());
                deleteCookie(request, response, cookie.getName(), logoutDomain);
        }
        }
        return token;
    }
    public void  deleteCookie(HttpServletRequest request, HttpServletResponse response,String cookieName, String logoutDomain) {
        Cookie cookie = new Cookie(cookieName, (String)null);
        String cookiePath = "/";
        cookie.setPath(cookiePath);
        cookie.setMaxAge(0);
        cookie.setDomain(logoutDomain);
        response.addCookie(cookie);
}

    protected final void clearAuthenticationAttributes(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
        }
    }
}
