package com.ksyun.auth.server.adapter;

import com.alibaba.fastjson.JSON;
import com.ksyun.auth.client.adapter.CookieAuthAdapter;
import com.ksyun.auth.client.adapter.CookieAuthServiceLoader;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.utils.PropConfig;
import com.ksyun.common.constant.Constants;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.Cookie;
import java.util.Map;
import java.util.Optional;


/**
 * @description: 租户控制台cookie适配器
 * @author: wuzhenliang
 * @date: 2020-05-13
 */
@Slf4j
@Component
public class BigDataCookieAuthAdapter extends CookieAuthServiceLoader implements CookieAuthAdapter {

    public BigDataCookieAuthAdapter() {
        log.debug("BigDataCookieAuthAdapter Init ...");
        super.add(this);
    }

    /**
     * @param authCookie
     * @return java.util.Optional<com.ksyun.common.domain.authentication.Authentication>
     * @description cookie解析
     */
    @Override
    public Optional<Authentication> resolve(Map<String, Cookie> authCookie) {
        if (authCookie.containsKey(Constants.SSO_TOKEN_COOKIE_NAME)) {
            Cookie cookie = authCookie.get(Constants.SSO_TOKEN_COOKIE_NAME);
            log.debug("resolve cookie, sso-token={}", cookie.getValue());
            return getAuthenticationByCookieValue(cookie);
        }
        return Optional.empty();
    }

    private Optional<Authentication> getAuthenticationByCookieValue(Cookie ssoCookie) {
        String token = ssoCookie.getValue();
        if (StringUtil.isNotEmpty(token)) {
            String user = (String) CacheUtil.get(Constants.AUTH_LOGIN_USER_PREFIX.concat(token));
            if (StringUtils.isNotEmpty(user)) {
                int tokenExpireSeconds = PropConfig.TOKEN_EXPIRE_SECONDS;
                CacheUtil.expire(Constants.AUTH_LOGIN_USER_PREFIX.concat(token), tokenExpireSeconds);
                CacheUtil.expire(Constants.BROKEN_ACCESS_CONTROL_PREFIX.concat(token), tokenExpireSeconds);
                log.debug("authentication expire token ={},expire={}", token, tokenExpireSeconds);
            }
            log.debug("authentication get token ={}", user);
            return Optional.ofNullable(JSON.parseObject(user, AuthUser.class));
        }
        return Optional.empty();
    }
}
