package com.ksyun.auth.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @title 线程池工具类
 * @date 2020/12/09
 */
@Configuration
@EnableAsync
public class ExecutorConfig {

    public ExecutorConfig() {
        System.out.println(Thread.currentThread().getName() + "构造器");
    }

    // 核数
    private static final int available = Runtime.getRuntime().availableProcessors();
    // 核心线程数
    private static final int corePoolSize = available * 2;
    // 队列大小
    private static final int queueCapacity = corePoolSize * 6;
    // 最大线程数
    private static final int maxPoolSize = queueCapacity * 6;
    // 创建线程池
    private static volatile ThreadPoolTaskExecutor executor = null;

    @Bean(name = "asyncServiceExecutor")
    public static ThreadPoolTaskExecutor asyncServiceExecutorInstance() {
        if (null == executor) {
            synchronized (ThreadPoolTaskExecutor.class) {
                if (null == executor) {
                    executor = new ThreadPoolTaskExecutor();
                    // 配置核心线程数
                    executor.setCorePoolSize(corePoolSize);
                    // 配置最大线程数
                    executor.setMaxPoolSize(maxPoolSize);
                    // 配置队列大小
                    executor.setQueueCapacity(queueCapacity);
                    // 配置线程池中的线程的名称前缀
                    executor.setThreadNamePrefix("AsyncThreadPool-");
                    // rejection-policy：当pool已经达到max size的时候，如何处理新任务
                    // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
                    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                    //线程空闲后的最大存活时间 60秒
                    executor.setKeepAliveSeconds(60);
                    // 执行初始化
                    executor.initialize();
                    return executor;
                }
            }
        }
        return executor;
    }
}
