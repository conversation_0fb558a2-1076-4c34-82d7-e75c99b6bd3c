package com.ksyun.auth.server.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.adapter.CookieAuthServiceLoader;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.interceptor.CookieProcessor;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static java.util.Arrays.stream;

/**
 * 认证中心 Cookie 拦截器，与 AuthClient 中的逻辑不同，
 * Server 端才实现Cookie的真正解析，AuthClient 只是将 Cookie 转发给 Server 解析
 */
@Slf4j
public class AuthServerCookieProcessor extends CookieProcessor {
    private static final AtomicReference<String> SWT_SALT_REF = new AtomicReference<>();
    private static final int ORDER = 1;
    private CookieAuthServiceLoader cookieAuthServiceLoader;

    private List<String> cookieNames;

    public AuthServerCookieProcessor(String salt, List<String> cookieNames, CookieAuthServiceLoader cookieAuthServiceLoader) {
        this.cookieNames = cookieNames;
        this.cookieAuthServiceLoader = cookieAuthServiceLoader;
        if (Objects.isNull(cookieNames) || cookieNames.size() <= 0) {
            throw new RuntimeException("统一登陆认证服务未配置cookie key列表,服务未能正常启动!");
        }
        SWT_SALT_REF.compareAndSet(null, salt);
        log.info("Initialized AuthServerCookieProcessor: salt={} cookieNames={}", salt, cookieNames);
    }

    @Override
    public Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        Cookie[] cookies = request.getCookies();
        if (Objects.nonNull(cookies)) {
            Optional<Authentication> authentication = authCookie(cookies);
            log.info("AuthServerCookieProcessor process Cookie Authentication Result={}", authentication);
            return authentication;
        }
        log.info("AuthServerCookieProcessor process Cookie Is Null");
        return Optional.empty();
    }

    public Optional<Authentication> authCookie(Cookie[] cookies) {
        log.info("authCookie cookieNames={}", cookieNames);
        // 1.根据配置文件配置的cookie key列表进行cookie过滤
        HashMap<String, Cookie> authCookie = stream(cookies).filter(cookie -> cookieNames.contains(cookie.getName())).collect(Collectors.toMap(Cookie::getName, cookie -> cookie, (x, y) -> x, HashMap::new));
        if (log.isInfoEnabled()) {
            log.info("authCookie: valid cookies={}", JSONObject.toJSONString(authCookie));
        }
        // 3.cookie转发到对应的cookie适配器进行解析、封装返回authUser
        return cookieAuthServiceLoader.resolveAll(authCookie);
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

}
