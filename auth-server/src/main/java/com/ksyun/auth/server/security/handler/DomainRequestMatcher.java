package com.ksyun.auth.server.security.handler;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.web.util.matcher.RequestMatcher;

@Slf4j
public class DomainRequestMatcher implements RequestMatcher {

    private String targetUrl;

    public DomainRequestMatcher(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    @Override
    public boolean matches(HttpServletRequest request) {
        log.info("current request is {}", request.getRequestURL().toString());
        // 自定义匹配逻辑，比如基于请求路径或请求参数进行匹配
        return request.getRequestURL().toString().contains("/oauth2/authorize");
    }
}