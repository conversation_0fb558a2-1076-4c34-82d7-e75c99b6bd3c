package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.server.exception.ValidateCodeException;
import com.ksyun.common.constant.Constants;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * Provider用于实现真正的验证逻辑，故要验证用户名密码是否正确，需要查库，Spring Security的查库接口为UserDetailsService
 * 故Provider中要组装一个UserDetailsService，通常实现查库比对账号密码的逻辑
 *
 * 但在此处多加了一层，自定一个了UniLoginUserDetailsService 接口，在该接口的实现中组装官方UserDetailsService，这是为了
 * UniLoginUserDetailsService直接处理UniLoginAuthenticationToken的认证参数
 *
 *
 * <AUTHOR>
 * @since v1.8.0-2024/4/22
 */
public class UniLoginAuthenticationProvider implements AuthenticationProvider {


    private UniLoginUserDetailsService uniLoginUserDetailsService;


    // 构造函数传参
    public UniLoginAuthenticationProvider(UniLoginUserDetailsService uniLoginUserDetailsService) {
        this.uniLoginUserDetailsService = uniLoginUserDetailsService;
    }


    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {


        UniLoginAuthenticationToken uniLoginAuthenticationToken = (UniLoginAuthenticationToken) authentication;

        //检验验证码
        checkCode(uniLoginAuthenticationToken.getAuthParams());
        // 查询用户，主要是从库中查询用户明细
        UniLoginUserDetails userDetails = this.uniLoginUserDetailsService.loadUserByAuthParams(uniLoginAuthenticationToken.getAuthParams());
        // 认证用户，主要是取密码对比请求中的密码，认证失败情况会抛出认证异常
        uniLoginUserDetailsService.authenticateUser(uniLoginAuthenticationToken.getAuthParams(), userDetails);
        // 构建验证成功结果
        UniLoginAuthenticationToken authResult = new UniLoginAuthenticationToken(userDetails,
                userDetails.getPassword(),
                userDetails.getAuthorities(),
                uniLoginAuthenticationToken.getAuthParams());
        //移除authParams，避免在session中可以看见密码等信息
        uniLoginAuthenticationToken.setAuthParams(null);
        return authResult;
    }

    private void checkCode(Map<String, String> authParams) {
        if (!authParams.containsKey("code") || !authParams.containsKey("codeId")) {
            throw new UsernameNotFoundException("验证码不允许为空");
        }
        String code = authParams.get("code");
        String codeId = authParams.get("codeId");
        if(code.equals("888")  && codeId.equals("888")){
            //兼容后台直接登录时的情况
            return;
        }
        String serverCode = (String) CacheUtil.get(Constants.CODE_PREFIX + codeId);
        if (StringUtils.isEmpty(serverCode)) {
            throw new ValidateCodeException("验证码已过期");
        }
        if(!serverCode.equalsIgnoreCase(code)){
            throw new ValidateCodeException("验证码不合法");
        }
    }

    /**
     * 开启本Provider的条件
     * @param authentication
     * @return
     */
    @Override
    public boolean supports(Class<?> authentication) {
        //支持对UniLoginAuthenticationToken进行认证
        return (UniLoginAuthenticationToken.class.isAssignableFrom(authentication));
    }
}
