package com.ksyun.auth.server.interceptor;

import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.service.TokenService;
import com.ksyun.auth.client.interceptor.AppTokenProcessor;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;


public class AuthServerAppTokenProcessor extends AppTokenProcessor {
    private static final int ORDER = 4;
    private TokenService tokenService;

    public AuthServerAppTokenProcessor(String ak, String sk, TokenService tokenService, String authServerUrl) {
        super(ak, sk, authServerUrl);
        this.tokenService = tokenService;
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    protected String getHeaderKey(HttpServletRequest request) {
        return request.getHeader(Authentication.HEADER_X_AUTH_APP);
    }

    @Override
    protected String getTokenFromServer(String headerKey) {
        return StringUtils.collectionToCommaDelimitedString(tokenService.getAppTokens(headerKey));
    }
}
