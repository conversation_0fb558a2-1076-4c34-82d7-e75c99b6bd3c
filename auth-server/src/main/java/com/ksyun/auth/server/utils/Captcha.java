package com.ksyun.auth.server.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.CubicCurve2D;
import java.awt.geom.QuadCurve2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2019/12/24 11:38
 */
@Slf4j
public final class Captcha {
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final int[][] COLOR = new int[][]{{0, 135, 255}, {51, 153, 51}, {255, 102, 102}, {255, 153, 0}, {153, 102, 0}, {153, 102, 153}, {51, 153, 153}, {102, 102, 255}, {0, 102, 204}, {204, 51, 51}, {0, 153, 204}, {0, 51, 102}};

    public static String toBase64(char[] strs, int width, int height, Font font) {
        try {
            BufferedImage bi = new BufferedImage(width, height, 1);
            Graphics2D g2d = (Graphics2D)bi.getGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            drawOval(2, width, height, color(), g2d);
            g2d.setStroke(new BasicStroke(2.0F, 0, 2));
            drawBesselLine(1, width, height, color(), g2d);
            g2d.setFont(font);
            FontMetrics fontMetrics = g2d.getFontMetrics();
            int fW = width / strs.length;
            int fSp = (fW - (int)fontMetrics.getStringBounds("W", g2d).getWidth()) / 2;

            for(int i = 0; i < strs.length; ++i) {
                g2d.setColor(color());
                int fY = height - (height - (int)fontMetrics.getStringBounds(String.valueOf(strs[i]), g2d).getHeight() >> 1);
                g2d.drawString(String.valueOf(strs[i]), i * fW + fSp + 3, fY - 3);
            }

            g2d.dispose();

            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                ImageIO.write(bi, "png", out);
                out.flush();
                return Base64.getEncoder().encodeToString(out.toByteArray());
            }
        } catch (Exception e) {
            log.error("验证码图片生成失败：", e);
            throw new RuntimeException("验证码生成失败：" + e.getMessage());
        }
    }

    private static void drawOval(int num, int width, int height, Color color, Graphics2D g) {
        for(int i = 0; i < num; ++i) {
            g.setColor(color);
            int w = 5 + num(10);
            g.drawOval(num(width - 25), num(height - 15), w, w);
        }
    }

    private static void drawBesselLine(int num, int width, int height, Color color, Graphics2D g) {
        for(int i = 0; i < num; ++i) {
            g.setColor(color);
            int x1 = 5;
            int y1 = num(5, height / 2);
            int x2 = width - 5;
            int y2 = num(height / 2, height - 5);
            int ctrlx = num(width / 4, width / 4 * 3);
            int ctrly = num(5, height - 5);
            int ctrlx1;
            if (num(2) == 0) {
                ctrlx1 = y1;
                y1 = y2;
                y2 = ctrlx1;
            }

            if (num(2) == 0) {
                QuadCurve2D shape = new QuadCurve2D.Double();
                shape.setCurve(x1, y1, ctrlx, ctrly, x2, y2);
                g.draw(shape);
            } else {
                ctrlx1 = num(width / 4, width / 4 * 3);
                int ctrly1 = num(5, height - 5);
                CubicCurve2D shape = new CubicCurve2D.Double(x1, y1, ctrlx, ctrly, ctrlx1, ctrly1, x2, y2);
                g.draw(shape);
            }
        }

    }

    private static int num(int num) {
        return RANDOM.nextInt(num);
    }

    private static int num(int min, int max) {
        return min + RANDOM.nextInt(max - min);
    }

    private static Color color() {
        int[] color = COLOR[num(COLOR.length)];
        return new Color(color[0], color[1], color[2]);
    }

    public static final void main(String[] args) {
        Captcha.toBase64("sw24".toCharArray(), 130, 48, new Font("Verdana", Font.PLAIN, 32));

    }
}
