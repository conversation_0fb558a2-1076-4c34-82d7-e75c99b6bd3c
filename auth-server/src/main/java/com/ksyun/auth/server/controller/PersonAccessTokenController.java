package com.ksyun.auth.server.controller;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.server.config.Oauth2ServerProps;
import com.ksyun.auth.service.PersonAccessTokenService;
import com.ksyun.common.constant.Response;
import com.ksyun.common.dto.PersonAccessTokenDTO;
import com.ksyun.common.entity.PersonAccessToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import static com.ksyun.auth.client.authentication.Authentication.ARAN;

/**
 * 个人访问令牌控制器
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Api(tags = "个人访问令牌接口")
public class PersonAccessTokenController {

    private final PersonAccessTokenService personAccessTokenService;

    @Resource
    private Oauth2ServerProps oauth2ServerProps;

    /**
     * 创建个人访问令牌
     * 如果已存在则先删除再创建新的
     */
    @ApiOperation("创建个人访问令牌")
    @PostMapping("/createPersonAccessToken")
    public Response createToken(@RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始创建个人访问令牌, authentication={}", authentication);
        try {
            if (authentication instanceof AuthUser) {
                AuthUser authUser = (AuthUser)authentication;
                Long userId = authUser.getId();
                log.info("创建个人访问令牌, userId={}, username={}", userId, authUser.getName());
                PersonAccessToken token = personAccessTokenService.createToken(oauth2ServerProps.getIssuer(), userId, authUser.getName());
                log.info("个人访问令牌创建成功, userId={}, tokenId={}", userId, token.getId());
                return Response.success(PersonAccessTokenDTO.fromEntity(token));
            } else {
                log.info("创建个人访问令牌失败, 认证信息非法, authentication={}", authentication);
                return Response.failure().message("认证信息非法");
            }
        } catch (Exception e) {
            log.error("创建个人访问令牌异常", e);
            return Response.failure().message(e.getMessage());
        }
    }

    /**
     * 获取当前用户的访问令牌列表
     */
    @ApiOperation("获取当前用户的访问令牌列表")
    @GetMapping("/listPersonAccessToken")
    public Response listTokens(@RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始查询个人访问令牌列表, authentication={}", authentication);
        try {
            if (authentication instanceof AuthUser) {
                AuthUser authUser = (AuthUser) authentication;
                Long userId = authUser.getId();
                log.info("查询个人访问令牌列表, userId={}", userId);
                List<PersonAccessTokenDTO> tokens = personAccessTokenService.getTokens(userId)
                        .stream()
                        .map(PersonAccessTokenDTO::fromEntity)
                        .collect(Collectors.toList());
                log.info("个人访问令牌列表查询成功, userId={}, tokenCount={}", userId, tokens.size());
                return Response.success(tokens);
            } else {
                log.info("查询个人访问令牌列表失败, 认证信息非法, authentication={}", authentication);
                return Response.failure().message("认证信息非法");
            }
        } catch (Exception e) {
            log.error("查询个人访问令牌列表异常", e);
            return Response.failure().message(e.getMessage());
        }
    }

    /**
     * 根据用户ID获取访问令牌
     */
    @ApiOperation("根据用户ID获取访问令牌")
    @GetMapping("/getPersonAccessTokenById")
    public Response getTokenById(@RequestAttribute(ARAN) Authentication authentication) {
        log.info("开始查询个人访问令牌, authentication={}", authentication);
        try {
            if (authentication instanceof AuthUser) {
                AuthUser authUser = (AuthUser) authentication;
                Long userId = authUser.getId();
                log.info("查询个人访问令牌, userId={}", userId);
                PersonAccessToken token = personAccessTokenService.getTokenByUserId(userId);
                if (token != null) {
                    log.info("个人访问令牌查询成功, userId={}, tokenId={}", userId, token.getId());
                    return Response.success(PersonAccessTokenDTO.fromEntity(token));
                } else {
                    log.info("未找到个人访问令牌, userId={}", userId);
                    return Response.failure().message("未找到该用户的访问令牌");
                }
            } else {
                log.info("查询个人访问令牌失败, 认证信息非法, authentication={}", authentication);
                return Response.failure().message("认证信息非法");
            }
        } catch (Exception e) {
            log.error("查询个人访问令牌异常", e);
            return Response.failure().message(e.getMessage());
        }
    }
}
