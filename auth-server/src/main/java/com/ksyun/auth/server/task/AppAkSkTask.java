package com.ksyun.auth.server.task;

import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component("appAkSkTask")
@Slf4j
public class AppAkSkTask {

    @Autowired
    TokenService tokenService;

    @Scheduled(cron = "0 0/10 * * * *")
    public void syncGenerateOnPeriod(){
        tokenService.addDynamicKeyOnPeriod();
    }

}
