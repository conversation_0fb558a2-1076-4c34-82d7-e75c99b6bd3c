package com.ksyun.auth.server;

import com.dtflys.forest.springboot.annotation.ForestScan;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.adapter.CookieAuthServiceLoader;
import com.ksyun.auth.client.interceptor.AuthenticationInterceptor;
import com.ksyun.auth.client.interceptor.CookieProcessor;
import com.ksyun.auth.client.interceptor.OpenApiProcessor;
import com.ksyun.auth.client.interceptor.TraceIdInterceptor;
import com.ksyun.auth.server.anno.EnableOidcAuthorizationServer;
import com.ksyun.auth.server.interceptor.AuthServerAppTokenProcessor;
import com.ksyun.auth.server.interceptor.AuthServerCookieProcessor;
import com.ksyun.auth.server.interceptor.AuthServerTenantTokenProcessor;
import com.ksyun.auth.server.interceptor.ResponseTimeInterceptor;
import com.ksyun.auth.service.TokenService;
import com.ksyun.auth.utils.PropConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.Rfc6265CookieProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@EnableOidcAuthorizationServer
@EnableCaching
@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.ksyun.*")
@ComponentScan("com.ksyun.*")
@ForestScan(basePackages = "com.ksyun.auth.inter")
@Slf4j
public class AuthServerApplication implements WebMvcConfigurer {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private CookieAuthServiceLoader cookieAuthServiceLoader;

    public static void main(String[] args) {
        log.info("-----------------开始启动-------------------");
        SpringApplication.run(AuthServerApplication.class, args);
        log.info(" _  _______ _____  ______                  _   _      _____                          \n" +
                " | |/ / ____|  __ \\|  ____|      /\\        | | | |    / ____|                         \n" +
                " | ' / |    | |  | | |__ ______ /  \\  _   _| |_| |__ | (___   ___ _ ____   _____ _ __ \n" +
                " |  <| |    | |  | |  __|______/ /\\ \\| | | | __| '_ \\ \\___ \\ / _ \\ '__\\ \\ / / _ \\ '__|\n" +
                " | . \\ |____| |__| | |____    / ____ \\ |_| | |_| | | |____) |  __/ |   \\ V /  __/ |   \n" +
                " |_|\\_\\_____|_____/|______|  /_/    \\_\\__,_|\\__|_| |_|_____/ \\___|_|    \\_/ \\___|_|   \n" +
                "                                                                                      \n" +
                "                                                                                     ");
        log.info("-----------------启动成功-------------------");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        CookieProcessor cookieProcessor = new AuthServerCookieProcessor(PropConfig.COOKIE_SALT, PropConfig.AUTH_SERVER_COOKIENAMES, cookieAuthServiceLoader);
        OpenApiProcessor openApiProcessor = new OpenApiProcessor(PropConfig.APP_KEY, PropConfig.APP_SECRET, PropConfig.AUTH_SERVER_URL);
        AuthServerAppTokenProcessor headerProcessor = new AuthServerAppTokenProcessor(PropConfig.APP_KEY, PropConfig.APP_SECRET, tokenService, PropConfig.AUTH_SERVER_URL);
        AuthServerTenantTokenProcessor tenantHeaderProcessor = new AuthServerTenantTokenProcessor(tokenService);
        AuthenticationInterceptor interceptor = AuthenticationHelper.getCustomizedInterceptor(cookieProcessor, headerProcessor, tenantHeaderProcessor, openApiProcessor);
        registry.addInterceptor(interceptor).addPathPatterns("/**");

        // 同一次请求traceId相同
        registry.addInterceptor(new TraceIdInterceptor()).addPathPatterns("/**");
        //打印请求路径、请求参数已经返回的参数等等
        registry.addInterceptor(new ResponseTimeInterceptor()).addPathPatterns("/**");
    }

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
        return (factory) -> factory.addContextCustomizers((context) -> context.setCookieProcessor(new Rfc6265CookieProcessor()));
    }

}
