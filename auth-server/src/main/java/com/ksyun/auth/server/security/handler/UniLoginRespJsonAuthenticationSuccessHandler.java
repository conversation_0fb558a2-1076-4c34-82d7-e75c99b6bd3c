package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.server.utils.CookieUtils;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.server.constant.SecurityConstants;
import com.ksyun.auth.server.utils.CookieUtils;
import com.ksyun.auth.server.utils.HttpContextUtils;
import com.ksyun.auth.server.utils.JsonUtils;
import com.ksyun.auth.server.utils.NonceIdUtils;
import com.ksyun.common.constant.Response;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;

import java.io.IOException;
import java.util.Optional;

/**
 * 通用登录 - 成功处理器 - 返回成功响应结果（支持Ajax）
 *
 * <AUTHOR>
 * @date 2022-03-11
 * @see org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler
 */
@Slf4j
public class UniLoginRespJsonAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

	protected final Log logger = LogFactory.getLog(this.getClass());

	private RequestCache requestCache = new HttpSessionRequestCache();

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
										Authentication authentication) throws ServletException, IOException {
		log.debug("UniLogin authentication success - username: {}", authentication.getPrincipal());
		//清空认证异常
		clearAuthenticationAttributes(request);

		//获取之前保存的请求，即对应OAuth2的认证端点URI
		String redirectUri = Optional.ofNullable(this.requestCache.getRequest(request, response))
				.map(SavedRequest::getRedirectUrl)
				.orElse(null);

		//构建认证成功响应结果
		AuthUser authUser;
		Object principal = authentication.getPrincipal();
		if(principal instanceof UniLoginUserDetails) {
			UniLoginUserDetails userDetails =  (UniLoginUserDetails) principal;
			authUser = userDetails.getAuthUser();
		} else {
			throw new IllegalStateException("非法的用户凭证");
		}
        //种植ssotoekn cookie
		CookieUtils.setSsoTokenCookie(request, response, authUser, "1", null);
        CookieUtils.setNonceIdCookie(request, response, "1", NonceIdUtils.getNonce(request));
        //认证成功后，将认证信息放入SecurityContextHolder
        request.setAttribute(com.ksyun.auth.client.authentication.Authentication.ARAN, authUser);
        SecurityContext sc = SecurityContextHolder.getContext();
        sc.setAuthentication(authUser);

        //{
        //    "status": 200,
        //    "message": "成功",
        //    "code": 0,
        //    "result": {
        //    		"user": {
        //		        "id": 1,
        //		        "name": "admin",
        //		        "alias": "uat_test",
        //		        "source": "LOCAL",
        //		        "status": "NORMAL",
        //		        "deadline": null,
        //		        "secretLevel": 0,
        //		        "resetPwdWhenFirstLogin": false,
        //		        "createTime": "2019-08-28 19:53:19",
        //		        "updateTime": "2019-08-28 19:53:19",
        //		        "tenant": null,
        //		        "tenants": [],
        //		        "roleTags": [],
        //		        "groups": [],
        //		        "systemType": null
        //		    },
        //          "redirectUri": "http://localhost:8080/auth/login/callback";
        //    }
        //}

        String respJson = JsonUtils.toJson(Response.success(UniLoginRespJsonDto.builder()
                .user(authUser)
                .redirectUri(redirectUri)
                .build()));
        log.debug("UniLogin authentication success resp: {}", respJson);
        HttpContextUtils.responseJson(respJson, response);
    }

    public void setRequestCache(RequestCache requestCache) {
        this.requestCache = requestCache;
    }

    /**
     * Removes temporary authentication-related data which may have been stored in the
     * session during the authentication process.
     */
    protected final void clearAuthenticationAttributes(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
        }
    }


}

/**
 * 自定义认证成功处理器，适用于平台表单登录，即KCDE-manager/dls登录失败后的跳转逻辑处理
 * <p>
 * 主要职责，
 *
 * <AUTHOR>
 * @since v1.8.0-2024/4/22
 */
