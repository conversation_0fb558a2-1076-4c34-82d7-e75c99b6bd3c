package com.ksyun.auth.server.support;

import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.inter.config.Oauth2Config;
import com.ksyun.auth.server.model.security.SupplierDeferredSecurityContext;
import com.ksyun.auth.server.utils.CookieUtils;
import com.ksyun.auth.server.utils.NonceIdUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.DeferredSecurityContext;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.web.context.HttpRequestResponseHolder;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.function.Supplier;

import static com.ksyun.auth.server.constant.SecurityConstants.SECURITY_CONTEXT_PREFIX_KEY;

@Slf4j
@Component
@RequiredArgsConstructor
public class MemorySecurityContextRepository implements SecurityContextRepository {

    @Autowired
    private Oauth2Config oauth2Config;


    private final SecurityContextHolderStrategy securityContextHolderStrategy = SecurityContextHolder
            .getContextHolderStrategy();

    @Override
    public SecurityContext loadContext(HttpRequestResponseHolder requestResponseHolder) {
//           HttpServletRequest request = requestResponseHolder.getRequest();
//           return readSecurityContextFromRedis(request);
//         方法已过时，使用 loadDeferredContext 方法
        throw new UnsupportedOperationException("Method deprecated.");
    }

    /**
     * 保存认证信息到内存中
     *
     * @param context
     * @param request
     * @param response
     */
    @Override
    public void saveContext(SecurityContext context, HttpServletRequest request, HttpServletResponse response) {
        //AuthUser authUser = ((UniLoginUserDetails) context.getAuthentication().getPrincipal()).getAuthUser();
        String nonce = NonceIdUtils.getNonce(request);
        log.debug("saveContext: nonce: {}", nonce);
        if (ObjectUtils.isEmpty(nonce)) {
            return;
        }
        // 如果当前的context是空的，则移除
        SecurityContext emptyContext = this.securityContextHolderStrategy.createEmptyContext();
        if (emptyContext.equals(context)) {
            CacheUtil.del((SECURITY_CONTEXT_PREFIX_KEY + nonce));
        } else {
            // 保存认证信息
            log.debug("saveContext: nonce :[{}]  ,context: {}", nonce, context);
            CacheUtil.set((SECURITY_CONTEXT_PREFIX_KEY + nonce), context, Long.valueOf(oauth2Config.getAuthenticationExpirationTime()));
        }
    }

    @Override
    public boolean containsContext(HttpServletRequest request) {
        String nonce = NonceIdUtils.getNonce(request);
        log.debug("检查SecurityContext中是否包含数据 nonceid = [{}]", nonce);
        if (ObjectUtils.isEmpty(nonce)) {
            return false;
        }
        // 检验当前请求是否有认证信息
        boolean falg = CacheUtil.get((SECURITY_CONTEXT_PREFIX_KEY + nonce)) != null;
        // 更新缓存过期时间
        if (falg) {
            CacheUtil.expire((SECURITY_CONTEXT_PREFIX_KEY + nonce), Long.valueOf(oauth2Config.getAuthenticationExpirationTime()));
        }

        return falg;
    }

    @Override
    public DeferredSecurityContext loadDeferredContext(HttpServletRequest request) {
        Supplier<SecurityContext> supplier = () -> readSecurityContextFromRedis(request);
        return new SupplierDeferredSecurityContext(supplier, this.securityContextHolderStrategy);
    }

    /**
     * 从内存中获取认证信息
     *
     * @param request 当前请求
     * @return 认证信息
     */
    private SecurityContext readSecurityContextFromRedis(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String nonce = NonceIdUtils.getNonce(request);
        if (ObjectUtils.isEmpty(nonce)) {
            return null;
        }
        log.debug("readSecurityContextFromRedis: nonce: {}", nonce);
        // 根据缓存id获取认证信息
        SecurityContext securityContext = (SecurityContext) CacheUtil.get((SECURITY_CONTEXT_PREFIX_KEY + nonce));
        if (!ObjectUtils.isEmpty(securityContext)) {
            // 更新缓存过期时间
            CacheUtil.expire((SECURITY_CONTEXT_PREFIX_KEY + nonce), Long.valueOf(oauth2Config.getAuthenticationExpirationTime()));
        }
        return securityContext;
    }

//    /**
//     * 先从请求参数中找，找不到去请求头中找，找不到获取当前session的id,sessionid为空则生成uuid
//     *
//     * @param request 当前请求
//     * @return 随机字符串(sessionId)
//     */
//    private String getNonce(HttpServletRequest request) {
//        String nonce = request.getParameter(NONCE_HEADER_NAME);
//        if (ObjectUtils.isEmpty(nonce) || nonce.equalsIgnoreCase("undefined")) {
//            nonce = request.getHeader(NONCE_HEADER_NAME);
//            HttpSession session = request.getSession(Boolean.FALSE);
//            if (ObjectUtils.isEmpty(nonce) && session != null && !ObjectUtils.isEmpty(session.getAttribute(NONCE_HEADER_NAME))) {
//                log.debug("使用session中的值为作为nonceid");
//                nonce = (String) session.getAttribute(NONCE_HEADER_NAME);
//            } else if (ObjectUtils.isEmpty(nonce) && session != null) {
//                log.debug("使用session id 作为nonceid");
//                nonce = session.getId();
//            } else {
//                log.debug("使用新uuid 作为nonceid");
//                //nonce = request.getSession(Boolean.TRUE).getId();
//                nonce = UUID.randomUUID().toString();
//            }
//        }
//        return nonce;
//    }
}
