package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.server.constant.SecurityConstants;
import com.ksyun.auth.server.exception.NonceIDNotFoundException;
import com.ksyun.auth.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Map;

/**
 * 通用登录 - 用户查询及认证服务 - 账号、密码验证的默认实现<br/>
 * 注：兼容原Spring Security UserDetailsService实现
 *
 * <AUTHOR>
 * @since v1.8.0-2024/4/22
 */
@Slf4j
public class UniLoginUserDetailsPasswordMatcherService implements UniLoginUserDetailsService {
    /**
     * 登录表单常量定义
     */
    private static final String USERNAME_PARAMETER = "username";
    private static final String PASSWORD_PARAMETER = "password";
    private static final String SOURCE_PARAMETER = "source";

    /**
     * Spring Security UserDetailsService实现
     */
    private UserDetailsService userDetailsService;

    /**
     * 用户服务
     */
    private UserService userService;

    public UniLoginUserDetailsPasswordMatcherService(UserDetailsService userDetailsService, UserService userService) {
        this.userDetailsService = userDetailsService;
        this.userService = userService;
    }

    @Override
    public UniLoginUserDetails loadUserByAuthParams(Map<String, String> authParams) throws UsernameNotFoundException {
        //验证账号、密码相关表单参数非空
        if (!authParams.containsKey(USERNAME_PARAMETER) || !authParams.containsKey(PASSWORD_PARAMETER)) {
            throw new UsernameNotFoundException("用户名或密码为空");
        }
//        if (!authParams.containsKey(SecurityConstants.NONCE_HEADER_NAME)) {
//            throw new NonceIDNotFoundException("NonceID 不允许为空");
//        }
//        //获取用户名参数
        String username = authParams.get(USERNAME_PARAMETER);
        String password = authParams.get(PASSWORD_PARAMETER);
        return new UniLoginUserDetails(username, password, null, null);
    }


    //TODO：此处需要改为原AuthServer逻辑
    @Override
    public void authenticateUser(Map<String, String> authParams, UniLoginUserDetails uniLoginUserDetails) throws AuthenticationException {

        try {
            //
            AuthUser user = userService.getUserByLoginVo(authParams.get(USERNAME_PARAMETER),
                    authParams.get(PASSWORD_PARAMETER),
                    authParams.get(SecurityConstants.LOGIN_TYPE_NAME),
                    "",
                    authParams.get(SOURCE_PARAMETER));
            // 认证通过后将用户信息放到UserDetails中,备用
            uniLoginUserDetails.setAuthUser(user);

        } catch (Exception e) {
            // 通常是用户名密码错误异常
            log.error("用户认证失败:", e);
            throw new BadCredentialsException(e.getMessage());
        }
    }
}
