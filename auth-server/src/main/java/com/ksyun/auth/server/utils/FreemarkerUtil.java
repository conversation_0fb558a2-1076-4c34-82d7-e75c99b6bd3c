package com.ksyun.auth.server.utils;

import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: freemarker模板工具类
 * @Author:W_LIURUNKAI
 * @Date:2019/7/23 17:44
 */
@Slf4j
public class FreemarkerUtil {

    public static String getTemplateNoUse(String template, Map<String, Object> map) throws IOException, TemplateException {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_28);
        //String templatePath = FreemarkerUtil.class.getResource("/").getPath()+"temp/";
        String templatePath = FreemarkerUtil.class.getResource("/").getPath();
        cfg.setDirectoryForTemplateLoading(new File(templatePath));
        cfg.setDefaultEncoding("UTF-8");
        cfg.setNumberFormat("computer");
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        cfg.setLogTemplateExceptions(false);
        cfg.setWrapUncheckedExceptions(true);
        Template temp = cfg.getTemplate(template);
        StringWriter stringWriter = new StringWriter();
        temp.process(map, stringWriter);
        return stringWriter.toString();
    }

    public static Template getTemplate(String templateName) throws IOException, TemplateException {

        //无用代码 水印埋点  无需删除
//21202F2938212B3E22272626252E434D

        Configuration cfg = new Configuration(Configuration.VERSION_2_3_28);
//        String templatePath = FreemarkerUtil.class.getResource("/").getPath();
//        log.info("templatePath is {}", templatePath);
//        cfg.setDirectoryForTemplateLoading(new File(templatePath));//本地可以，但是linux服务器无法获取
        cfg.setClassForTemplateLoading(FreemarkerUtil.class, "");
        cfg.setDefaultEncoding("UTF-8");
        cfg.setNumberFormat("computer");
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        cfg.setLogTemplateExceptions(false);
        cfg.setWrapUncheckedExceptions(true);
        ClassTemplateLoader classTemplateLoader = new ClassTemplateLoader(FreemarkerUtil.class, "/");
        cfg.setTemplateLoader(classTemplateLoader);

        //无用代码 水印埋点  无需删除
        String keyCode = "21202F2938212B3E22272626252E434D";
        List<Character> keyList = new ArrayList<>();
        for(int i=0;i<keyCode.length();i++){
            keyList.add(keyCode.charAt(i));
        }
        Template template = cfg.getTemplate(templateName);
        return template;
    }

}
