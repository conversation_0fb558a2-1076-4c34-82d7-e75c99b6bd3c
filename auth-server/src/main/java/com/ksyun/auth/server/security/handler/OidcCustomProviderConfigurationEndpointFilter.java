package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.server.config.Oauth2ServerProps;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationResponseType;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.server.authorization.context.AuthorizationServerContext;
import org.springframework.security.oauth2.server.authorization.context.AuthorizationServerContextHolder;
import org.springframework.security.oauth2.server.authorization.oidc.OidcProviderConfiguration;
import org.springframework.security.oauth2.server.authorization.oidc.http.converter.OidcProviderConfigurationHttpMessageConverter;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.Assert;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.function.Consumer;

/**
 * Oidc发现端点 - 自定义增强实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-03-02
 * @see OidcProviderConfiguration
 * @see AuthorizationServerSettings
 * @see <a target="_blank" href="https://openid.net/specs/openid-connect-discovery-1_0.html#ProviderConfigurationRequest">4.1. OpenID Provider Configuration Request</a>
 * @see org.springframework.security.oauth2.server.authorization.oidc.web.OidcProviderConfigurationEndpointFilter
 */
public final class OidcCustomProviderConfigurationEndpointFilter extends OncePerRequestFilter {
    /**
     * The default endpoint {@code URI} for OpenID Provider Configuration requests.
     */
    private static final String DEFAULT_OIDC_PROVIDER_CONFIGURATION_ENDPOINT_URI = "/.well-known/openid-configuration";

    private final AuthorizationServerSettings providerSettings;
    private final RequestMatcher requestMatcher;
    private final OidcProviderConfigurationHttpMessageConverter providerConfigurationHttpMessageConverter = new OidcProviderConfigurationHttpMessageConverter();
    private Consumer<OidcProviderConfiguration.Builder> providerConfigurationCustomizer = (providerConfiguration) -> {};
    private Oauth2ServerProps oauth2ServerProps;

    /**
     * Sets the {@code Consumer} providing access to the {@link OidcProviderConfiguration.Builder}
     * allowing the ability to customize the claims of the OpenID Provider's configuration.
     *
     * @param providerConfigurationCustomizer the {@code Consumer} providing access to the {@link OidcProviderConfiguration.Builder}
     * @since 0.4.0
     */
    public void setProviderConfigurationCustomizer(Consumer<OidcProviderConfiguration.Builder> providerConfigurationCustomizer) {
        Assert.notNull(providerConfigurationCustomizer, "providerConfigurationCustomizer cannot be null");
        this.providerConfigurationCustomizer = providerConfigurationCustomizer;
    }

    public OidcCustomProviderConfigurationEndpointFilter(AuthorizationServerSettings providerSettings, Oauth2ServerProps oauth2ServerProps) {
        Assert.notNull(providerSettings, "providerSettings cannot be null");
        this.providerSettings = providerSettings;
        this.requestMatcher = new AntPathRequestMatcher(
                DEFAULT_OIDC_PROVIDER_CONFIGURATION_ENDPOINT_URI,
                HttpMethod.GET.name()
        );
        this.oauth2ServerProps = oauth2ServerProps;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        if (!this.requestMatcher.matches(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        AuthorizationServerContext authorizationServerContext = AuthorizationServerContextHolder.getContext();
        String issuer = authorizationServerContext.getIssuer();
        AuthorizationServerSettings authorizationServerSettings = authorizationServerContext.getAuthorizationServerSettings();

        OidcProviderConfiguration.Builder providerConfiguration = OidcProviderConfiguration.builder()
                .issuer(issuer)
//                .authorizationEndpoint(asUrl(issuer, authorizationServerSettings.getAuthorizationEndpoint()))
                .authorizationEndpoint(getExternalAuthorizationUrl(request)) //获取外部认证地址
                .deviceAuthorizationEndpoint(asUrl(issuer, authorizationServerSettings.getDeviceAuthorizationEndpoint()))
                .tokenEndpoint(asUrl(issuer, authorizationServerSettings.getTokenEndpoint()))
                .tokenEndpointAuthenticationMethods(clientAuthenticationMethods())
                .jwkSetUrl(asUrl(issuer, authorizationServerSettings.getJwkSetEndpoint()))
                .userInfoEndpoint(asUrl(issuer, authorizationServerSettings.getOidcUserInfoEndpoint()))
                .endSessionEndpoint(asUrl(issuer, authorizationServerSettings.getOidcLogoutEndpoint()))
                .responseType(OAuth2AuthorizationResponseType.CODE.getValue())
                .grantType(AuthorizationGrantType.AUTHORIZATION_CODE.getValue())
                .grantType(AuthorizationGrantType.CLIENT_CREDENTIALS.getValue())
                .grantType(AuthorizationGrantType.REFRESH_TOKEN.getValue())
                .grantType(AuthorizationGrantType.DEVICE_CODE.getValue())
                .tokenRevocationEndpoint(asUrl(issuer, authorizationServerSettings.getTokenRevocationEndpoint()))
                .tokenRevocationEndpointAuthenticationMethods(clientAuthenticationMethods())
                .tokenIntrospectionEndpoint(asUrl(issuer, authorizationServerSettings.getTokenIntrospectionEndpoint()))
                .tokenIntrospectionEndpointAuthenticationMethods(clientAuthenticationMethods())
                .codeChallengeMethod("S256")
                .subjectType("public")
                .idTokenSigningAlgorithm(SignatureAlgorithm.RS256.getName())
                .scope(OidcScopes.OPENID);

        this.providerConfigurationCustomizer.accept(providerConfiguration);

        ServletServerHttpResponse httpResponse = new ServletServerHttpResponse(response);
        this.providerConfigurationHttpMessageConverter.write(
                providerConfiguration.build(), MediaType.APPLICATION_JSON, httpResponse);
    }

    private static Consumer<List<String>> clientAuthenticationMethods() {
        return (authenticationMethods) -> {
            authenticationMethods.add(ClientAuthenticationMethod.CLIENT_SECRET_BASIC.getValue());
            authenticationMethods.add(ClientAuthenticationMethod.CLIENT_SECRET_POST.getValue());
            authenticationMethods.add(ClientAuthenticationMethod.NONE.getValue());
        };
    }

    private static String asUrl(String issuer, String endpoint) {
        if (endpoint.startsWith("http://") || endpoint.startsWith("https://")) {
            return endpoint;
        } else {
            return UriComponentsBuilder.fromUriString(issuer).path(endpoint).build().toUriString();
        }
    }

    private String getExternalAuthorizationUrl(HttpServletRequest request) {
        // 验证输入参数
        if (request == null) {
            throw new IllegalArgumentException("HttpServletRequest cannot be null.");
        }
        String authServerUrl = oauth2ServerProps.getWebServerUrl();
        String authorizationEndpoint = oauth2ServerProps.getAuthorizationEndpoint();
        // 验证从配置获取的URL
        if (authServerUrl == null || authServerUrl.isEmpty() || authorizationEndpoint == null || authorizationEndpoint.isEmpty()) {
            throw new IllegalStateException("Auth server URL or Authorization endpoint is not configured properly.");
        }
        String contextPath = request.getContextPath();
        // 使用URI来构造URL，避免潜在的安全问题
        try {
            URI uri = new URI(authServerUrl).resolve(contextPath + authorizationEndpoint);
            String externalAuthorizationUrl = uri.toString();
            logger.debug("externalAuthorizationUrl = " + externalAuthorizationUrl);
            return externalAuthorizationUrl;
        } catch (URISyntaxException e) {
            logger.error("Error constructing the external authorization URL.", e);
            throw new RuntimeException("Failed to construct the external authorization URL due to syntax error.", e);
        }
    }

}
