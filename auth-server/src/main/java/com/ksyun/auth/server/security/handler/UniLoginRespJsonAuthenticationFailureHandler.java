package com.ksyun.auth.server.security.handler;

import com.ksyun.auth.server.utils.HttpContextUtils;
import com.ksyun.auth.server.utils.JsonUtils;
import com.ksyun.common.constant.Response;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import java.io.IOException;

/**
 * 自定义认证失败处理器，适用于表单登录，即KCDE-manager/dls登录失败后的跳转逻辑处理
 *
 * <AUTHOR>
 * @since v1.8.0-2024/4/22
 */
@Slf4j
public class UniLoginRespJsonAuthenticationFailureHandler implements AuthenticationFailureHandler {

	@Override
	public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
		log.info("UniLogin authentication failure - {}", exception.getMessage());
		//构建认证失败的响应结果

		String respJson = JsonUtils.toJson(Response.failure().message(exception.getMessage()));
		log.info("UniLogin authentication failure resp: {}", respJson);
		HttpContextUtils.responseJson(respJson, response);
	}
}
