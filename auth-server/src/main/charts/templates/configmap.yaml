apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-config
  namespace: vela-system
data:
  application-prod.properties: |
      spring.datasource.url=***********************************************************************************************************************************************************************
      spring.datasource.username=privilege_rw
      spring.datasource.password=P7DjOCYWYi72RZVr
      spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

      mybatis-plus.mapper-locations=classpath*:mapper/*.xml
      mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

      cookie.salt=secret.dev

      auth.server.url=http://127.0.0.1:7070/authserver/

      spring.servlet.multipart.enabled=true
      spring.servlet.multipart.max-request-size=10MB
      spring.servlet.multipart.max-file-size=10MB

      auth-server.map.bigdata.url=http://127.0.0.1:7070/authserver/
      auth-server.map.bigdata.cookieNames=sso-token
      auth-server.map.bigdata.cacheExpire=30
      auth-server.map.bigdata.loginCacheTime=60
      auth-server.map.bigdata.domain=.kscbigdata.cloud
      auth-server.map.bigdata.logoutDomain=*

      app.ak=auth-server
      app.sk=3adcb390-064a-4e28-83a6-5afd88941271

      server.servlet.context-path=/authserver
      server.port=7070
      logging.file.path=/data/log/kcde-auth-server
      logging.level.com.ksyun.auth.server.mapper=debug
      logging.level.com.ksyun.privilege=debug
      logging.level.com.ksyun.privilege.dao.mapper=debug
      logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %c{36} %t - %m%n
      logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %c{36} %t - %m%n
      logging.config=classpath:logback-spring.xml
      spring.http.encoding.charset=UTF-8
      spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
      spring.jackson.time-zone=GMT+8
      upload.tmp.dir=/data/temp
      oauth2.on-off=false
      oauth2.client-id=kcde
      oauth2.client-secret=kcde
      oauth2.login-url=http://10.148.20.28:8003/toAuthLogin?platform=kcde
      oauth2.admin=6313b274-0f3a-4718-9265-88c5b48de607
      forest.variables.oauth2TokenUrl=http://10.148.20.28:8003/oauth2Inner
      forest.variables.kcdeUrl=http://vela-api-server-velacp.vela-system:8081
      forest.connect-timeout=1800000
      forest.read-timeout=1800000
