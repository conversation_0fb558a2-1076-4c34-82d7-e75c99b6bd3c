apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth
  namespace: vela-system
spec:
  replicas: 2
  selector:
    matchLabels:
      name: auth
  template:
    metadata:
      labels:
        name: auth
    spec:
      imagePullSecrets:
       - name: ksyunregistrykey
      containers:
        - name: auth
          image: harbor.sdns.kscbigdata.cloud:11180/kbdp/kcde-auth-server:0.4-arm
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 7070
              protocol: TCP
            - name: debug
              containerPort: 8888
              protocol: TCP
          volumeMounts:
            - mountPath: /data/apps/kcde-auth-server/config/
              name: host-time
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 1000m
              memory: 1Gi
          env:
            - name: auth_server_memory
              value: "1"
            - name: is_debug
              value: "false"
          readinessProbe:
            httpGet:
              path: /authserver/actuator/health/readiness
              port: http
            initialDelaySeconds: 60
            periodSeconds: 2
            failureThreshold: 5
            successThreshold: 2
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: name
                      operator: In
                      values:
                        - auth
                topologyKey: kubernetes.io/hostname
      priorityClassName: kcde-system-high
      volumes:
        - name: host-time
          configMap:
            name: auth-config