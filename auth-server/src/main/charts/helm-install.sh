#!/bin/bash

app_namespace=$1
app_name=$2
app_path=/data/deploy-yamls/$3

apps_status=`/usr/local/bin/helm list -n $app_namespace |grep "^$app_name" | wc -l`
echo "app_status  = $apps_status"
if [  ${apps_status} -eq 0 ] ; then
  echo "server install start"
  /usr/local/bin/helm install  --create-namespace -n $app_namespace $app_name ${app_path}
else
  echo "server upgrade start"
  /usr/local/bin/helm upgrade -n $app_namespace $app_name ${app_path}
fi