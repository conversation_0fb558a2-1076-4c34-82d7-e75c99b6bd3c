#!/bin/bash
auth_server_memory=${auth_server_memory:-"2"}

# 内存值设置为Pod内存的80%,需要给操作系统预留资源一定的资源
memory=`echo "scale=0; $auth_server_memory * 1024 * 0.8 / 1" | bc`
jvm_opts="-Xms${memory}m -Xmx${memory}m"
if [ "$is_debug" = "true" ]; then
    debug_opts="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8888"
else
    debug_opts=""
fi
echo "java ${jvm_opts} ${debug_opts} --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED -Dspring.config.location=classpath:application.properties,file:/data/apps/kcde-auth-server/config/application-prod.properties -jar /data/apps/kcde-auth-server/kcde-auth-server-0.0.1-SNAPSHOT.jar"
java ${jvm_opts} ${debug_opts} --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED -Dspring.config.location=classpath:application.properties,file:/data/apps/kcde-auth-server/config/application-prod.properties -jar /data/apps/kcde-auth-server/kcde-auth-server-0.0.1-SNAPSHOT.jar