<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ksyun.kbdp</groupId>
    <artifactId>auth-center</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>
    <name>auth-center</name>
    <description>公共组件公共服务</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <!--<java.version>1.8</java.version>-->
        <boot.version>3.2.3</boot.version>
        <junit.version>4.12</junit.version>
        <alibb.json.version>1.2.70</alibb.json.version>
        <jodd.version>3.9.1</jodd.version>
        <commons-lang3.version>3.8.1</commons-lang3.version>
        <org.mapstruct.version>1.2.0.CR1</org.mapstruct.version>
        <lombok.version>1.18.32</lombok.version>
        <slf4j.version>2.0.12</slf4j.version>
        <jwt.version>3.6.0</jwt.version>
        <powermock.version>2.0.9</powermock.version>
        <bouncycastle.version>1.66</bouncycastle.version>
        <dynamic.version>2.5.4</dynamic.version>
        <druid.version>1.2.6</druid.version>
        <easypoi.version>3.0.3</easypoi.version>
        <servlet.version>6.0.0</servlet.version>
        <mybatis.plus>3.5.5</mybatis.plus>
        <swagger.version>2.10.5</swagger.version>
        <freemarker.version>2.3.1.RELEASE</freemarker.version>
        <forest.version>1.5.36</forest.version>
        <!--公共服务组件-->
        <auth-client.version>kcde-0.6</auth-client.version>
        <auth-common.version>1.0.0</auth-common.version>
        <auth-service.version>1.0.0</auth-service.version>
        <jsch.version>0.1.55</jsch.version>
        <mockito-core.version>3.12.4</mockito-core.version>
        <mysql-connector-j.version>8.0.33</mysql-connector-j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus}</version>
            </dependency>
            <!--动态数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.version}</version>
            </dependency>
            <!-- druid数据库连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--org.jodd-->
            <dependency>
                <groupId>org.jodd</groupId>
                <artifactId>jodd-http</artifactId>
                <version>${jodd.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodd</groupId>
                <artifactId>jodd-core</artifactId>
                <version>${jodd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${alibb.json.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <!-- swagger -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!--map struct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <!--jwt-->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <!--powermock反射-->
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-core</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>${jsch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector-j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!--easypoi文档处理-->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-web</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-annotation</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <!-- servlet -->
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${servlet.version}</version>
                <scope>provided</scope>
            </dependency>


            <dependency>
                <groupId>com.dtflys.forest</groupId>
                <artifactId>forest-spring-boot3-starter</artifactId>
                <version>${forest.version}</version>
                <!--                <exclusions>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>commons-logging</artifactId>-->
                <!--                        <groupId>commons-logging</groupId>-->
                <!--                    </exclusion>-->
                <!--                    <exclusion>-->
                <!--                        <groupId>org.slf4j</groupId>-->
                <!--                        <artifactId>slf4j-api</artifactId>-->
                <!--                    </exclusion>-->
                <!--                </exclusions>-->
            </dependency>


            <!--公共组件公共服务start-->
            <dependency>
                <groupId>com.ksyun.kbdp</groupId>
                <artifactId>auth-client</artifactId>
                <version>${auth-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun.kbdp</groupId>
                <artifactId>auth-common</artifactId>
                <version>${auth-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun.kbdp</groupId>
                <artifactId>auth-service</artifactId>
                <version>${auth-service.version}</version>
            </dependency>
            <!--公共组件公共服务start-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>1.12.4</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>3.2.3</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>5.1.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>jdk17_compile</id>
            <properties>
                <JAVA17_HOME>/data/zhongbin/jdk-17.0.9</JAVA17_HOME>
            </properties>
        </profile>
        <profile>
            <id>jdk17_local</id>
            <properties>
                <JAVA17_HOME>/home/<USER>/apps/jdk-17.0.12</JAVA17_HOME>
            </properties>
        </profile>
    </profiles>
    <build>


        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                    <configuration>
                        <target>17</target>
                        <source>17</source>
                        <verbose>true</verbose>
                        <fork>true</fork>
                        <executable>${JAVA17_HOME}/bin/javac</executable>
                        <compilerVersion>17</compilerVersion>
                        <parameters>true</parameters>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <modules>
        <module>auth-privilege</module>
        <module>auth-client</module>
        <module>auth-common</module>
        <module>auth-server</module>
        <module>auth-service</module>
    </modules>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://10.69.82.173:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshot</id>
            <name>maven-snapshot</name>
            <url>http://10.69.82.173:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
