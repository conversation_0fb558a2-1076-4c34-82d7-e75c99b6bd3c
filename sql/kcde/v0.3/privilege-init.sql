/*
Navicat MySQL Data Transfer

Source Server         : 农行联调数据库
Source Server Version : 50721
Source Host           : 127.0.0.1:13306
Source Database       : privilege

Target Server Type    : MYSQL
Target Server Version : 50721
File Encoding         : 65001

Date: 2022-09-19 10:33:42
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for app_aksk
-- ----------------------------
DROP TABLE IF EXISTS `app_aksk`;
CREATE TABLE `app_aksk` (
  `ak` varchar(255) NOT NULL COMMENT 'AK,',
  `sk` varchar(255) NOT NULL COMMENT 'SK',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`ak`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用aksk';

-- ----------------------------
-- Table structure for auth_dynamic_key
-- ----------------------------
DROP TABLE IF EXISTS `auth_dynamic_key`;
CREATE TABLE `auth_dynamic_key` (
  `period` bigint(20) NOT NULL COMMENT '周期，每 10 分钟向下取整得到',
  `dynamic_key` varchar(255) NOT NULL COMMENT '动态码',
  PRIMARY KEY (`period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='认证动态秘钥';

-- ----------------------------
-- Table structure for basic_app
-- ----------------------------
DROP TABLE IF EXISTS `basic_app`;
CREATE TABLE `basic_app` (
  `ak` varchar(255) NOT NULL COMMENT 'ak',
  `context_path` varchar(255) NOT NULL COMMENT '路径',
  `description` varchar(255) NOT NULL COMMENT '描述',
  `product_code` varchar(255) NOT NULL COMMENT '产品code'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用基础信息';

-- ----------------------------
-- Table structure for basic_tags
-- ----------------------------
DROP TABLE IF EXISTS `basic_tags`;
CREATE TABLE `basic_tags` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `name` varchar(255) NOT NULL COMMENT '标签名',
  `key` varchar(255) DEFAULT NULL COMMENT '标签key',
  `group` varchar(255) NOT NULL COMMENT '标签组',
  `role_type_apply` int(11) DEFAULT '0' COMMENT '适用角色类型 0：所有，1：平台角色 2：项目角色',
  UNIQUE KEY `bt_key_index` (`key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '分类名称',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='权限组分类';

-- ----------------------------
-- Table structure for group
-- ----------------------------
DROP TABLE IF EXISTS `group`;
CREATE TABLE `group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '分组名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分组描述',
  `tenant_id` bigint(20) NOT NULL COMMENT '所属租户ID, 0 表示该组不区分租户，所有租户能用',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='群组';

-- ----------------------------
-- Table structure for group_role
-- ----------------------------
DROP TABLE IF EXISTS `group_role`;
CREATE TABLE `group_role` (
  `group_id` bigint(20) NOT NULL COMMENT '分组Id',
  `role_id` bigint(20) NOT NULL COMMENT '角色Id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分组与角色的关联时间',
  PRIMARY KEY (`group_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='群主角色';

-- ----------------------------
-- Table structure for group_user
-- ----------------------------
DROP TABLE IF EXISTS `group_user`;
CREATE TABLE `group_user` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `group_id` bigint(20) NOT NULL COMMENT '角色ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`user_id`,`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='群主用户';

-- ----------------------------
-- Table structure for ip_config
-- ----------------------------
DROP TABLE IF EXISTS `ip_config`;
CREATE TABLE `ip_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '租户id',
  `user_name` varchar(255) NOT NULL COMMENT '租户名称',
  `black_list` varchar(255) DEFAULT NULL COMMENT '黑名单',
  `prod_white_list` varchar(255) DEFAULT NULL COMMENT '生产白名单',
  `test_white_list` varchar(255) DEFAULT NULL COMMENT '测试白名单',
  `status` enum('NORMAL','DELETED') DEFAULT 'NORMAL' COMMENT '数据状态，做逻辑删除时使用',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for permission_group
-- ----------------------------
DROP TABLE IF EXISTS `permission_group`;
CREATE TABLE `permission_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '权限分组名称',
  `description` varchar(500) DEFAULT NULL,
  `readonly` tinyint(1) DEFAULT '1' COMMENT '是否只读',
  `status` enum('NORMAL','CLOSE','DELETED') DEFAULT 'NORMAL' COMMENT '数据状态，做逻辑删除时使用',
  `source` tinyint(1) DEFAULT '1' COMMENT '类型 0 内置 1 自定义',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类id',
  `create_by` varchar(100) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unionNameIndex` (`name`,`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8 COMMENT='权限组';

-- ----------------------------
-- Table structure for privilege
-- ----------------------------
DROP TABLE IF EXISTS `privilege`;
CREATE TABLE `privilege` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL COMMENT '权限编码',
  `name` varchar(255) NOT NULL COMMENT '权限名称',
  `type` bigint(2) NOT NULL COMMENT '权限类型 1 菜单 2 按钮',
  `icon` varchar(255) DEFAULT NULL COMMENT 'icon名称',
  `order` bigint(20) DEFAULT NULL COMMENT '顺序',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父ID',
  `url` varchar(4096) DEFAULT NULL COMMENT '权限路径',
  `business` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `ak` varchar(255) DEFAULT NULL COMMENT 'ak',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8 COMMENT='权限点';

-- ----------------------------
-- Table structure for privilege_permission_group
-- ----------------------------
DROP TABLE IF EXISTS `privilege_permission_group`;
CREATE TABLE `privilege_permission_group` (
  `privilege_id` bigint(20) NOT NULL COMMENT '权限id',
  `permission_group_id` bigint(20) NOT NULL COMMENT '权限分组id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  KEY `privilege_id_permission_group_id` (`privilege_id`,`permission_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='权限组与权限点';

-- ----------------------------
-- Table structure for privilege_props
-- ----------------------------
DROP TABLE IF EXISTS `privilege_props`;
CREATE TABLE `privilege_props` (
  `privilege_id` bigint(20) NOT NULL COMMENT '权限ID',
  `prop_key` varchar(255) NOT NULL COMMENT '权限属性KEY',
  `prop_value` varchar(255) NOT NULL COMMENT '权限属性值',
  KEY `privilege_id_key_value` (`privilege_id`,`prop_key`,`prop_value`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='权限配置表';

-- ----------------------------
-- Table structure for project
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '项目名称',
  `chinese_name` varchar(255) NOT NULL COMMENT '项目中文名称',
  `type` varchar(255) NOT NULL COMMENT '项目中文名称',
  `description` varchar(500) DEFAULT NULL COMMENT '项目备注',
  `owner_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `status` enum('ENABLED','DISABLED','DELETED','ARCHIVED') DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `required_approve` tinyint(1) DEFAULT '1' COMMENT '作业是否需要审批，默认需要审批',
  `export_package_required_approve` varchar(255) NOT NULL COMMENT '项目中文名称',
  `project_depend` tinyint(1) DEFAULT '0' COMMENT '允许跨项目依赖，默认需要审批 1允许 0 不允许  ',
  `space_id` int(20) DEFAULT '0',
  `project_depend_status` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='项目';

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL COMMENT '角色编码',
  `name` varchar(255) NOT NULL COMMENT '角色中文名',
  `type` bigint(2) NOT NULL COMMENT '角色级别 1 平台级别 2 项目级别',
  `role` varchar(50) DEFAULT NULL COMMENT '角色标签',
  `description` varchar(500) DEFAULT NULL,
  `tag_ids` varchar(255) DEFAULT NULL COMMENT '角色标签',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `default` varchar(255) NOT NULL DEFAULT '0' COMMENT '0 非默认 1默认 ',
  `source` enum('bigdata','ope','ops') NOT NULL DEFAULT 'bigdata',
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_code_index` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='角色';

-- ----------------------------
-- Table structure for role_permission_group
-- ----------------------------
DROP TABLE IF EXISTS `role_permission_group`;
CREATE TABLE `role_permission_group` (
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `role_name` varchar(255) DEFAULT NULL,
  `permission_group_id` bigint(20) NOT NULL COMMENT '权限分组id',
  `permission_group_name` varchar(255) DEFAULT NULL COMMENT '权限分组名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  KEY `role_id_permission_group_id` (`role_id`,`permission_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色权限组关系';

-- ----------------------------
-- Table structure for role_privilege
-- ----------------------------
DROP TABLE IF EXISTS `role_privilege`;
CREATE TABLE `role_privilege` (
  `role_id` bigint(20) NOT NULL COMMENT '角色编号',
  `privilege_id` bigint(255) NOT NULL COMMENT '权限编号',
  `privilege_code` varchar(255) DEFAULT NULL COMMENT '权限编码',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  KEY `idx_role_id` (`role_id`) USING BTREE,
  KEY `idx_privilege_id` (`privilege_id`) USING BTREE,
  KEY `idx_privilege_code` (`privilege_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '用户名',
  `source` enum('LOCAL','LDAP','PASSPORT','CAM','UASS','UASS-SELF') DEFAULT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '所属租户ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `status` enum('NORMAL','DELETED') DEFAULT NULL COMMENT '数据状态，做逻辑删除时使用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='租户与子账户';

-- ----------------------------
-- Table structure for user_props
-- ----------------------------
DROP TABLE IF EXISTS `user_props`;
CREATE TABLE `user_props` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `prop_key` varchar(255) NOT NULL COMMENT '用户属性KEY',
  `prop_value` text NOT NULL,
  PRIMARY KEY (`user_id`,`prop_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户属性';

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户Id',
  `role_id` bigint(20) NOT NULL COMMENT '角色Id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用户与角色的关联时间',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户角色';
