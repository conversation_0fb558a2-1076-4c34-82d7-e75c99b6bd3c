use privilege;
ALTER TABLE `privilege`.`role`
    ADD COLUMN `platform` VARCHAR(45) NULL COMMENT '平台名称，KCDE、HH、CAS' AFTER `source`;
ALTER TABLE `privilege`.`user`
    ADD COLUMN `remark` VARCHAR(150) NULL COMMENT '备注，可用于存放一些扩展信息，如部门' AFTER `oidc_id`;
ALTER TABLE `privilege`.`user`
    CHANGE COLUMN `source` `source` ENUM('LOCAL', 'LDAP', 'PASSPORT', 'OIDC', 'CAS', 'TEST', 'HH') NULL DEFAULT NULL ;
INSERT INTO `privilege`.`oauth2_registered_client`(`id`, `client_id`, `client_id_issued_at`, `client_secret`, `client_secret_expires_at`, `client_name`, `client_authentication_methods`, `authorization_grant_types`, `redirect_uris`, `post_logout_redirect_uris`, `scopes`, `client_settings`, `token_settings`) VALUES ('e52e28d5-b2aa-441c-8ff3-414b7f39ef87', 'kcde-gateway-Client', '2024-10-30 09:14:44', '$2a$10$4cwPLTtf8.kRl3fuBUBsKOLPzYYFHMJMOXjlFGB1Da2UWZU9qbbmy', NULL, 'kcde-gateway-Client', 'client_secret_basic', 'refresh_token,client_credentials,authorization_code', 'http://ec.kcde.ksyun.com/gateway/login/oauth2/code/kcde-auth-server-client', '', 'openid,profile', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.client.require-proof-key\":false,\"settings.client.require-authorization-consent\":false}', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":[\"java.time.Duration\",3600.000000000],\"settings.token.authorization-code-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.device-code-time-to-live\":[\"java.time.Duration\",300.000000000]}');

-- 初始化瀚海管理员的用户
INSERT INTO `privilege`.`user` (`name`, `source`, `create_time`, `update_time`, `status`, `oidc_id`, `remark`)
SELECT 'hanhai_admin', 'LOCAL', NOW(), NOW(), 'NORMAL', NULL, ''
    WHERE NOT EXISTS (SELECT 1 FROM `privilege`.`user` WHERE `name` = 'hanhai_admin');

-- 初始化瀚海管理员的角色
INSERT INTO `privilege`.`user_role` (`user_id`, `role_id`, `create_time`)
SELECT (SELECT id FROM `privilege`.`user` WHERE `name` = 'hanhai_admin'),
       (SELECT id FROM `privilege`.`role` WHERE `code` = 'hanhai_admin'),
       NOW()
    WHERE NOT EXISTS (
    SELECT 1
    FROM `privilege`.`user_role`
    WHERE `user_id` = (SELECT id FROM `privilege`.`user` WHERE `name` = 'hanhai_admin')
      AND `role_id` = (SELECT id FROM `privilege`.`role` WHERE `code` = 'hanhai_admin')
);