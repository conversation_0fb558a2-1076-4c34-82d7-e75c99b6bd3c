/*
 Navicat Premium Data Transfer

 Source Server         : kced_arm
 Source Server Type    : MySQL
 Source Server Version : 50739
 Source Host           : localhost:13311
 Source Schema         : privilege

 Target Server Type    : MySQL
 Target Server Version : 50739
 File Encoding         : 65001

 Date: 05/01/2023 15:29:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_aksk
-- ----------------------------
DROP TABLE IF EXISTS `app_aksk`;
CREATE TABLE `app_aksk`  (
  `ak` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'AK,',
  `sk` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SK',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`ak`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '应用aksk' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_aksk
-- ----------------------------
INSERT INTO `app_aksk` VALUES ('dls-server', '7c561462-54fd-48b2-a0c4-fadd553be1f8', 'DLS服务', 0, '2019-08-15 09:35:40');
INSERT INTO `app_aksk` VALUES ('gaea-server', '3adcb390-064a-4e28-83a6-5afd88941271', 'GAEA服务', 0, '2019-08-15 09:35:40');
INSERT INTO `app_aksk` VALUES ('kcde-server', '77b909f9-6216-4770-acd0-5a3b1c1868f2', 'KCDE服务', 0, '2021-02-01 16:00:23');
INSERT INTO `app_aksk` VALUES ('kcde-tenant', '5a2918ec-f4ac-11ed-818b-4bc3f06736d8', 'KCDE租户中心', 0, '2023-05-17 20:15:20');

-- ----------------------------
-- Table structure for auth_dynamic_key
-- ----------------------------
DROP TABLE IF EXISTS `auth_dynamic_key`;
CREATE TABLE `auth_dynamic_key`  (
  `period` bigint(20) NOT NULL COMMENT '周期，每 10 分钟向下取整得到',
  `dynamic_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '动态码',
  PRIMARY KEY (`period`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '认证动态秘钥' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of auth_dynamic_key
-- ----------------------------
INSERT INTO `auth_dynamic_key` VALUES (20230105152020, '20230105152020');

-- ----------------------------
-- Table structure for basic_app
-- ----------------------------
DROP TABLE IF EXISTS `basic_app`;
CREATE TABLE `basic_app`  (
  `ak` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ak',
  `context_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '路径',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '描述',
  `product_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品code'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '应用基础信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_app
-- ----------------------------
INSERT INTO `basic_app` VALUES ('kcde-server', '/', 'KCDE应用', 'kcde-server');
INSERT INTO `basic_app` VALUES ('dls-server', '/dls', 'DLS应用', 'dls-server');
INSERT INTO `basic_app` VALUES ('gaea-server', '/storage-rm', 'GAEA应用', 'gaea-server');

-- ----------------------------
-- Table structure for basic_tags
-- ----------------------------
DROP TABLE IF EXISTS `basic_tags`;
CREATE TABLE `basic_tags`  (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签名',
  `key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签key',
  `group` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签组',
  `role_type_apply` int(11) NULL DEFAULT 0 COMMENT '适用角色类型 0：所有，1：平台角色 2：项目角色',
  UNIQUE INDEX `bt_key_index`(`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_tags
-- ----------------------------

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类名称',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类描述',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限组分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of category
-- ----------------------------

-- ----------------------------
-- Table structure for group
-- ----------------------------
DROP TABLE IF EXISTS `group`;
CREATE TABLE `group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组名称',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分组描述',
  `tenant_id` bigint(20) NOT NULL COMMENT '所属租户ID, 0 表示该组不区分租户，所有租户能用，kcde中用到的地方都设置为了0',
  `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group
-- ----------------------------

-- ----------------------------
-- Table structure for group_role
-- ----------------------------
DROP TABLE IF EXISTS `group_role`;
CREATE TABLE `group_role`  (
  `group_id` bigint(20) NOT NULL COMMENT '分组Id',
  `role_id` bigint(20) NOT NULL COMMENT '角色Id',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '分组与角色的关联时间',
  PRIMARY KEY (`group_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群主角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group_role
-- ----------------------------

-- ----------------------------
-- Table structure for group_user
-- ----------------------------
DROP TABLE IF EXISTS `group_user`;
CREATE TABLE `group_user`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `group_id` bigint(20) NOT NULL COMMENT '角色ID',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`user_id`, `group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群主用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group_user
-- ----------------------------

-- ----------------------------
-- Table structure for ip_config
-- ----------------------------
DROP TABLE IF EXISTS `ip_config`;
CREATE TABLE `ip_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '租户id',
  `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '租户名称',
  `black_list` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '黑名单',
  `prod_white_list` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生产白名单',
  `test_white_list` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '测试白名单',
  `status` enum('NORMAL','DELETED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'NORMAL' COMMENT '数据状态，做逻辑删除时使用',
  `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ip_config
-- ----------------------------

-- ----------------------------
-- Table structure for permission_group
-- ----------------------------
DROP TABLE IF EXISTS `permission_group`;
CREATE TABLE `permission_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限分组名称',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `readonly` tinyint(1) NULL DEFAULT 1 COMMENT '是否只读',
  `status` enum('NORMAL','CLOSE','DELETED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'NORMAL' COMMENT '数据状态，做逻辑删除时使用',
  `source` tinyint(1) NULL DEFAULT 1 COMMENT '类型 0 内置 1 自定义',
  `category_id` bigint(20) NULL DEFAULT NULL COMMENT '分类id',
  `create_by` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unionNameIndex`(`name`, `category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 98 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permission_group
-- ----------------------------
INSERT INTO `permission_group` VALUES (90, '用户管理_读', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (91, '用户管理_写', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (92, '角色管理_读', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (93, '角色管理_写', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (94, '权限点_读', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (95, '权限点_写', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (96, '权限组_读', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` VALUES (97, '权限组_写', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` values (98, '系统授权_读', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');
INSERT INTO `permission_group` values (99, '系统授权_写', '', 0, 'NORMAL', 0, 1, '系统', '2022-10-12 11:27:44', '2022-10-12 11:27:44');

-- ----------------------------
-- Table structure for privilege
-- ----------------------------
DROP TABLE IF EXISTS `privilege`;
CREATE TABLE `privilege`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限编码',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限名称',
  `type` bigint(2) NOT NULL COMMENT '权限类型 1 菜单 2 按钮',
  `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'icon名称',
  `order` bigint(20) NULL DEFAULT NULL COMMENT '顺序',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父ID',
  `url` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限路径',
  `business` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务类型',
  `ak` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ak',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 239 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of privilege
-- ----------------------------
INSERT INTO `privilege` VALUES (214, 'KCDE_PTGL', '平台管理', 1, 'iconfont  icon-pingtaiguanli', 214, 0, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (215, 'KCDE_PTGL_User', '用户管理', 1, '', 215, 214, '#/main/userManager', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (216, 'KCDE_PTGL_User_New', '新增用户', 2, '', 216, 215, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (217, 'KCDE_PTGL_User_Edit', '编辑用户', 2, '', 217, 215, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (218, 'KCDE_PTGL_User_Delete', '删除用户', 2, '', 218, 215, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (219, 'KCDE_PTGL_User_Details', '用户详情', 2, '', 219, 215, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (220, 'KCDE_PTGL_User_ChangePW', '修改密码', 2, '', 220, 215, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (221, 'KCDE_PTGL_Role', '角色管理', 1, '', 221, 214, '#/main/roleManager', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (222, 'KCDE_PTGL_Role_Edit', '编辑', 2, '', 222, 221, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (223, 'KCDE_PTGL_Role_AddUser', '添加用户', 2, '', 223, 221, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (224, 'KCDE_PTGL_Authority', '权限点', 1, '', 224, 214, '#/main/jurisdictionManager', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (225, 'KCDE_PTGL_Authority_New', '新增权限点', 2, '', 225, 224, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (226, 'KCDE_PTGL_Authority_Edit', '编辑权限点', 2, '', 226, 224, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (227, 'KCDE_PTGL_Authority_Delete', '删除权限点', 2, '', 227, 224, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (228, 'KCDE_PTGL_Authority_Up', '上移', 2, '', 228, 224, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (229, 'KCDE_PTGL_Authority_Down', '下移', 2, '', 229, 224, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (230, 'KCDE_PTGL_Authority_Top', '置顶', 2, '', 230, 224, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (231, 'KCDE_PTGL_AuthorityGroup', '权限组', 1, '', 231, 214, '#/main/jurisdictionGroupManager', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (232, 'KCDE_PTGL_AuthorityGroup_New', '新增权限组', 2, '', 232, 231, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (233, 'KCDE_PTGL_AuthorityGroup_Edit', '编辑权限组', 2, '', 233, 231, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (234, 'KCDE_PTGL_AuthorityGroup_Delete', '删除权限组', 2, '', 234, 231, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (235, 'KCDE_PTGL_AuthorityGroup_Details', '权限组详情', 2, '', 235, 231, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (236, 'KCDE_PTGL_AuthorityGroup_Public', '公开', 2, '', 236, 231, '', NULL, 'kcde-server', '2022-10-12 11:27:44');
INSERT INTO `privilege` VALUES (237, 'KCDE_PTGL_License', '系统授权', 1, NULL, 232, 214, '#/main/systemAuthorization', NULL, 'kcde-server', '2023-01-05 15:27:48');
INSERT INTO `privilege` VALUES (238, 'KCDE_PTGL_License_Import', '导入License', 2, NULL, 232, 237, NULL, NULL, 'kcde-server', '2023-01-05 15:28:54');

-- ----------------------------
-- Table structure for privilege_permission_group
-- ----------------------------
DROP TABLE IF EXISTS `privilege_permission_group`;
CREATE TABLE `privilege_permission_group`  (
  `privilege_id` bigint(20) NOT NULL COMMENT '权限id',
  `permission_group_id` bigint(20) NOT NULL COMMENT '权限分组id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  INDEX `privilege_id_permission_group_id`(`privilege_id`, `permission_group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限组与权限点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of privilege_permission_group
-- ----------------------------
INSERT INTO `privilege_permission_group` VALUES (214, 90, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (215, 90, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (216, 91, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (217, 91, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (218, 91, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (219, 91, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (220, 91, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (221, 92, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (222, 93, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (223, 93, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (224, 94, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (225, 95, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (226, 95, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (227, 95, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (228, 95, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (229, 95, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (230, 95, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (231, 96, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (232, 97, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (233, 97, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (234, 97, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (235, 97, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` VALUES (236, 97, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` values (237, 98, '2022-10-12 11:27:44');
INSERT INTO `privilege_permission_group` values (238, 99, '2022-10-12 11:27:44');

-- ----------------------------
-- Table structure for privilege_props
-- ----------------------------
DROP TABLE IF EXISTS `privilege_props`;
CREATE TABLE `privilege_props`  (
  `privilege_id` bigint(20) NOT NULL COMMENT '权限ID',
  `prop_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限属性KEY',
  `prop_value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限属性值',
  INDEX `privilege_id_key_value`(`privilege_id`, `prop_key`, `prop_value`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of privilege_props
-- ----------------------------

-- ----------------------------
-- Table structure for project
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目名称',
  `chinese_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目中文名称',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目中文名称',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目备注',
  `owner_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `status` enum('ENABLED','DISABLED','DELETED','ARCHIVED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `required_approve` tinyint(1) NULL DEFAULT 1 COMMENT '作业是否需要审批，默认需要审批',
  `export_package_required_approve` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目中文名称',
  `project_depend` tinyint(1) NULL DEFAULT 0 COMMENT '允许跨项目依赖，默认需要审批 1允许 0 不允许  ',
  `space_id` int(20) NULL DEFAULT 0,
  `project_depend_status` tinyint(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of project
-- ----------------------------

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色编码',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色中文名',
  `type` bigint(2) NOT NULL COMMENT '角色级别 1 平台级别 2 项目级别',
  `role` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色标签',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `tag_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色标签',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后更新时间',
  `default` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '0 非默认 1默认 ',
  `source` enum('bigdata','ope','ops') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'bigdata',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `role_code_index`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, 'admin', '管理员', 1, NULL, '可访问所有页面及所有功能1', NULL, '2022-10-12 11:27:45', '系统', '2022-10-13 17:40:13', '1', 'bigdata');

-- ----------------------------
-- Table structure for role_permission_group
-- ----------------------------
DROP TABLE IF EXISTS `role_permission_group`;
CREATE TABLE `role_permission_group`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `role_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `permission_group_id` bigint(20) NOT NULL COMMENT '权限分组id',
  `permission_group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限分组名称',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  INDEX `role_id_permission_group_id`(`role_id`, `permission_group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色权限组关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permission_group
-- ----------------------------
INSERT INTO `role_permission_group` VALUES (1, '管理员', 90, '用户管理_读', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 91, '用户管理_写', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 92, '角色管理_读', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 93, '角色管理_写', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 94, '权限点_读', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 95, '权限点_写', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 96, '权限组_读', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` VALUES (1, '管理员', 97, '权限组_写', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` values (1, '管理员', 98, '系统授权_读', '2022-10-12 11:27:45');
INSERT INTO `role_permission_group` values (1, '管理员', 99, '系统授权_写', '2022-10-12 11:27:45');

-- ----------------------------
-- Table structure for role_privilege
-- ----------------------------
DROP TABLE IF EXISTS `role_privilege`;
CREATE TABLE `role_privilege`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色编号',
  `privilege_id` bigint(255) NOT NULL COMMENT '权限编号',
  `privilege_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限编码',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  INDEX `idx_role_id`(`role_id`) USING BTREE,
  INDEX `idx_privilege_id`(`privilege_id`) USING BTREE,
  INDEX `idx_privilege_code`(`privilege_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_privilege
-- ----------------------------

-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant` (
  `id` bigint(20) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT '租户名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `status` enum('NORMAL', 'DELETING', 'DELETED') DEFAULT NULL COMMENT '数据状态，做逻辑删除时使用',
  `created_by` bigint NOT NULL COMMENT '租户创建的用户id',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `description` varchar(512) DEFAULT NULL COMMENT '说明',
  PRIMARY KEY `tenant_id_index` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户';

-- ----------------------------
-- Records of tenant
-- ----------------------------
INSERT INTO `tenant` VALUES (0, 'default', '2019-08-28 19:53:19', '2019-08-28 19:53:19', 'NORMAL', 1, '系统', '系统');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户名',
  `source` enum('LOCAL','LDAP','PASSPORT','OIDC') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  -- `tenant_id` bigint(20) NOT NULL COMMENT '所属租户ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后更新时间',
  `status` enum('NORMAL','DELETED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据状态，做逻辑删除时使用',
  `oidc_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '统一登录外部系统ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_oidc_id`(`oidc_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '租户与子账户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', 'LOCAL', '2019-08-28 19:53:19', '2019-08-28 19:53:19', 'NORMAL', NULL);

-- ----------------------------
-- Table structure for user_props
-- ----------------------------
DROP TABLE IF EXISTS `user_props`;
CREATE TABLE `user_props`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `prop_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户属性KEY',
  `prop_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`user_id`, `prop_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户属性' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_props
-- ----------------------------
INSERT INTO `user_props` VALUES (1, 'alias', 'uat_test');
INSERT INTO `user_props` VALUES (1, 'iam_id', '*********');
INSERT INTO `user_props` VALUES (1, 'passport_id', '*********');
INSERT INTO `user_props` VALUES (1, 'password', '57ed0c1a735a3d66f3fd0faab92421cc');
INSERT INTO `user_props` VALUES (1, 'phone', '18310902030');
INSERT INTO `user_props` VALUES (1, 'secret_level', '5');
INSERT INTO `user_props` VALUES (1, 'userAk', '66ee3fbcac9d820575e2d713690ad17cb01df3ed39e3255d5ef0ff6fa53ef5ac');
INSERT INTO `user_props` VALUES (1, 'userAk.create-time', '2020-08-27 10:46:16');
INSERT INTO `user_props` VALUES (1, 'userAkSk-status', 'NORMAL');
INSERT INTO `user_props` VALUES (1, 'userSk', 'eb92659bdbbedb56e37e2bd263cdfe8c61dfd873b0a48d033d229a13ca6b5865');

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户Id',
  `role_id` bigint(20) NOT NULL COMMENT '角色Id',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '用户与角色的关联时间',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_role
-- ----------------------------
INSERT INTO `user_role` VALUES (1, 1, '2019-08-28 19:53:19');

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_tenant`;
CREATE TABLE `user_tenant` (
  `user_id` bigint(20) NOT NULL COMMENT '用户Id',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户Id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用户与租户的绑定时间',
  PRIMARY KEY (`user_id`, `tenant_id`),
  INDEX `user_id_index` (`tenant_id`) USING BTREE,
  INDEX `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户';

-- ----------------------------
-- Records of user_tenant
-- ----------------------------
INSERT INTO `user_tenant` VALUES (1, 1, '2019-08-28 19:53:19');

SET FOREIGN_KEY_CHECKS = 1;
