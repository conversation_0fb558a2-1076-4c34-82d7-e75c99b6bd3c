
-- RSA Keys table
CREATE TABLE IF NOT EXISTS rsa_keys (
                                        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                        user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    name VARCHAR(255) NOT NULL COMMENT '密钥名称',
    type ENUM('用户上传', '平台生成') NOT NULL COMMENT '密钥类型',
    public_key VARCHAR(2048) NOT NULL COMMENT 'Base64编码的公钥',
    private_key VARCHAR(4096) COMMENT 'Base64编码的加密私钥，用户上传时可为空',
    key_size INT NOT NULL DEFAULT 2048 COMMENT '密钥长度（位）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '最后更新时间',
    UNIQUE KEY uk_name (name) COMMENT '名称唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RSA密钥管理表';

-- person access token
CREATE TABLE IF NOT EXISTS person_access_token (
                                                   id BIGINT AUTO_INCREMENT COMMENT '主键ID',
                                                   user_id BIGINT NOT NULL COMMENT '用户ID',
                                                   token_value VARCHAR(2048) NOT NULL COMMENT '令牌值',
    lifetime_seconds BIGINT NOT NULL COMMENT '有效期（秒）',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人访问令牌表';