

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
use privilege;
-- ----------------------------
-- Table structure for app_aksk
-- ----------------------------
DROP TABLE IF EXISTS `app_aksk`;
CREATE TABLE `app_aksk`  (
                             `ak` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'AK,',
                             `sk` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'SK',
                             `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                             `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
                             `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                             PRIMARY KEY (`ak`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '应用aksk' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app_aksk
-- ----------------------------
INSERT INTO `app_aksk` VALUES ('dls-server', '7c561462-54fd-48b2-a0c4-fadd553be1f8', 'DLS服务', 0, '2019-08-15 09:35:40');
INSERT INTO `app_aksk` VALUES ('gaea-server', '3adcb390-064a-4e28-83a6-5afd88941271', 'GAEA服务', 0, '2019-08-15 09:35:40');
INSERT INTO `app_aksk` VALUES ('kcde-server', '77b909f9-6216-4770-acd0-5a3b1c1868f2', 'KCDE服务', 0, '2021-02-01 16:00:23');
INSERT INTO `app_aksk` VALUES ('kcde-tenant', '5a2918ec-f4ac-11ed-818b-4bc3f06736d8', 'KCDE租户中心', 0, '2023-05-17 20:15:20');

-- ----------------------------
-- Table structure for auth_dynamic_key
-- ----------------------------
DROP TABLE IF EXISTS `auth_dynamic_key`;
CREATE TABLE `auth_dynamic_key`  (
                                     `period` bigint(20) NOT NULL COMMENT '周期，每 10 分钟向下取整得到',
                                     `dynamic_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '动态码',
                                     PRIMARY KEY (`period`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '认证动态秘钥' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of auth_dynamic_key
-- ----------------------------
INSERT INTO `auth_dynamic_key` VALUES (20230105152020, '20230105152020');

-- ----------------------------
-- Table structure for basic_app
-- ----------------------------
DROP TABLE IF EXISTS `basic_app`;
CREATE TABLE `basic_app`  (
                              `ak` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ak',
                              `context_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '路径',
                              `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '描述',
                              `product_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品code'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '应用基础信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_app
-- ----------------------------
INSERT INTO `basic_app` VALUES ('kcde-server', '/', 'KCDE应用', 'kcde-server');
INSERT INTO `basic_app` VALUES ('dls-server', '/dls', 'DLS应用', 'dls-server');
INSERT INTO `basic_app` VALUES ('gaea-server', '/storage-rm', 'GAEA应用', 'gaea-server');

-- ----------------------------
-- Table structure for basic_tags
-- ----------------------------
DROP TABLE IF EXISTS `basic_tags`;
CREATE TABLE `basic_tags`  (
                               `id` bigint(20) NOT NULL COMMENT '编号',
                               `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签名',
                               `key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签key',
                               `group` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签组',
                               `role_type_apply` int(11) NULL DEFAULT 0 COMMENT '适用角色类型 0：所有，1：平台角色 2：项目角色',
                               UNIQUE INDEX `bt_key_index`(`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_tags
-- ----------------------------

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT,
                             `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类名称',
                             `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类描述',
                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改时间',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限组分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of category
-- ----------------------------

-- ----------------------------
-- Table structure for group
-- ----------------------------
DROP TABLE IF EXISTS `group`;
CREATE TABLE `group`  (
                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                          `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组名称',
                          `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分组描述',
                          `tenant_id` bigint(20) NOT NULL COMMENT '所属租户ID, 0 表示该组不区分租户，所有租户能用，kcde中用到的地方都设置为了0',
                          `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
                          `update_by` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
                          `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                          `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改时间',
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group
-- ----------------------------

-- ----------------------------
-- Table structure for group_role
-- ----------------------------
DROP TABLE IF EXISTS `group_role`;
CREATE TABLE `group_role`  (
                               `group_id` bigint(20) NOT NULL COMMENT '分组Id',
                               `role_id` bigint(20) NOT NULL COMMENT '角色Id',
                               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '分组与角色的关联时间',
                               PRIMARY KEY (`group_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群主角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group_role
-- ----------------------------

-- ----------------------------
-- Table structure for group_user
-- ----------------------------
DROP TABLE IF EXISTS `group_user`;
CREATE TABLE `group_user`  (
                               `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                               `group_id` bigint(20) NOT NULL COMMENT '角色ID',
                               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                               PRIMARY KEY (`user_id`, `group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群主用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group_user
-- ----------------------------

-- ----------------------------
-- Table structure for ip_config
-- ----------------------------
DROP TABLE IF EXISTS `ip_config`;
CREATE TABLE `ip_config`  (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT,
                              `user_id` bigint(20) NOT NULL COMMENT '租户id',
                              `user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '租户名称',
                              `black_list` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '黑名单',
                              `prod_white_list` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生产白名单',
                              `test_white_list` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '测试白名单',
                              `status` enum('NORMAL','DELETED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'NORMAL' COMMENT '数据状态，做逻辑删除时使用',
                              `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                              `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ip_config
-- ----------------------------

-- ----------------------------
-- Table structure for permission_group
-- ----------------------------
DROP TABLE IF EXISTS `permission_group`;
CREATE TABLE `permission_group`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                     `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限分组名称',
                                     `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                     `readonly` tinyint(1) NULL DEFAULT 1 COMMENT '是否只读',
                                     `status` enum('NORMAL','CLOSE','DELETED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'NORMAL' COMMENT '数据状态，做逻辑删除时使用',
                                     `source` tinyint(1) NULL DEFAULT 1 COMMENT '类型 0 内置 1 自定义',
                                     `category_id` bigint(20) NULL DEFAULT NULL COMMENT '分类id',
                                     `create_by` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                     `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE INDEX `unionNameIndex`(`name`, `category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 98 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permission_group
-- ----------------------------
INSERT INTO `permission_group` VALUES (1662, '总览_读', '测试描述', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1663, '资源管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1664, '基础设施_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1665, '基础设施_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1666, '队列管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1667, '队列管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1668, '命名空间_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1669, '命名空间_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1670, '资源定义_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1671, '资源定义_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1672, '镜像管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1673, '镜像管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1674, '应用管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1675, '应用部署_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1676, '应用部署_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1677, '应用实例_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1678, '应用实例_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1679, '运维中心_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1680, '作业监控_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1681, '限流管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1682, '限流管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1683, '配置变更_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1684, '运营中心_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1685, '利用率分析_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1686, '利用率分析_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1687, '租户中心_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1688, '租户中心_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:08', '2025-04-14 16:48:08');
INSERT INTO `permission_group` VALUES (1689, '租户信息_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1690, '租户用户_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1691, '租户用户_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1692, '租户应用_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1693, '租户应用_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1694, '租户作业监控_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1695, '开发机_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1696, '开发机_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1697, '进入DLS_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1698, '平台管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1699, 'kcde用户管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1700, 'kcde用户管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1701, 'kcde角色管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1702, 'kcde角色管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1703, '权限点_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1704, '权限点_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1705, '系统授权_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1706, '系统授权_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1707, '机器检测_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1708, '机器检测_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1709, '模板管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1710, '模板管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1711, '平台设置_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1712, '平台设置_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1713, '数据湖开发_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1714, '数据管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1715, '库表管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1716, '包资源管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1717, '包资源管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1718, '查询分析_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1719, '查询分析_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1720, 'Spark作业_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1721, 'Spark作业_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1722, 'Flink作业_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1723, 'Flink作业_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1724, 'Spark实例_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1725, 'Spark实例_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1726, 'Flink实例_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1727, 'Flink实例_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1728, '数据集管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1729, '数据集管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1730, '数据清洗制备_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1731, '数据清洗制备_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1732, '任务管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1733, '任务管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1734, '标注任务_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1735, '标注任务_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1736, '标签管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1737, '标签管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1738, '推理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1739, '推理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1740, '评测_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1741, '评测_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1742, '评测执行_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1743, '评测执行_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1744, '微调_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1745, '微调_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1746, '资源_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1747, '资源_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1748, '用户管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1749, '用户管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1750, '角色管理_写', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1751, '角色管理_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1752, '瀚海平台_读', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');
INSERT INTO `permission_group` VALUES (1753, '瀚海平台_管理', '', '0', 'NORMAL', '0', '1', '系统', '2025-04-14 16:48:09', '2025-04-14 16:48:09');

-- ----------------------------
-- Table structure for privilege
-- ----------------------------
DROP TABLE IF EXISTS `privilege`;
CREATE TABLE `privilege`  (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT,
                              `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限编码',
                              `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限名称',
                              `type` bigint(2) NOT NULL COMMENT '权限类型 1 菜单 2 按钮',
                              `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'icon名称',
                              `order` bigint(20) NULL DEFAULT NULL COMMENT '顺序',
                              `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父ID',
                              `url` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限路径',
                              `business` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务类型',
                              `ak` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ak',
                              `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                              PRIMARY KEY (`id`) USING BTREE,
                              UNIQUE INDEX `code`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 239 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of privilege
-- ----------------------------
INSERT INTO `privilege` VALUES (2498, 'KCDE_Overview', '总览', '1', 'iconfont  icon-zonglan', '2498', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2499, 'KCDE_Overview_JCSS', '基础设施', '1', '', '2499', '2498', '#/main/infrastructure', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2500, 'KCDE_ZYGL', '资源管理', '1', 'iconfont  icon-jichusheshiyuziyuanguanli', '2500', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2501, 'KCDE_ZYGL_JCSS', '基础设施管理', '1', '', '2501', '2500', '#/main/infrastructureManage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2502, 'KCDE_ZYGL_JCSS_RW', '存储资源新增、编辑、删除', '2', '', '2502', '2501', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2503, 'KCDE_ZYGL_DLGL', '队列管理', '1', '', '2503', '2500', '#/main/resourceManagement', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2504, 'KCDE_ZYGL_DLGL_RW', '资源池/资源组新增、编辑、删除', '2', '', '2504', '2503', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2505, 'KCDE_ZYGL_MMKJ', '命名空间', '1', '', '2505', '2500', '#/main/namespaceManage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2506, 'KCDE_ZYGL_MMKJ_RW', '命名空间新增、编辑、删除、克隆', '2', '', '2506', '2505', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2507, 'KCDE_ZYGL_ZYDY', '资源定义', '1', '', '2507', '2500', '#/main/resourceDefinition', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2508, 'KCDE_ZYGL_ZYDY_RW', '资源定义新增、编辑、删除', '2', '', '2508', '2507', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2509, 'KCDE_ZYGL_JXGL', '镜像管理', '1', '', '2509', '2500', '#/main/projectManage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2510, 'KCDE_ZYGL_JXGL_RW', '镜像管理', '1', '', '2510', '2509', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2511, 'KCDE_YYGL', '应用管理', '1', 'iconfont  icon-fuwuguanli', '2511', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2512, 'KCDE_YYGL_YYBS', '应用部署', '1', '', '2512', '2511', '#/main/serviceOverview', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2513, 'KCDE_YYGL_YYBS_Deploy', '部署', '2', '', '2513', '2512', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2514, 'KCDE_YYGL_YYSL', '应用实例', '1', '', '2514', '2511', '#/main/deployManage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2515, 'KCDE_YYGL_YYSL_Details', '实例详情', '2', '', '2515', '2514', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2516, 'KCDE_YWZX', '运维中心', '1', 'iconfont icon-yunweizhongxin', '2516', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2517, 'KCDE_YWZX_ZYGL', '作业监控', '1', '', '2517', '2516', '#/main/jobmonitoring', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2518, 'KCDE_YWZX_XLGL', '限流管理', '1', '', '2518', '2516', '#/main/currentLimitManage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2519, 'KCDE_YWZX_XLGL_RW', '批量修改', '2', '', '2519', '2518', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2520, 'KCDE_YWZX_PZBG', '配置变更', '1', '', '2520', '2516', '#/main/configChange', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2521, 'KCDE_YYZX', '运营中心', '1', 'iconfont  icon-chaxunfenxi', '2521', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2522, 'KCDE_YYZX_LYLFX', '利用率分析', '1', '', '2522', '2521', '#/main/utilizationAnalysisPage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2523, 'KCDE_YYZX_LYLFX_RW', '导出结果', '2', '', '2523', '2522', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2524, 'KCDE_ZHZX', '租户中心', '1', 'bigdata dataicon-zuhuzhongxin', '2524', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2525, 'KCDE_ZHZX_ZHGL', '租户管理', '1', '', '2525', '2524', '#/main/tenantManage', NULL, 'kcde-server', '2025-04-14 16:48:08');
INSERT INTO `privilege` VALUES (2526, 'KCDE_ZHZX_ZHGL_RW', '租户新增、资源池、路径、编辑、删除', '2', '', '2526', '2525', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2527, 'KCDE_ZHXX', '租户信息', '1', '', '2527', '0', '#/tenant/tenantInfor', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2528, 'KCDE_ZHYH', '租户用户', '1', '', '2528', '0', '#/tenant/tenantUser', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2529, 'KCDE_ZHYH_RW', '租户用户添加、移除、重置密码', '2', '', '2529', '2528', '', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2530, 'KCDE_ZHYY', '租户应用', '1', '', '2530', '0', '#/tenant/tenantApp', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2531, 'KCDE_ZHYY_RW', '应用部署、以及进入详情后的所有写操作', '2', '', '2531', '2530', '', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2532, 'KCDE_ZHZYJK', '租户作业监控', '1', '', '2532', '0', '#/tenant/tenantMonitor', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2533, 'KCDE_KFJ', '开发机', '1', '', '2533', '0', '#/tenant/developMachine', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2534, 'KCDE_KFJ_RW', '开发机新建、回收站、启动、编辑、克隆、IDE、终端、删除、保存镜像、', '2', '', '2534', '2533', '', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2535, 'KCDE_DLS', '进入DLS', '1', '', '2535', '0', '/dlstudio', NULL, 'kcde-tenant', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2536, 'KCDE_PTGL', '平台管理', '1', 'iconfont  icon-pingtaiguanli', '2536', '0', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2537, 'KCDE_PTGL_User', '用户管理', '1', '', '2537', '2536', '#/main/userManager', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2538, 'KCDE_PTGL_User_RW', '用户新增、编辑、删除、重置密码', '2', '', '2538', '2537', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2539, 'KCDE_PTGL_Role', '角色管理', '1', '', '2539', '2536', '#/main/roleManager', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2540, 'KCDE_PTGL_Role_RW', '角色编辑、添加用户', '2', '', '2540', '2539', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2541, 'KCDE_PTGL_Authority', '权限点', '1', '', '2541', '2536', '#/main/jurisdictionManager', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2542, 'KCDE_PTGL_Authority_RW', '权限点', '1', '', '2542', '2541', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2543, 'KCDE_PTGL_License', '系统授权', '1', '', '2543', '2536', '#/main/systemAuthorization', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2544, 'KCDE_PTGL_License_RW', '导入License', '2', '', '2544', '2543', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2545, 'KCDE_PTGL_JQJC', '机器检测', '1', '', '2545', '2536', '#/main/machineCheck', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2546, 'KCDE_PTGL_JQJC_RW', '机器检测', '2', '', '2546', '2536', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2547, 'KCDE_PTGL_TemplateManage', '模板管理', '1', '', '2547', '2536', '#/main/applyCommonTemplate', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2548, 'KCDE_PTGL_TemplateManage_RW', '模板编辑', '2', '', '2548', '2547', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2549, 'KCDE_PTGL_PTSZ', '平台设置', '1', '', '2549', '2536', '#/main/platformSetting', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2550, 'KCDE_PTGL_PTSZ_RW', 'Logo和平台名称的编辑', '1', '', '2550', '2549', '', NULL, 'kcde-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2551, 'DLS_SJGL', '数据管理', '1', 'ksfont  ksicon-qy-shujuguanli', '2551', '0', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2552, 'DLS_SJGL_Schema', '库表管理', '1', '', '2552', '2551', '#/main/database', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2553, 'DLS_SJGL_JarSource', '包资源管理', '1', '', '2553', '2551', '#/main/jar', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2554, 'DLS_SJGL_JarSource_RW', '资源包导入、编辑、删除', '2', '', '2554', '2553', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2555, 'DLS_CXFX', 'SQL查询分析', '1', 'ksfont  ksicon-shujuku', '2555', '0', '#/main/sql', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2556, 'DLS_CXFX_RW', '新建、执行、新增、保存、格式化、执行历史、编辑、删除', '2', '', '2556', '2555', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2557, 'DLS_ZYGL', '作业管理', '1', 'ksfont  ksicon-zuoyemoban1', '2557', '0', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2558, 'DLS_ZYGL_Spark', 'Spark作业', '1', '', '2558', '2557', '#/main/spark', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2559, 'DLS_ZYGL_Spark_RW', 'Spark作业新建、启动、调度配置、编辑、删除、复制', '2', '', '2559', '2558', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2560, 'DLS_ZYGL_Flink', 'Flink作业', '1', '', '2560', '2557', '#/main/flink', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2561, 'DLS_ZYGL_Flink_RW', 'Flink作业新建、启动、编辑、删除、复制', '2', '', '2561', '2560', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2562, 'DLS_SLGL', '作业实例管理', '1', 'ksfont ksicon-shiliguanli', '2562', '0', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2563, 'DLS_SLGL_Spark', 'Spark实例', '1', '', '2563', '2562', '#/main/sparkInstances', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2564, 'DLS_SLGL_Spark_RW', 'Spark实例删除、启动、终止', '2', '', '2564', '2563', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2565, 'DLS_SLGL_Flink', 'Flink实例', '1', '', '2565', '2562', '#/main/flinkInstances', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2566, 'DLS_SLGL_Flink_RW', 'Flink实例删除、启动、终止', '2', '', '2566', '2565', '', NULL, 'dls-server', '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2567, 'MGMT_DATASET', '数据集管理_写', '2', '', '2567', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2568, 'VIEW_DATASET', '数据集管理_读', '2', '', '2568', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2569, 'MGMT_DATA', '数据清洗制备_写', '2', '', '2569', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2570, 'VIEW_DATA', '数据清洗制备_读', '2', '', '2570', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2571, 'MGMT_DA', '任务管理_写', '2', '', '2571', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2572, 'VIEW_DA', '任务管理_读', '2', '', '2572', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2573, 'EXEC_DA_TASK', '标注任务_写', '2', '', '2573', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2574, 'VIEW_DA_TASK', '标注任务_读', '2', '', '2574', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2575, 'MGMT_DA_LABEL', '标签管理_写', '2', '', '2575', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2576, 'VIEW_DA_LABEL', '标签管理_读', '2', '', '2576', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2577, 'MGMT_INFER', '推理_写', '2', '', '2577', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2578, 'VIEW_INFER', '推理_读', '2', '', '2578', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2579, 'MGMT_EVAL', '评测_写', '2', '', '2579', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2580, 'VIEW_EVAL', '评测_读', '2', '', '2580', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2581, 'MGMT_EVAL_TASK', '评测执行_写', '2', '', '2581', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2582, 'VIEW_EVAL_TASK', '评测执行_读', '2', '', '2582', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2583, 'MGMT_SFT', '微调_写', '2', '', '2583', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2584, 'VIEW_SFT', '微调_读', '2', '', '2584', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2585, 'MGMT_RESOURCE', '资源_写', '2', '', '2585', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2586, 'VIEW_RESOURCE', '资源_读', '2', '', '2586', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2587, 'MGMT_USER', '用户管理_写', '2', '', '2587', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2588, 'VIEW_USER', '用户管理_读', '2', '', '2588', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2589, 'MGMT_ROLE', '角色管理_写', '2', '', '2589', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2590, 'VIEW_ROLE', '角色管理_读', '2', '', '2590', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2591, 'VIEW_PAGE', '瀚海平台_读', '1', '', '2591', '0', '', NULL, NULL, '2025-04-14 16:48:09');
INSERT INTO `privilege` VALUES (2592, 'ADMIN', '瀚海平台_管理', '1', '', '2592', '0', '', NULL, NULL, '2025-04-14 16:48:09');

-- ----------------------------
-- Table structure for privilege_permission_group
-- ----------------------------
DROP TABLE IF EXISTS `privilege_permission_group`;
CREATE TABLE `privilege_permission_group`  (
                                               `privilege_id` bigint(20) NOT NULL COMMENT '权限id',
                                               `permission_group_id` bigint(20) NOT NULL COMMENT '权限分组id',
                                               `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                               INDEX `privilege_id_permission_group_id`(`privilege_id`, `permission_group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限组与权限点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of privilege_permission_group
-- ----------------------------
INSERT INTO `privilege_permission_group` VALUES (2498, '1662', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2499, '1662', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2500, '1663', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2501, '1664', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2502, '1665', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2503, '1666', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2504, '1667', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2505, '1668', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2506, '1669', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2507, '1670', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2508, '1671', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2509, '1672', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2510, '1673', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2511, '1674', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2512, '1675', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2513, '1676', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2514, '1677', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2515, '1678', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2516, '1679', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2517, '1680', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2518, '1681', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2519, '1682', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2520, '1683', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2521, '1684', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2522, '1685', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2523, '1686', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2524, '1687', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2525, '1687', '2025-04-14 16:48:08');
INSERT INTO `privilege_permission_group` VALUES (2526, '1688', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2527, '1689', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2528, '1690', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2529, '1691', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2530, '1692', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2531, '1693', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2532, '1694', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2533, '1695', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2534, '1696', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2535, '1697', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2536, '1698', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2537, '1699', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2538, '1700', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2539, '1701', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2540, '1702', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2541, '1703', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2542, '1704', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2543, '1705', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2544, '1706', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2545, '1707', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2546, '1708', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2547, '1709', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2548, '1710', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2545, '1707', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2549, '1711', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2550, '1712', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2535, '1713', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2551, '1714', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2552, '1715', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2553, '1716', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2554, '1717', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2555, '1718', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2556, '1719', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2557, '1720', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2558, '1720', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2559, '1721', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2560, '1722', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2561, '1723', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2562, '1724', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2563, '1724', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2564, '1725', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2565, '1726', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2566, '1727', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2567, '1728', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2568, '1729', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2569, '1730', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2570, '1731', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2571, '1732', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2572, '1733', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2573, '1734', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2574, '1735', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2575, '1736', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2576, '1737', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2577, '1738', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2578, '1739', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2579, '1740', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2580, '1741', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2581, '1742', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2582, '1743', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2583, '1744', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2584, '1745', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2585, '1746', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2586, '1747', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2587, '1748', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2588, '1749', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2589, '1750', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2590, '1751', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2591, '1752', '2025-04-14 16:48:09');
INSERT INTO `privilege_permission_group` VALUES (2592, '1753', '2025-04-14 16:48:09');

-- ----------------------------
-- Table structure for privilege_props
-- ----------------------------
DROP TABLE IF EXISTS `privilege_props`;
CREATE TABLE `privilege_props`  (
                                    `privilege_id` bigint(20) NOT NULL COMMENT '权限ID',
                                    `prop_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限属性KEY',
                                    `prop_value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限属性值',
                                    INDEX `privilege_id_key_value`(`privilege_id`, `prop_key`, `prop_value`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '权限配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of privilege_props
-- ----------------------------

-- ----------------------------
-- Table structure for project
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project`  (
                            `id` bigint(20) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目名称',
                            `chinese_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目中文名称',
                            `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目中文名称',
                            `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目备注',
                            `owner_id` bigint(20) NOT NULL COMMENT '创建人ID',
                            `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
                            `status` enum('ENABLED','DISABLED','DELETED','ARCHIVED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                            `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                            `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                            `required_approve` tinyint(1) NULL DEFAULT 1 COMMENT '作业是否需要审批，默认需要审批',
                            `export_package_required_approve` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目中文名称',
                            `project_depend` tinyint(1) NULL DEFAULT 0 COMMENT '允许跨项目依赖，默认需要审批 1允许 0 不允许  ',
                            `space_id` int(20) NULL DEFAULT 0,
                            `project_depend_status` tinyint(1) NULL DEFAULT 1,
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of project
-- ----------------------------

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                         `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色编码',
                         `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色中文名',
                         `type` bigint(2) NOT NULL COMMENT '角色级别 1 平台级别 2 项目级别',
                         `role` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色标签',
                         `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `tag_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色标签',
                         `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                         `create_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                         `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后更新时间',
                         `default` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '0 非默认 1默认 ',
                         `source` enum('bigdata','ope','ops') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'bigdata',
                         PRIMARY KEY (`id`) USING BTREE,
                         UNIQUE INDEX `role_code_index`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, 'admin', '管理员', 1, NULL, '可访问所有页面及所有功能1', NULL, '2022-10-12 11:27:45', '系统', '2022-10-13 17:40:13', '1', 'bigdata');

-- ----------------------------
-- Table structure for role_permission_group
-- ----------------------------
DROP TABLE IF EXISTS `role_permission_group`;
CREATE TABLE `role_permission_group`  (
                                          `role_id` bigint(20) NOT NULL COMMENT '角色id',
                                          `role_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                          `permission_group_id` bigint(20) NOT NULL COMMENT '权限分组id',
                                          `permission_group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限分组名称',
                                          `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                          INDEX `role_id_permission_group_id`(`role_id`, `permission_group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色权限组关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permission_group
-- ----------------------------
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1662', '总览_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1663', '资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1664', '基础设施_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1665', '基础设施_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1666', '队列管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1667', '队列管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1668', '命名空间_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1669', '命名空间_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1670', '资源定义_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1671', '资源定义_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1672', '镜像管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1673', '镜像管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1674', '应用管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1675', '应用部署_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1676', '应用部署_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1677', '应用实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1678', '应用实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1679', '运维中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1680', '作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1681', '限流管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1682', '限流管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1683', '配置变更_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1684', '运营中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1685', '利用率分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1686', '利用率分析_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1698', '平台管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1699', 'kcde用户管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1700', 'kcde用户管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1701', 'kcde角色管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1702', 'kcde角色管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1703', '权限点_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1704', '权限点_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1705', '系统授权_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1706', '系统授权_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1709', '模板管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1710', '模板管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1711', '平台设置_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1712', '平台设置_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1707', '机器检测_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1707', '机器检测_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1708', '机器检测_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1687', '租户中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1688', '租户中心_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1689', '租户信息_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1690', '租户用户_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1691', '租户用户_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1692', '租户应用_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1693', '租户应用_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1695', '开发机_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1696', '开发机_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1697', '进入DLS_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1713', '数据湖开发_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1714', '数据管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1715', '库表管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1716', '包资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1717', '包资源管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1718', '查询分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1719', '查询分析_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1720', 'Spark作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1721', 'Spark作业_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1722', 'Flink作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1723', 'Flink作业_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1724', 'Spark实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1725', 'Spark实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1726', 'Flink实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1727', 'Flink实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (1, '管理员', '1694', '租户作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1662', '总览_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1663', '资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1664', '基础设施_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1665', '基础设施_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1666', '队列管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1667', '队列管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1668', '命名空间_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1669', '命名空间_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1670', '资源定义_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1671', '资源定义_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1672', '镜像管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1673', '镜像管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1674', '应用管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1675', '应用部署_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1676', '应用部署_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1677', '应用实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1678', '应用实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1679', '运维中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1680', '作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1681', '限流管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1682', '限流管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1683', '配置变更_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1684', '运营中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1685', '利用率分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1686', '利用率分析_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1698', '平台管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1703', '权限点_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1704', '权限点_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1705', '系统授权_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1706', '系统授权_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1709', '模板管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1710', '模板管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1711', '平台设置_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1712', '平台设置_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1707', '机器检测_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1687', '租户中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1689', '租户信息_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1692', '租户应用_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (2, '运维', '1693', '租户应用_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1687', '租户中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1689', '租户信息_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1697', '进入DLS_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1695', '开发机_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1696', '开发机_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1713', '数据湖开发_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1714', '数据管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1715', '库表管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1716', '包资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1717', '包资源管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1718', '查询分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1719', '查询分析_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1720', 'Spark作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1721', 'Spark作业_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1722', 'Flink作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1723', 'Flink作业_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1724', 'Spark实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1725', 'Spark实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1726', 'Flink实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1727', 'Flink实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (3, '开发', '1694', '租户作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1662', '总览_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1663', '资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1664', '基础设施_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1666', '队列管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1668', '命名空间_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1670', '资源定义_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1672', '镜像管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1674', '应用管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1675', '应用部署_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1677', '应用实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1679', '运维中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1680', '作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1681', '限流管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1683', '配置变更_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1684', '运营中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1685', '利用率分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1698', '平台管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1703', '权限点_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1699', 'kcde用户管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1701', 'kcde角色管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1705', '系统授权_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1709', '模板管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1711', '平台设置_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '0', '机器检测_读，租户中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1689', '租户信息_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1690', '租户用户_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1692', '租户应用_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1695', '开发机_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1697', '进入DLS_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1713', '数据湖开发_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1714', '数据管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1715', '库表管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1716', '包资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1718', '查询分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1720', 'Spark作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1722', 'Flink作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1724', 'Spark实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1726', 'Flink实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (4, '访客', '1694', '租户作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1687', '租户中心_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1688', '租户中心_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1689', '租户信息_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1690', '租户用户_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1691', '租户用户_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1692', '租户应用_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1693', '租户应用_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1695', '开发机_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1696', '开发机_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1697', '进入DLS_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1713', '数据湖开发_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1714', '数据管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1715', '库表管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1716', '包资源管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1717', '包资源管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1718', '查询分析_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1719', '查询分析_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1720', 'Spark作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1721', 'Spark作业_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1722', 'Flink作业_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1723', 'Flink作业_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1724', 'Spark实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1725', 'Spark实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1726', 'Flink实例_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1727', 'Flink实例_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (5, '租户管理员', '1694', '租户作业监控_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1728', '数据集管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1729', '数据集管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1730', '数据清洗制备_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1731', '数据清洗制备_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1732', '任务管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1733', '任务管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1734', '标注任务_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1735', '标注任务_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1736', '标签管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1737', '标签管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1738', '推理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1739', '推理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1740', '评测_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1741', '评测_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1742', '评测执行_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1743', '评测执行_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1744', '微调_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1745', '微调_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1746', '资源_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1747', '资源_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1748', '用户管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1749', '用户管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1750', '角色管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1751', '角色管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1752', '瀚海平台_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (24, '瀚海管理员', '1753', '瀚海平台_管理', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1746', '资源_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1747', '资源_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1748', '用户管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1749', '用户管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1750', '角色管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1751', '角色管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (25, '瀚海系统管理员', '1752', '瀚海平台_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1728', '数据集管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1729', '数据集管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1730', '数据清洗制备_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1731', '数据清洗制备_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1732', '任务管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1733', '任务管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1734', '标注任务_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1735', '标注任务_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1736', '标签管理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1737', '标签管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1738', '推理_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1739', '推理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1740', '评测_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1741', '评测_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1742', '评测执行_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1743', '评测执行_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1744', '微调_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1745', '微调_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (26, '模型管理员', '1752', '瀚海平台_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (27, '数据标注员', '1734', '标注任务_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (27, '数据标注员', '1735', '标注任务_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (27, '数据标注员', '1739', '推理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (27, '数据标注员', '1747', '资源_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (27, '数据标注员', '1752', '瀚海平台_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (28, '模型评测员', '1742', '评测执行_写', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (28, '模型评测员', '1743', '评测执行_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (28, '模型评测员', '1739', '推理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (28, '模型评测员', '1747', '资源_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (28, '模型评测员', '1752', '瀚海平台_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1729', '数据集管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1731', '数据清洗制备_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1733', '任务管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1735', '标注任务_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1737', '标签管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1739', '推理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1741', '评测_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1743', '评测执行_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1745', '微调_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1747', '资源_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1749', '用户管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1751', '角色管理_读', '2025-04-14 16:48:09');
INSERT INTO `role_permission_group` VALUES (29, '瀚海访客', '1752', '瀚海平台_读', '2025-04-14 16:48:09');
-- ----------------------------
-- Table structure for role_privilege
-- ----------------------------
DROP TABLE IF EXISTS `role_privilege`;
CREATE TABLE `role_privilege`  (
                                   `role_id` bigint(20) NOT NULL COMMENT '角色编号',
                                   `privilege_id` bigint(255) NOT NULL COMMENT '权限编号',
                                   `privilege_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限编码',
                                   `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                   INDEX `idx_role_id`(`role_id`) USING BTREE,
                                   INDEX `idx_privilege_id`(`privilege_id`) USING BTREE,
                                   INDEX `idx_privilege_code`(`privilege_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_privilege
-- ----------------------------

-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant` (
                          `id` bigint(20) NOT NULL,
                          `name` varchar(255) NOT NULL COMMENT '租户名',
                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                          `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
                          `status` enum('NORMAL', 'DELETING', 'DELETED') DEFAULT NULL COMMENT '数据状态，做逻辑删除时使用',
                          `created_by` bigint NOT NULL COMMENT '租户创建的用户id',
                          `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                          `description` varchar(512) DEFAULT NULL COMMENT '说明',
                          PRIMARY KEY `tenant_id_index` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户';

-- ----------------------------
-- Records of tenant
-- ----------------------------
INSERT INTO `tenant` VALUES (0, 'default', '2019-08-28 19:53:19', '2019-08-28 19:53:19', 'NORMAL', 1, '系统', '系统');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                         `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户名',
                         `source` enum('LOCAL','LDAP','PASSPORT','OIDC') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
    -- `tenant_id` bigint(20) NOT NULL COMMENT '所属租户ID',
                         `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                         `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后更新时间',
                         `status` enum('NORMAL','DELETED') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据状态，做逻辑删除时使用',
                         `oidc_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '统一登录外部系统ID',
                         PRIMARY KEY (`id`) USING BTREE,
                         INDEX `idx_oidc_id`(`oidc_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '租户与子账户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', 'LOCAL', '2019-08-28 19:53:19', '2019-08-28 19:53:19', 'NORMAL', NULL);

-- ----------------------------
-- Table structure for user_props
-- ----------------------------
DROP TABLE IF EXISTS `user_props`;
CREATE TABLE `user_props`  (
                               `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                               `prop_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户属性KEY',
                               `prop_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                               PRIMARY KEY (`user_id`, `prop_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户属性' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_props
-- ----------------------------
INSERT INTO `user_props` VALUES (1, 'alias', 'uat_test');
INSERT INTO `user_props` VALUES (1, 'iam_id', '*********');
INSERT INTO `user_props` VALUES (1, 'passport_id', '*********');
INSERT INTO `user_props` VALUES (1, 'password', '57ed0c1a735a3d66f3fd0faab92421cc');
INSERT INTO `user_props` VALUES (1, 'phone', '18310902030');
INSERT INTO `user_props` VALUES (1, 'secret_level', '5');
INSERT INTO `user_props` VALUES (1, 'userAk', '66ee3fbcac9d820575e2d713690ad17cb01df3ed39e3255d5ef0ff6fa53ef5ac');
INSERT INTO `user_props` VALUES (1, 'userAk.create-time', '2020-08-27 10:46:16');
INSERT INTO `user_props` VALUES (1, 'userAkSk-status', 'NORMAL');
INSERT INTO `user_props` VALUES (1, 'userSk', 'eb92659bdbbedb56e37e2bd263cdfe8c61dfd873b0a48d033d229a13ca6b5865');

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
                              `user_id` bigint(20) NOT NULL COMMENT '用户Id',
                              `role_id` bigint(20) NOT NULL COMMENT '角色Id',
                              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '用户与角色的关联时间',
                              PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_role
-- ----------------------------
INSERT INTO `user_role` VALUES (1, 1, '2019-08-28 19:53:19');

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_tenant`;
CREATE TABLE `user_tenant` (
                               `user_id` bigint(20) NOT NULL COMMENT '用户Id',
                               `tenant_id` bigint(20) NOT NULL COMMENT '租户Id',
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用户与租户的绑定时间',
                               PRIMARY KEY (`user_id`, `tenant_id`),
                               INDEX `user_id_index` (`tenant_id`) USING BTREE,
                               INDEX `tenant_id_index` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户';

-- ----------------------------
-- Records of user_tenant
-- ----------------------------
INSERT INTO `user_tenant` VALUES (1, 1, '2019-08-28 19:53:19');

SET FOREIGN_KEY_CHECKS = 1;


-- ----------------------------
-- 1.8.4 update
-- ----------------------------
ALTER TABLE `user` MODIFY COLUMN `source` enum('LOCAL','LDAP','PASSPORT','OIDC','CAS');

CREATE TABLE IF NOT EXISTS oauth2_authorization_consent (
    registered_client_id varchar(100) NOT NULL,
    principal_name varchar(200) NOT NULL,
    authorities varchar(1000) NOT NULL,
    PRIMARY KEY (registered_client_id, principal_name)
    );
CREATE TABLE IF NOT EXISTS oauth2_authorization (
    id varchar(100) NOT NULL,
    registered_client_id varchar(100) NOT NULL,
    principal_name varchar(200) NOT NULL,
    authorization_grant_type varchar(100) NOT NULL,
    authorized_scopes varchar(1000) DEFAULT NULL,
    attributes blob DEFAULT NULL,
    state varchar(500) DEFAULT NULL,
    authorization_code_value blob DEFAULT NULL,
    authorization_code_issued_at timestamp DEFAULT NULL,
    authorization_code_expires_at timestamp DEFAULT NULL,
    authorization_code_metadata blob DEFAULT NULL,
    access_token_value blob DEFAULT NULL,
    access_token_issued_at timestamp DEFAULT NULL,
    access_token_expires_at timestamp DEFAULT NULL,
    access_token_metadata blob DEFAULT NULL,
    access_token_type varchar(100) DEFAULT NULL,
    access_token_scopes varchar(1000) DEFAULT NULL,
    oidc_id_token_value blob DEFAULT NULL,
    oidc_id_token_issued_at timestamp DEFAULT NULL,
    oidc_id_token_expires_at timestamp DEFAULT NULL,
    oidc_id_token_metadata blob DEFAULT NULL,
    oidc_id_token_claims blob DEFAULT NULL,
    refresh_token_value blob DEFAULT NULL,
    refresh_token_issued_at timestamp DEFAULT NULL,
    refresh_token_expires_at timestamp DEFAULT NULL,
    refresh_token_metadata blob DEFAULT NULL,
    user_code_value blob DEFAULT NULL,
    user_code_issued_at timestamp DEFAULT NULL,
    user_code_expires_at timestamp DEFAULT NULL,
    user_code_metadata blob DEFAULT NULL,
    device_code_value blob DEFAULT NULL,
    device_code_issued_at timestamp DEFAULT NULL,
    device_code_expires_at timestamp DEFAULT NULL,
    device_code_metadata blob DEFAULT NULL,
    PRIMARY KEY (id)
    );

CREATE TABLE IF NOT EXISTS oauth2_registered_client (
    id varchar(100) NOT NULL,
    client_id varchar(100) NOT NULL,
    client_id_issued_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    client_secret varchar(200) DEFAULT NULL,
    client_secret_expires_at timestamp DEFAULT NULL,
    client_name varchar(200) NOT NULL,
    client_authentication_methods varchar(1000) NOT NULL,
    authorization_grant_types varchar(1000) NOT NULL,
    redirect_uris varchar(1000) DEFAULT NULL,
    post_logout_redirect_uris varchar(1000) DEFAULT NULL,
    scopes varchar(1000) NOT NULL,
    client_settings varchar(2000) NOT NULL,
    token_settings varchar(2000) NOT NULL,
    PRIMARY KEY (id)
    );

INSERT INTO `oauth2_registered_client`(`id`, `client_id`, `client_id_issued_at`, `client_secret`, `client_secret_expires_at`, `client_name`, `client_authentication_methods`, `authorization_grant_types`, `redirect_uris`, `post_logout_redirect_uris`, `scopes`, `client_settings`, `token_settings`) VALUES ('49c97994-55de-49ad-af3d-b52415cde303', 'registrar-client', now(), '$2a$10$GlWKG.vtsZRLg5c8thH7weNhX2LebDUCiZJUn2Omt/TmAEMUSCp/q', NULL, '49c97994-55de-49ad-af3d-b52415cde303', 'client_secret_basic', 'client_credentials', '', '', 'client.create,client.read', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.client.require-proof-key\":false,\"settings.client.require-authorization-consent\":false}', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":[\"java.time.Duration\",1800.000000000],\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":[\"java.time.Duration\",3600.000000000],\"settings.token.authorization-code-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.device-code-time-to-live\":[\"java.time.Duration\",300.000000000]}');

-- ----------------------------
-- 2.1 update
-- ----------------------------
INSERT INTO `user_props` VALUES (1, 'reset_password', 'false');

ALTER TABLE `privilege`.`role`
    ADD COLUMN `platform` VARCHAR(45) NULL COMMENT '平台名称，KCDE、HH、CAS' AFTER `source`;
ALTER TABLE `privilege`.`user`
    ADD COLUMN `remark` VARCHAR(150) NULL COMMENT '备注，可用于存放一些扩展信息，如部门' AFTER `oidc_id`;
ALTER TABLE `privilege`.`user`
    CHANGE COLUMN `source` `source` ENUM('LOCAL', 'LDAP', 'PASSPORT', 'OIDC', 'CAS', 'TEST', 'HH') NULL DEFAULT NULL ;
REPLACE INTO `privilege`.`oauth2_registered_client`(`id`, `client_id`, `client_id_issued_at`, `client_secret`, `client_secret_expires_at`, `client_name`, `client_authentication_methods`, `authorization_grant_types`, `redirect_uris`, `post_logout_redirect_uris`, `scopes`, `client_settings`, `token_settings`) VALUES ('e52e28d5-b2aa-441c-8ff3-414b7f39ef87', 'kcde-gateway-Client', '2024-10-30 09:14:44', '$2a$10$4cwPLTtf8.kRl3fuBUBsKOLPzYYFHMJMOXjlFGB1Da2UWZU9qbbmy', NULL, 'kcde-gateway-Client', 'client_secret_basic', 'refresh_token,client_credentials,authorization_code', 'http://ec.kcde.kscbigdata.cloud/gateway/login/oauth2/code/kcde-auth-server-client', '', 'openid,profile', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.client.require-proof-key\":false,\"settings.client.require-authorization-consent\":false}', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":[\"java.time.Duration\",3600.000000000],\"settings.token.authorization-code-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.device-code-time-to-live\":[\"java.time.Duration\",300.000000000]}');
REPLACE INTO `privilege`.`oauth2_registered_client`(`id`, `client_id`, `client_id_issued_at`, `client_secret`, `client_secret_expires_at`, `client_name`, `client_authentication_methods`, `authorization_grant_types`, `redirect_uris`, `post_logout_redirect_uris`, `scopes`, `client_settings`, `token_settings`) VALUES( '1faa7a0e-8324-48d6-a5a2-1df24b8d2f57', 'kcde-gateway-Client-duli', '2024-12-17 08:44:01', '$2a$10$uSxUdWKe5dZi/LDzZotSIu867xNnYjwJ1mrLlfiuT/o0k9sITI6z2', NULL, '1faa7a0e-8324-48d6-a5a2-1df24b8d2f57', 'client_secret_basic', 'refresh_token,client_credentials,authorization_code', 'http://ec.kcde.kscbigdata.cloud/gateway/login/oauth2/code/kcde-gateway-Client-duli', 'http://127.0.0.1:8090/authserver/logged-out', 'openid,profile', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.client.require-proof-key\":false,\"settings.client.require-authorization-consent\":false}', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":[\"java.time.Duration\",3600.000000000],\"settings.token.authorization-code-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.device-code-time-to-live\":[\"java.time.Duration\",300.000000000]}' );

-- 翰海管理员初始化， 用户角色固定为6，所以这里写死，用户id可以动态生成
SET @username = 'hanhai_admin';
-- 初始化用户
REPLACE INTO `privilege`.`user` (`name`, `source`, `create_time`, `update_time`, `status`, `oidc_id`, `remark`)
SELECT @username, 'LOCAL', NOW(), NOW(), 'NORMAL', NULL, ''
    WHERE NOT EXISTS (SELECT 1 FROM `privilege`.`user` WHERE `name` = @username and status='NORMAL');

-- 初始化用户属性
-- 先获取用户ID到变量（假设已存在相关用户记录）
SET @user_id = (SELECT id FROM `privilege`.`user` WHERE `name` = @username and status='NORMAL');

-- 条件插入属性记录
REPLACE INTO `privilege`.`user_props` (user_id, prop_key, prop_value)
SELECT * FROM (
                  SELECT @user_id, 'alias', @username
                  UNION ALL
                  SELECT @user_id, 'deadline', 'LONGTERM'
                  UNION ALL
                  SELECT @user_id, 'deadline-valid-day', null
                  UNION ALL
                  SELECT @user_id, 'password', 'f0ebb4ddc7a5167888873507ca11362f'
                  UNION ALL
                  SELECT @user_id, 'password-generate-time', '2025-01-02 21:03:42'
                  UNION ALL
                  SELECT @user_id, 'reset_password', 'false'
                  UNION ALL
                  SELECT @user_id, 'secret_level', '5'
              ) AS new_props
WHERE NOT EXISTS (
    SELECT 1
    FROM `privilege`.`user_props`
    WHERE user_id = @user_id
      AND prop_key IN ('alias','deadline','password') -- 关键属性存在性检查
);
-- 初始化瀚海管理员的角色，由于瀚海管理员id固定为6所以此处写死为6
REPLACE INTO `privilege`.`user_role` (`user_id`, `role_id`, `create_time`)
SELECT (SELECT id FROM `privilege`.`user` WHERE `name` = @username),
       6,
       NOW()
    WHERE NOT EXISTS (
    SELECT 1
    FROM `privilege`.`user_role`
    WHERE `user_id` = (SELECT id FROM `privilege`.`user` WHERE `name` = @username)
      AND `role_id` = 6  -- 同步修改条件判断
);


-- RSA Keys table
CREATE TABLE IF NOT EXISTS rsa_keys (
                                        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                        user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    name VARCHAR(255) NOT NULL COMMENT '密钥名称',
    type ENUM('用户上传', '平台生成') NOT NULL COMMENT '密钥类型',
    public_key VARCHAR(2048) NOT NULL COMMENT 'Base64编码的公钥',
    private_key VARCHAR(4096) COMMENT 'Base64编码的加密私钥，用户上传时可为空',
    key_size INT NOT NULL DEFAULT 2048 COMMENT '密钥长度（位）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '最后更新时间',
    UNIQUE KEY uk_name (name) COMMENT '名称唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RSA密钥管理表';

-- person access token
CREATE TABLE IF NOT EXISTS person_access_token (
                                                   id BIGINT AUTO_INCREMENT COMMENT '主键ID',
                                                   user_id BIGINT NOT NULL COMMENT '用户ID',
                                                   token_value VARCHAR(2048) NOT NULL COMMENT '令牌值',
    lifetime_seconds BIGINT NOT NULL COMMENT '有效期（秒）',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人访问令牌表';