use privilege;

ALTER TABLE `privilege`.`role`
    ADD COLUMN `platform` VARCHAR(45) NULL COMMENT '平台名称，KCDE、HH、CAS' AFTER `source`;
ALTER TABLE `privilege`.`user`
    ADD COLUMN `remark` VARCHAR(150) NULL COMMENT '备注，可用于存放一些扩展信息，如部门' AFTER `oidc_id`;
ALTER TABLE `privilege`.`user`
    CHANGE COLUMN `source` `source` ENUM('LOCAL', 'LDAP', 'PASSPORT', 'OIDC', 'CAS', 'TEST', 'HH') NULL DEFAULT NULL ;
REPLACE INTO `privilege`.`oauth2_registered_client`(`id`, `client_id`, `client_id_issued_at`, `client_secret`, `client_secret_expires_at`, `client_name`, `client_authentication_methods`, `authorization_grant_types`, `redirect_uris`, `post_logout_redirect_uris`, `scopes`, `client_settings`, `token_settings`) VALUES ('e52e28d5-b2aa-441c-8ff3-414b7f39ef87', 'kcde-gateway-Client', '2024-10-30 09:14:44', '$2a$10$4cwPLTtf8.kRl3fuBUBsKOLPzYYFHMJMOXjlFGB1Da2UWZU9qbbmy', NULL, 'kcde-gateway-Client', 'client_secret_basic', 'refresh_token,client_credentials,authorization_code', 'http://ec.kcde.ksyun.com/gateway/login/oauth2/code/kcde-auth-server-client', '', 'openid,profile', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.client.require-proof-key\":false,\"settings.client.require-authorization-consent\":false}', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":[\"java.time.Duration\",3600.000000000],\"settings.token.authorization-code-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.device-code-time-to-live\":[\"java.time.Duration\",300.000000000]}');
REPLACE INTO `privilege`.`oauth2_registered_client`(`id`, `client_id`, `client_id_issued_at`, `client_secret`, `client_secret_expires_at`, `client_name`, `client_authentication_methods`, `authorization_grant_types`, `redirect_uris`, `post_logout_redirect_uris`, `scopes`, `client_settings`, `token_settings`) VALUES( '1faa7a0e-8324-48d6-a5a2-1df24b8d2f57', 'kcde-gateway-Client-duli', '2024-12-17 08:44:01', '$2a$10$uSxUdWKe5dZi/LDzZotSIu867xNnYjwJ1mrLlfiuT/o0k9sITI6z2', NULL, '1faa7a0e-8324-48d6-a5a2-1df24b8d2f57', 'client_secret_basic', 'refresh_token,client_credentials,authorization_code', 'http://ec.kcde.kscbigdata.cloud/gateway/login/oauth2/code/kcde-gateway-Client-duli', 'http://127.0.0.1:8090/authserver/logged-out', 'openid,profile', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.client.require-proof-key\":false,\"settings.client.require-authorization-consent\":false}', '{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":[\"java.time.Duration\",3600.000000000],\"settings.token.authorization-code-time-to-live\":[\"java.time.Duration\",300.000000000],\"settings.token.device-code-time-to-live\":[\"java.time.Duration\",300.000000000]}' );

-- 翰海管理员初始化， 用户角色固定为6，所以这里写死，用户id可以动态生成
SET @username = 'hanhai_admin';
-- 初始化用户
REPLACE INTO `privilege`.`user` (`name`, `source`, `create_time`, `update_time`, `status`, `oidc_id`, `remark`)
SELECT @username, 'LOCAL', NOW(), NOW(), 'NORMAL', NULL, ''
    WHERE NOT EXISTS (SELECT 1 FROM `privilege`.`user` WHERE `name` = @username and status='NORMAL');

-- 初始化用户属性
-- 先获取用户ID到变量（假设已存在相关用户记录）
SET @user_id = (SELECT id FROM `privilege`.`user` WHERE `name` = @username and status='NORMAL');

-- 条件插入属性记录
REPLACE INTO `privilege`.`user_props` (user_id, prop_key, prop_value)
SELECT * FROM (
                  SELECT @user_id, 'alias', @username
                  UNION ALL
                  SELECT @user_id, 'deadline', 'LONGTERM'
                  UNION ALL
                  SELECT @user_id, 'deadline-valid-day', null
                  UNION ALL
                  SELECT @user_id, 'password', 'f0ebb4ddc7a5167888873507ca11362f'
                  UNION ALL
                  SELECT @user_id, 'password-generate-time', '2025-01-02 21:03:42'
                  UNION ALL
                  SELECT @user_id, 'reset_password', 'false'
                  UNION ALL
                  SELECT @user_id, 'secret_level', '5'
              ) AS new_props
WHERE NOT EXISTS (
    SELECT 1
    FROM `privilege`.`user_props`
    WHERE user_id = @user_id
      AND prop_key IN ('alias','deadline','password') -- 关键属性存在性检查
);
-- 初始化瀚海管理员的角色，由于瀚海管理员id固定为6所以此处写死为6
REPLACE INTO `privilege`.`user_role` (`user_id`, `role_id`, `create_time`)
SELECT (SELECT id FROM `privilege`.`user` WHERE `name` = @username),
       6,
       NOW()
    WHERE NOT EXISTS (
    SELECT 1
    FROM `privilege`.`user_role`
    WHERE `user_id` = (SELECT id FROM `privilege`.`user` WHERE `name` = @username)
      AND `role_id` = 6  -- 同步修改条件判断
);


-- RSA Keys table
CREATE TABLE IF NOT EXISTS rsa_keys (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    name VARCHAR(255) NOT NULL COMMENT '密钥名称',
    type ENUM('用户上传', '平台生成') NOT NULL COMMENT '密钥类型',
    public_key VARCHAR(2048) NOT NULL COMMENT 'Base64编码的公钥',
    private_key VARCHAR(4096) COMMENT 'Base64编码的加密私钥，用户上传时可为空',
    key_size INT NOT NULL DEFAULT 2048 COMMENT '密钥长度（位）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '最后更新时间',
    UNIQUE KEY uk_name (name) COMMENT '名称唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RSA密钥管理表';

-- person access token
CREATE TABLE IF NOT EXISTS person_access_token (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    token_value VARCHAR(2048) NOT NULL COMMENT '令牌值',
    lifetime_seconds BIGINT NOT NULL COMMENT '有效期（秒）',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人访问令牌表';