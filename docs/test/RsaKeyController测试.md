# RSA密钥管理接口测试命令

# 环境变量设置
```bash
# 设置服务地址
export HOST="*************:7070"
# 设置认证Cookie
export AUTH_COOKIE="JSESSIONID=FA19FC550429C2987E867574275E9926; nonceId=7af71f8a-7194-44fa-b34d-982676c3bf48; callbackUrl=http://ec.kcde.kscbigdata.cloud/#/main/infrastructure; sso-token=AA2200FF222255440099DDEEEEBB2299-84846dd42b894c69ab4c3fcdbdc6a456"
```

## 1. 分页查询密钥列表

### 基本查询
```bash
curl -X GET "http://$HOST/authserver/api/rsaKeys?current=1&size=10" \
-H "Cookie: $AUTH_COOKIE" | jq
```

### 带名称搜索
```bash
curl -X GET "http://$HOST/authserver/api/rsaKeys?current=1&size=10&name=test" \
-H "Cookie: $AUTH_COOKIE" | jq
```

## 2. 生成新的RSA密钥对

### 使用默认密钥大小(2048)
```bash
curl -X POST "http://$HOST/authserver/api/rsaKeys/generate" \
-H 'Content-Type: application/x-www-form-urlencoded' \
-H "Cookie: $AUTH_COOKIE" \
-d 'name=my-key' | jq
```

### 指定密钥大小
```bash
curl -X POST "http://$HOST/authserver/api/rsaKeys/generate" \
-H 'Content-Type: application/x-www-form-urlencoded' \
-H "Cookie: $AUTH_COOKIE" \
-d 'name=my-key' \
-d 'keySize=4096' | jq
```

## 3. 上传公钥
```bash
curl -X POST "http://$HOST/authserver/api/rsaKeys/upload" \
-H 'Content-Type: application/x-www-form-urlencoded' \
-H "Cookie: $AUTH_COOKIE" \
-d 'name=myuploaded-key' \
-d 'publicKey=ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDak6ksX5YL8vXR2JymWqJT9Znz0iNurO8sIkOvMOca5cemX6n8EPfnLQxrMZEw6sZ/KTFI2uRwnQJu4fcbcog2zYGFAD2TwOozhnduVhZCSIDZofcayCZzoggs+LHsB5Io/fKn1WjixuqrO42njb9DYOrFcPRroWoPnEOzVG4lI9jQVyBv191bI89Z5Nwvi6XV3zCutElltL/O1PLuXw34tlqCLkUqZe2bf2zOFo6j2ocrHdC0fuXk65XLDH05vQdXjOXdyGSCci0Nm9r33m1Y2So1XIANl3WLsc/JEhugQ2CNHYgpmhMg5SCycTz/frGdKFXh9IeVxnmgZg8DVyeM+7WdudBCaa4x+buYDxHuiJmd/MD+ivEiaoBZ+GgrZUu0Fb8QL9vLTqSzadObsi6T8k0rv42zbnROlcipdcd4zl25yzm2l1OxvApEVC4sCoghuN99k8zh4O5XFfFHYUtS8MZzFgO7e8nPaYKYfCeBfRLY6dSY7IHaIVz9mYykPms= <EMAIL>' | jq
```

## 4. 删除密钥
```bash
curl -X DELETE "http://$HOST/authserver/api/rsaKeys/1" \
-H "Cookie: $AUTH_COOKIE" | jq
```

## 5. 更新密钥状态

### 激活密钥
```bash
curl -X PUT "http://$HOST/authserver/api/rsaKeys/1/status" \
-H 'Content-Type: application/x-www-form-urlencoded' \
-H "Cookie: $AUTH_COOKIE" \
-d 'isActive=true' | jq
```

### 停用密钥
```bash
curl -X PUT "http://$HOST/authserver/api/rsaKeys/1/status" \
-H 'Content-Type: application/x-www-form-urlencoded' \
-H "Cookie: $AUTH_COOKIE" \
-d 'isActive=false' | jq
