# 个人访问令牌 API 文档

## 概述

个人访问令牌（Personal Access Token，PAT）是一种用于API认证的长期令牌。每个用户只能拥有一个有效的PAT，创建新的PAT会自动使旧的PAT失效。

## API 接口

### 1. 创建个人访问令牌

创建一个新的个人访问令牌。如果用户已有令牌，旧令牌会被自动删除。

**请求方法**：POST

**请求路径**：`/api/createPersonAccessToken`

**请求头**：
- `Authorization`: Bearer token（必需）

**响应示例**：
```json
{
    "code": 200,
    "data": {
        "tokenValue": "eyJhbGciOiJSUzI1NiIs...",
        "createdAt": "2025-02-26T09:00:00Z",
        "expiresAt": "2025-03-28T09:00:00Z"
    }
}
```

**错误响应示例**：
```json
{
    "code": 500,
    "message": "认证信息非法"
}
```

**curl示例**：
```bash
curl -X POST 'http://localhost:8080/api/createPersonAccessToken' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 2. 获取个人访问令牌列表

获取当前用户的个人访问令牌信息。

**请求方法**：GET

**请求路径**：`/api/listPersonAccessToken`

**请求头**：
- `Authorization`: Bearer token（必需）

**响应示例**：
```json
{
    "code": 200,
    "data": [
        {
            "tokenValue": "eyJhbGciOiJSUzI1NiIs...",
            "createdAt": "2025-02-26T09:00:00Z",
            "expiresAt": "2025-03-28T09:00:00Z"
        }
    ]
}
```

**错误响应示例**：
```json
{
    "code": 500,
    "message": "认证信息非法"
}
```

**curl示例**：
```bash
curl -X GET 'http://localhost:8080/api/listPersonAccessToken' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 3. 获取当前用户的访问令牌

获取当前认证用户的个人访问令牌信息。

**请求方法**：GET

**请求路径**：`/api/getPersonAccessTokenById`

**请求头**：
- `Authorization`: Bearer token（必需）

**响应示例**：
```json
{
    "code": 200,
    "data": {
        "tokenValue": "eyJhbGciOiJSUzI1NiIs...",
        "createdAt": "2025-02-26T09:00:00Z",
        "expiresAt": "2025-03-28T09:00:00Z"
    }
}
```

**错误响应示例**：
```json
{
    "code": 500,
    "message": "认证信息非法"
}
```

或

```json
{
    "code": 500,
    "message": "未找到该用户的访问令牌"
}
```

**curl示例**：
```bash
curl -X GET 'http://localhost:8080/api/getPersonAccessTokenById' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 4. 验证令牌

验证令牌是否有效。

**请求方法**：POST

**请求路径**：`/api/validatePersonAccessToken`

**请求参数**：
- `token`: 令牌值（必需）

**响应示例**：
```json
{
    "code": 200,
    "result": true
}
```

**错误响应示例**：
```json
{
    "code": 500,
    "message": "令牌无效"
}
```

**curl示例**：
```bash
curl -X POST 'http://localhost:8080/api/validatePersonAccessToken' \
  -d 'token=YOUR_TOKEN_VALUE'
```

## 错误码

| 错误码 | 描述 |
|--------|------|
| 200    | 操作成功 |
| 400    | 请求参数错误 |
| 401    | 未授权或令牌无效 |
| 403    | 权限不足 |
| 404    | 令牌不存在 |
| 500    | 服务器内部错误 |

## 单元测试

### 测试类
测试类位于`auth-server/src/test/java/com/ksyun/auth/server/controller/PersonAccessTokenControllerTest.java`

### 测试用例

1. 创建令牌测试
```java
@Test
@WithMockUser(username = "testUser")
public void testCreateToken() {
    // 测试创建新的PAT
}
```

2. 获取令牌列表测试
```java
@Test
@WithMockUser(username = "testUser")
public void testListTokens() {
    // 测试获取PAT列表
}
```

3. 获取当前用户令牌测试
```java
@Test
@WithMockUser(username = "testUser")
public void testGetTokenById() {
    // 测试获取当前用户的PAT
    // 包含成功和失败场景
}
```

4. 验证令牌测试
```java
@Test
public void testValidateToken() throws Exception {
    // 准备测试数据
    String validToken = "eyJhbGciOiJSUzI1..."; // 有效的token
    String expiredToken = "eyJhbGciOiJSUzI1..."; // 过期的token
    String invalidToken = "invalid-token"; // 无效的token
    
    PersonAccessToken token = new PersonAccessToken();
    token.setTokenValue(validToken);
    token.setUserId(1L);
    token.setCreatedAt(new Date());
    token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000));

    when(personAccessTokenService.getTokenByUserId(1L)).thenReturn(token);

    // 测试场景1：有效token
    mockMvc.perform(post("/api/validatePersonAccessToken")
            .param("token", validToken))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result").value(true));

    // 测试场景2：过期token
    mockMvc.perform(post("/api/validatePersonAccessToken")
            .param("token", expiredToken))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result").value(false));

    // 测试场景3：无效token
    mockMvc.perform(post("/api/validatePersonAccessToken")
            .param("token", invalidToken))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result").value(false));
}
```

## 测试环境配置

### 1. 测试类配置
```java
@SpringBootTest(
        classes = AuthServerApplication.class,
        properties = {
                "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,"
                        + "org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration,"
                        + "org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration"
        }
)
public class PersonAccessTokenControllerTest {
    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Resource
    private PersonAccessTokenController personAccessTokenController;

    @MockBean
    private PersonAccessTokenService personAccessTokenService;

    private AuthUser mockAuthUser;
}
```

### 2. 测试初始化
```java
@BeforeEach
void setUp() {
    // 仅测试单个controller，使用standaloneSetup
    mockMvc = MockMvcBuilders.standaloneSetup(personAccessTokenController)
            .defaultResponseCharacterEncoding(StandardCharsets.UTF_8)
            .build();
    
    // 模拟认证用户
    mockAuthUser = mock(AuthUser.class);
    when(mockAuthUser.getId()).thenReturn(1L);
}
```

### 3. 测试依赖
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.springframework.security</groupId>
    <artifactId>spring-security-test</artifactId>
    <scope>test</scope>
</dependency>
```

## API 接口测试

### 1. 创建个人访问令牌测试

```java
@Test
@WithMockUser(username = "testUser")
public void testCreateToken() throws Exception {
    // 准备测试数据
    PersonAccessToken token = new PersonAccessToken();
    token.setTokenValue("test-token");
    token.setCreatedAt(new Date());
    token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000));

    when(personAccessTokenService.createToken(anyLong())).thenReturn(token);

    // 执行测试
    mockMvc.perform(post("/api/createPersonAccessToken")
            .requestAttr(Authentication.ARAN, mockAuthUser))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result.tokenValue").value("test-token"))
            .andExpect(jsonPath("$.result.createdAt").exists())
            .andExpect(jsonPath("$.result.expiresAt").exists());
}
```

### 2. 获取令牌列表测试

```java
@Test
@WithMockUser(username = "testUser")
public void testListTokens() throws Exception {
    // 准备测试数据
    PersonAccessToken token = new PersonAccessToken();
    token.setTokenValue("test-token");
    token.setCreatedAt(new Date());
    token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000));

    when(personAccessTokenService.getTokens(anyLong()))
            .thenReturn(Collections.singletonList(token));

    // 执行测试
    mockMvc.perform(get("/api/listPersonAccessToken")
            .requestAttr(Authentication.ARAN, mockAuthUser))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result[0].tokenValue").value("test-token"))
            .andExpect(jsonPath("$.result[0].createdAt").exists())
            .andExpect(jsonPath("$.result[0].expiresAt").exists());
}
```

### 3. 获取当前用户令牌测试

```java
@Test
@WithMockUser(username = "testUser")
public void testGetTokenById() throws Exception {
    // 准备测试数据
    Long userId = 1L;
    PersonAccessToken token = new PersonAccessToken();
    token.setUserId(userId);
    token.setTokenValue("test-token");
    token.setCreatedAt(new Date());
    token.setExpiresAt(new Date(System.currentTimeMillis() + 86400000));

    when(personAccessTokenService.getTokenByUserId(userId)).thenReturn(token);

    // 执行测试 - 成功场景
    mockMvc.perform(get("/api/getPersonAccessTokenById")
            .param("user_id", userId.toString())
            .requestAttr(Authentication.ARAN, mockAuthUser))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result.tokenValue").value("test-token"))
            .andExpect(jsonPath("$.result.createdAt").exists())
            .andExpect(jsonPath("$.result.expiresAt").exists());

    // 执行测试 - 用户不存在场景
    when(personAccessTokenService.getTokenByUserId(999L)).thenReturn(null);
    mockMvc.perform(get("/api/getPersonAccessTokenById")
            .param("user_id", "999"))
            .andDo(result -> {
                String content = result.getResponse().getContentAsString();
                System.out.println("Response content: " + content);
            })
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.result").doesNotExist());
}
```

## 测试要点说明

1. 测试配置
   - 使用`@SpringBootTest`加载应用上下文
   - 排除了Spring Security的自动配置，避免认证干扰
   - 使用`standaloneSetup`配置MockMvc，专注于单个Controller的测试
   - 设置UTF-8编码确保响应内容正确显示

2. 认证模拟
   - 使用`mock(AuthUser.class)`创建模拟用户
   - 通过`requestAttr(Authentication.ARAN, mockAuthUser)`注入认证信息
   - 使用`@WithMockUser`注解提供基础认证支持

3. 测试覆盖
   - 包含了所有API端点的测试
   - 覆盖了成功和失败场景
   - 验证了响应码和响应数据结构
   - 添加了响应内容打印，便于调试

4. 断言验证
   - 检查HTTP状态码
   - 验证响应中的result字段
   - 验证返回数据的完整性
   - 确保时间字段存在

5. Mock使用
   - 使用Mockito模拟Service层返回数据
   - 使用`anyLong()`匹配任意用户ID参数
   - 模拟不同场景的返回值

## 运行测试

1. 使用Maven运行测试
```bash
mvn test -Dtest=PersonAccessTokenControllerTest
```

2. 使用IDE运行
   - 在IDE中右键点击测试类
   - 选择"Run PersonAccessTokenControllerTest"

## 注意事项

1. 测试环境配置
   - 确保已配置好测试环境
   - 检查所有必要的测试依赖
   - 验证编码设置正确

2. 测试数据准备
   - 使用固定值便于断言验证
   - 时间字段使用当前时间
   - 用户ID使用模拟值

3. Mock配置
   - Service层需要完整的Mock
   - 认证用户需要正确模拟
   - 返回值符合实际业务逻辑

4. 响应验证
   - 使用正确的JSON路径（$.result）
   - 添加响应内容打印便于调试
   - 验证所有关键字段

5. 异常处理
   - 测试用户不存在的场景
   - 验证错误响应格式
   - 确保异常被正确处理

6. 令牌验证测试
   - 测试有效token的验证
   - 测试过期token的验证
   - 测试无效token的验证
   - 验证响应格式正确性
