package com.ksyun.auth.client.interceptor;

import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.CommonUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.logging.Logger;

public class TenantTokenProcessor extends AppTokenProcessor {
    private static final Logger RUN_LOG = Logger.getLogger("TenantHeaderProcessor");
    private static final int ORDER = 3;

    public TenantTokenProcessor(String ak, String sk, String authServerUrl) {
        super(ak, sk, authServerUrl);
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        return AccessBy.Method.TENANT_TOKEN;
    }

    @Override
    protected String getHeaderKey(HttpServletRequest request) {
        return request.getHeader(CONFIG_KEY_TENANT);
    }

    @Override
    protected String getTokenFromServer(String headerKey) {
        return CommonUtils.getTenantToken(authServerUrl, headerKey);
    }
}
