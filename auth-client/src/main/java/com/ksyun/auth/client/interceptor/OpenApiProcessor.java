package com.ksyun.auth.client.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.cache.GenericCache;
import jodd.http.HttpRequest;
import jodd.http.HttpResponse;
import jodd.http.HttpStatus;
import jodd.util.StringUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;


public class OpenApiProcessor implements AuthenticationProcessor {

    private static final Logger LOGGER = Logger.getLogger("OpenApiProcessor");

    private static final int ORDER = 0;

    //缓存头参数名
    private static final String CACHE_OPEN_API_AUTH_USER_KEY = "cacheOpenApiAuthUserKey";

    // 请求头参数名，OpenApi 标识
    private static final String CONFIG_KEY_KSC_ACCOUNT_ID = "X-KSC-ACCOUNT-ID";
    private static final String CONFIG_KEY_KSC_USER_ID = "X-KSC-USER-ID";

    // APP 标识，由认证中心分配
    private String appKey;
    // APP 密钥，由认证中心分配
    protected String appSecret;
    // 认证中心URL
    protected String authServerUrl;


    private static final GenericCache<String, AuthUser> GENERIC_AUTH_CACHE = new GenericCache<>();

    public OpenApiProcessor(String ak, String sk, String authServerUrl) {
        AuthenticationHelper.initialize(ak, sk, authServerUrl);
        this.appKey = ak;
        this.appSecret = sk;
        this.authServerUrl = authServerUrl;
    }

    @Override
    public Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        String requestHeaderAccountId = getHeaderKscAccountId(request);
        String requestHeaderUserId = getHeaderKscUserId(request);

        LOGGER.info(String.format("OpenApiProcessor url = %s, requestHeaderAccountId = %s,requestHeaderUserId = %s", request.getRequestURL(), requestHeaderAccountId, requestHeaderUserId));

        return parseOpenApiHeaderByAuthUser(requestHeaderAccountId, requestHeaderUserId, authorize, request);
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        return AccessBy.Method.APP_TOKEN;
    }

    private Optional<Authentication> parseOpenApiHeaderByAuthUser(String requestHeaderAccountId, String requestHeaderUserId, Authorize.Type authorize, HttpServletRequest request) {
        if (StringUtil.isEmpty(requestHeaderAccountId)) {
            return Optional.empty();
        }
        if (StringUtil.isEmpty(requestHeaderUserId)) {
            LOGGER.info("OpenApiProcessor X-KSC-USER-ID IS NULL");
        }

        AuthUser authUser = GENERIC_AUTH_CACHE.get(CACHE_OPEN_API_AUTH_USER_KEY + ":" + requestHeaderAccountId + "_" + requestHeaderUserId, () -> parseAuthenticationByAuthServer(requestHeaderAccountId, requestHeaderUserId), 60);

        if (authUser != null) {
            CommonUtils.checkUserHasPrivilege(request, authorize, authUser, appKey, 30, AuthenticationHelper.getAuthServerUrl());
        }

        return Optional.ofNullable(authUser);
    }

    private AuthUser parseAuthenticationByAuthServer(String requestHeaderAccountId, String requestHeaderUserId) {
        String url = AuthenticationHelper.getAuthServerUrl()
                .concat("/api/convertAuthUser?tenantId=" + requestHeaderAccountId)
                .concat("&iamId=" + requestHeaderUserId);
        LOGGER.info(String.format("Start to parse convertAuthUser: url=%s, AccountId=%s,UserId=%s", url, requestHeaderAccountId, requestHeaderUserId));

        HttpResponse response = HttpRequest.get(url).header(Authentication.HEADER_X_AUTH_APP, appKey)
                .header(Authentication.HEADER_X_AUTH_TOKEN, AuthenticationHelper.getToken()).send();
        LOGGER.info(String.format("Response of parse convertAuthUser: url=%s, responseStatusCode=%s", url, response.statusCode()));

        return processConvertAuthUserResponse(response);
    }

    private AuthUser processConvertAuthUserResponse(HttpResponse response) {
        JSONObject jsonObject = processResponse(response);
        return JSON.parseObject(jsonObject == null ? null : jsonObject.getString("result"), AuthUser.class);
    }

    private JSONObject processResponse(HttpResponse response) {
        if (response.statusCode() != HttpStatus.HTTP_OK) {
            LOGGER.log(Level.SEVERE, String.format("Parse Authentication OpenApiHeader Failed, Server-Response=%s", response));
            return null;
        }

        JSONObject rr = JSON.parseObject(response.bodyText());
        if (rr.getInteger("status") != HttpStatus.HTTP_OK) {
            LOGGER.log(Level.SEVERE, String.format("Parse Authentication OpenApiHeader Failed, Server-Response-Message=%s", rr.toJSONString()));
            return null;
        }

        return rr;
    }

    private String getHeaderKscAccountId(HttpServletRequest request) {
        return request.getHeader(CONFIG_KEY_KSC_ACCOUNT_ID);
    }

    private String getHeaderKscUserId(HttpServletRequest request) {
        return request.getHeader(CONFIG_KEY_KSC_USER_ID);
    }
}
