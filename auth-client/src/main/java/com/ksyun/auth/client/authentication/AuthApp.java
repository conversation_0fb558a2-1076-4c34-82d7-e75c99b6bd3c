package com.ksyun.auth.client.authentication;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class AuthApp implements Authentication {
    private String appKey;

    public AuthApp(String appKey) {
        this.appKey = appKey;
    }

    @Override
    public AuthenticationType getType() {
        return AuthenticationType.APP_AUTHENTICATION;
    }

    public String getAppKey() {
        return appKey;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return null;
    }

    @Override
    public boolean isAuthenticated() {
        return false;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {

    }

    @Override
    public String getName() {
        return null;
    }
}
