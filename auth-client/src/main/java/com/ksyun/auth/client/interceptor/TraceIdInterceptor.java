package com.ksyun.auth.client.interceptor;

import com.ksyun.auth.client.TraceUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * traceId请求拦截器
 *
 * <AUTHOR>
 * @since 2020-08-20 15:27
 */
public class TraceIdInterceptor implements HandlerInterceptor {

    public static final String CLIENT_TRANCE_ID = "CLIENT_TRANCE_ID";

    //@Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = request.getHeader(CLIENT_TRANCE_ID);
        if (StringUtils.isEmpty(traceId)) {
            TraceUtils.beginTaskTrace(TraceUtils.randomTraceId());
        } else {
            TraceUtils.beginTaskTrace(traceId);
        }
        return true;
    }

    //@Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    //@Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        TraceUtils.endTaskTrace();
    }

}
