package com.ksyun.auth.client;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.filter.AuthenticationFilter;
import com.ksyun.auth.client.interceptor.AppTokenProcessor;
import com.ksyun.auth.client.interceptor.AuthenticationInterceptor;
import com.ksyun.auth.client.interceptor.AuthenticationProcessor;
import com.ksyun.auth.client.interceptor.CookieProcessor;
import com.ksyun.auth.client.interceptor.OpenApiProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import static com.ksyun.auth.client.CommonUtils.filterRelatedCookies;
import static com.ksyun.auth.client.CommonUtils.getCookieNamesByAuthClientConfig;
import static com.ksyun.auth.client.Constants.CACHE_AUTH_CLIENT_CONFIG_KEY;

/**
 * 辅助生成请求所需要的 X-AUTH-TOKEN
 */
@Slf4j
public final class AuthenticationHelper {
    private static final AtomicReference<String> AUTH_SERVER_URL = new AtomicReference<>();
    private static final AtomicReference<String> APP_KEY = new AtomicReference<>(null);
    private static final AtomicReference<String> APP_SECRET = new AtomicReference<>(null);
    private static final AtomicReference<Boolean> INITIALIZED = new AtomicReference<>(Boolean.FALSE);

    private static Authorize.Type DEFAULT_AUTHORIZE_METHODS = Authorize.Type.NONE;

    //默认cookie 缓存时间
    private static long COOKIE_CACHE_SECOND = 60;

    public static long getCookieCacheSecond() {
        return COOKIE_CACHE_SECOND;
    }

    public static void setCookieCacheSecond(long cookieCacheSecond) {
        COOKIE_CACHE_SECOND = cookieCacheSecond;
    }

    public static void setDefaultAuthorizeMethod(Authorize.Type authType) {
        DEFAULT_AUTHORIZE_METHODS = authType;
    }

    public static Authorize.Type getDefaultAuthorizeMethod() {
        return DEFAULT_AUTHORIZE_METHODS;
    }

    public static final AuthenticationInterceptor getHeaderInterceptor(String ak, String sk, String authServerUrl) {
        return new AuthenticationInterceptor(new AppTokenProcessor(ak, sk, authServerUrl));
    }

    public static final AuthenticationInterceptor getCookieAndHeaderInterceptor(String ak, String sk, String authServerUrl) {
        log.info("=========ak={}", ak);
        return new AuthenticationInterceptor(new CookieProcessor(ak, sk, authServerUrl), new AppTokenProcessor(ak, sk, authServerUrl));
    }

    public static final AuthenticationInterceptor getCookieAndHeaderInterceptor(String ak, String sk, String authServerUrl, boolean filterOpenApiPrivilege) {
        if (!filterOpenApiPrivilege) {
            return getCookieAndHeaderInterceptor(ak, sk, authServerUrl);
        }
        return new AuthenticationInterceptor(new CookieProcessor(ak, sk, authServerUrl), new AppTokenProcessor(ak, sk, authServerUrl), new OpenApiProcessor(ak, sk, authServerUrl));
    }

    public static final AuthenticationInterceptor getCustomizedInterceptor(AuthenticationProcessor... processors) {
        return new AuthenticationInterceptor(processors);
    }

    @Deprecated
    public static final AuthenticationFilter getCookieAndHeaderFilter(String ak, String sk, String authServerUrl, boolean filterOpenApiPrivilege) {
        return new AuthenticationFilter(new CookieProcessor(ak, sk, authServerUrl), new AppTokenProcessor(ak, sk, authServerUrl), new OpenApiProcessor(ak, sk, authServerUrl));
    }

    @Deprecated
    public static final AuthenticationFilter getCustomizedFilter(AuthenticationProcessor... processors) {
        return new AuthenticationFilter(processors);
    }

    public static Authentication getAuthentication(HttpServletRequest request) {
        return (Authentication) request.getAttribute(Authentication.ARAN);
    }

    public static void initialize(String ak, String sk, String authServerUrl) {
        if (!INITIALIZED.get()) {
            synchronized (INITIALIZED) {
                if (!INITIALIZED.get()) {
                    APP_KEY.set(ak);
                    APP_SECRET.set(sk);
                    AUTH_SERVER_URL.set(authServerUrl);
                    log.info(String.format("AuthenticationInterceptor Initialized: ak=%s, sk=%s, auth-server-url=%s", ak, sk, authServerUrl));

                    INITIALIZED.compareAndSet(Boolean.FALSE, Boolean.TRUE);
                }
            }
        }
    }

    /**
     * 获取AK
     */
    public static String getAk() {
        return APP_KEY.get();
    }

    /**
     * 获取SK
     */
    public static String getSk() {
        return APP_SECRET.get();
    }

    /**
     * 获取AuthServerUrl
     */
    public static String getAuthServerUrl() {
        return AUTH_SERVER_URL.get();
    }

    /**
     * 生成当前应用的 APP TOKEN
     */
    public static String getToken() {
        String dsk = CommonUtils.getDsk(AUTH_SERVER_URL.get());
        return CommonUtils.encodeByMd5(APP_SECRET.get(), dsk);
    }

    public static String getOauthLoginUrl() {
        try {
            String oAuthLoginUrl = CommonUtils.getOauthLoginUrl(AUTH_SERVER_URL.get());
            return oAuthLoginUrl;
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 生成指定租户的TOKEN
     */
    public static String getTenantToken(String tenant) {
        String dsk = CommonUtils.getDsk(AUTH_SERVER_URL.get());
        String tenantAkSk = CommonUtils.getTenantAkSk(AUTH_SERVER_URL.get(), tenant);
        String tenantSk = StringUtils.isEmpty(tenantAkSk) ? null : tenantAkSk.split(",")[1];
        return StringUtils.isEmpty(tenantSk) ? null : CommonUtils.encodeByMd5(tenantSk, dsk);
    }

    /**
     * 从请求中获取认证相关的 Cookie，即名称为 sso-token 或 kscdiggest 的 cookie，优先获取sso-token，如果没有再获取kscdiggest
     */
    public static Optional<Cookie> getAuthenticationRelatedCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        Authentication authentication = getAuthentication(request);
        if (cookies != null) {
            Set<Cookie> relatedCookies = filterRelatedCookies(Collections.singletonList("sso-token"), cookies);
            return Arrays.stream(relatedCookies.toArray(new Cookie[0])).findAny();
        } else if (authentication instanceof AuthUser) {
            AuthUser authUser = (AuthUser) authentication;
            return CommonUtils.generationSsoTokenCookie(getAuthServerUrl(), getAk(), authUser);
        } else {
            return Optional.empty();
        }
    }

    public static Optional<Cookie> getAuthenticationRelatedCookie(HttpServletRequest request, String systemType) {
        Cookie[] cookies = request.getCookies();
        Authentication authentication = getAuthentication(request);
        AuthClientConfig authClientConfig = CookieProcessor.GENERIC_AUTH_CLIENT_CONFIG_CACHE.get(CACHE_AUTH_CLIENT_CONFIG_KEY);
        if (Objects.isNull(authClientConfig)) {
            authClientConfig = CommonUtils.invokerAuthClientConfig();
        }

        if (cookies != null) {
            Set<Cookie> relatedCookies = filterRelatedCookies(getCookieNamesByAuthClientConfig(authClientConfig, systemType), cookies);
            return Arrays.stream(relatedCookies.toArray(new Cookie[0])).findAny();
        } else if (authentication instanceof AuthUser) {
            AuthUser authUser = (AuthUser) authentication;
            return CommonUtils.generationSsoTokenCookie(getAuthServerUrl(), getAk(), authUser);
        } else {
            return Optional.empty();
        }
    }

    /**
     * 从请求中获取认证相关的 Cookie，即名称为 sso-token 或 kscdiggest 的 cookie，优先获取sso-token，如果没有再获取kscdiggest
     * 如果request 不存在Cookie 并且传递了AuthUser对象 则将authUser对象转换成sso-token Cookie返回
     */
    public static Optional<Cookie> getAuthenticationRelatedCookie(HttpServletRequest request, AuthUser authUser) {
        Optional<Cookie> cookie = getAuthenticationRelatedCookie(request);
        if (!cookie.isPresent() && authUser != null) {
            return CommonUtils.generationSsoTokenCookie(getAuthServerUrl(), getAk(), authUser);
        }
        return cookie;
    }

    /**
     * 从请求中获取认证相关的 Cookie，即名称为 sso-token 或 kscdiggest 的 cookie
     */
    public static Cookie[] getAuthenticationRelatedCookies(HttpServletRequest request) {
        if (request.getCookies() != null) {
            return Arrays.stream(request.getCookies()).toArray(Cookie[]::new);
        }
        return null;
    }

    /**
     * 从请求中获取要认证成为的租户，kcde 0.6中一个用户绑定多个租户，需要校验用户是否绑定请求中的租户
     */
    public static String getAuthenticationTenantId(HttpServletRequest request) {
        return request.getHeader(Authentication.HEADER_X_AUTH_TENANT_ID);
    }

}
