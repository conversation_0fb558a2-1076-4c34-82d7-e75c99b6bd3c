package com.ksyun.auth.client.cache;

import java.util.List;
import java.util.concurrent.TimeUnit;

public interface CacheStrategy {

    boolean expire(String key, long time);

    /**
     * 获取缓存
     * @param key 缓存键
     * @return 缓存值
     */
    Object get(String key);

    /**
     * 设置缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    void set(String key, Object value);

    /**
     * 设置缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    void set(String key, String value, long time);

    /**
     * 设置缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    void set(String key, Object value, long time);

    /**
     * 删除缓存
     */
    void delete(String... key);

}
