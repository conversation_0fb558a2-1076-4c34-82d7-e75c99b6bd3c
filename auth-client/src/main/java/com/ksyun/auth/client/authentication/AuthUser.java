package com.ksyun.auth.client.authentication;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import org.springframework.security.core.GrantedAuthority;

import java.io.Serializable;
import java.util.*;

@Setter
@Getter
@Builder(toBuilder=true)
@AllArgsConstructor
@NoArgsConstructor
public class AuthUser implements Authentication {
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return name;
    }

    @Override
    public boolean isAuthenticated() {
        return true;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        authenticated = isAuthenticated;
    }

    public enum Status {NORMAL, DELETED, LOGOUT, SLEEP}

//    public enum AKSKIdentification {IAAS_AKSK, USER_AKSK}

    private Long id;
    private String name;
    private String alias;
    private String source;
    private Status status;
    private String deadline;
    private int secretLevel;
    private boolean resetPwdWhenFirstLogin;
    private Date createTime;
    private Date updateTime;
    private InnerTenant tenant;
    private List<InnerTenant> tenants = new ArrayList<>(1);
    private Collection<String> roleTags = new HashSet<>(1);
    private Collection<String> groups = new HashSet<>(1);
//    private String ak;
//    private String sk;
//    private String userAk;
//    private String userSk;
//    private String ipConfigStatus;
    /**
     * 用来区分是哪个系统的用户
     */
    private String systemType;
    private boolean authenticated;


    @Override
    @JsonIgnore
    public AuthenticationType getType() {
        return AuthenticationType.USER_AUTHENTICATION;
    }

    public static class InnerTenant implements Serializable {
        Long id;
        String name;
        String status;

        public InnerTenant(){}

        public InnerTenant(Long id, String name, String status) {
            this.id = id;
            this.name = name;
            this.status = status;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }


        @Override
        public String toString() {
            return "InnerTenant{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", status='" + status + '\'' +
                    "}";
        }
    }

//    public boolean getIsTenant() {
//        return tenant != null && Objects.equals(id, tenant.getId());
//    }

//    public ArrayList<AKSKIdentification> getAKSKIdentification() {
//        ArrayList<AKSKIdentification> akskIdentification = new ArrayList<>();
//        if (tenant != null && Objects.equals(id, tenant.getId())) {
//            if ((ak != null && !ak.isEmpty()) && (sk != null && !sk.isEmpty())) {
//                akskIdentification.add(AKSKIdentification.IAAS_AKSK);
//            }
//        }
//
//        if ((userAk != null && !userAk.isEmpty()) && (userSk != null && !userSk.isEmpty())) {
//            akskIdentification.add(AKSKIdentification.USER_AKSK);
//        }
//
//        return akskIdentification;
//    }


//    @JsonIgnore
//    public String getAk() {
//        return ak;
//    }
//
//    public void setAk(String ak) {
//        this.ak = ak;
//    }
//
//    @JsonIgnore
//    public String getSk() {
//        return sk;
//    }
//
//    public void setSk(String sk) {
//        this.sk = sk;
//    }

//    @JsonIgnore
//    public String getUserAk() {
//        return userAk;
//    }
//
//    public void setUserAk(String userAk) {
//        this.userAk = userAk;
//    }
//
//    @JsonIgnore
//    public String getUserSk() {
//        return userSk;
//    }
//
//    public void setUserSk(String userSk) {
//        this.userSk = userSk;
//    }

    public void attachTenant(InnerTenant tenant) {
        tenants.add(tenant);
    }

    public List<InnerTenant> getTenants() {
        return tenants;
    }

    public InnerTenant getTenant() {
        return tenant;
    }

    public void setTenant(InnerTenant tenant) {
        this.tenant = tenant;
    }

    @Override
    public String toString() {
        return "AuthUser{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", alias='" + alias + '\'' +
                ", source='" + source + '\'' +
                ", status=" + status +
                ", secretLevel=" + secretLevel +
                ", resetPwdWhenFirstLogin=" + resetPwdWhenFirstLogin +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", tenant=" + tenant +
                ", roleTags=" + roleTags +
                ", groups=" + groups +
//                ", ak='" + ak + '\'' +
//                ", sk='" + sk + '\'' +
//                ", userAk='" + userAk + '\'' +
//                ", userSk='" + userSk + '\'' +
//                ", ipConfigStatus='" + ipConfigStatus + '\'' +
                '}';
    }
}
