package com.ksyun.auth.client;

public class Constants {

    /**
     * 用来标识去哪个数据源做权限校验
     */
    public static final String AUTH_TYPE = "Auth-Type";
    /**
     * 用来标识路由到哪个数据源
     */
    public static final String SYSTEM_TYPE = "System-Type";
    /**
     * 大数据云数据源标识
     */
    public static final String BIG_DATA = "bigdata";
    /**
     * 运营数据源标识
     */
    public static final String OPE = "ope";
    /**
     * 运维数据源标识
     */
    public static final String OPS = "ops";

    public static final String START_TIME = "startTime";

    public static final String RESPONSE_TIME = "responseTime";
    public static final String TRACE_ID = "traceId";

    public static final String SYNC_DSK_PATH = "/key";
    public static final String OAUTH_LOGIN_URL_PATH = "/oauthLoginUrl";
    public static final String SYNC_APP_TOKEN_PATH = "/app-token/{app}";
    public static final String SYNC_TENANT_SK_PATH = "/tenant-aksk/{tenant}";
    public static final String SYNC_TENANT_TOKEN_PATH = "/tenant-token/{tenant}";
    public static final String SYNC_COOKIE = "/api/authUserConvertCookie";

    public static final String HEADER_AUTH_PROJECT_ID = "AUTH-PROJECT-ID";
    // 网络限制
    public static final String CONFIG_KEY_KSC_NETWORK = "network_tag";
    public static final String NOT_FOUND_NO_PRIVILEGE = "功能未授权，请检查权限点配置";
    public static final String NOT_FOUND_HEADER_PROJECT = "Header Project Is NUll";

    // IP限制
    public static final String NOT_FOUND_IP_BLACK = "租户黑名单";
    public static final String CACHE_IP_CONFIG_KEY = "cacheIpConfigKey";
    public static final String CACHE_PRIVILEGE_KEY = "cachePrivilegeKey";
    public static final String CACHE_ACCESS_TIME_KEY = "cacheAccessTimeKey";


    public static final String DEFAULT_COOKIE_NAME = "sso-token";
    public static final String UNAUTHORIZED_MESSAGE = "请求未授权";
    // 标识 请求类型  IS-APP = true 前端请求
    public static final String CACHE_COOKIE_AUTH_USER_KEY = "cacheCookieAuthUserKey";
    public static final String CACHE_AUTH_CLIENT_CONFIG_KEY = "cacheAuthClientConfig";

    public static final String SUCCESS = "success";

}
