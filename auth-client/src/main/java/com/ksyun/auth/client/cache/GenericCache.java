package com.ksyun.auth.client.cache;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 通用缓存，该缓存只对数据保留一段时间，到期后，数据将被清除
 */
public class GenericCache<K, V> {
    private static final Logger LOGGER = Logger.getLogger("GenericCache");

    // 缓存数据默认时长：10分钟
    private long EXPIRE_PERIOD_IN_SECONDS = 600;
    // 清理过期缓存数据周期
    private long SCHEDULE_CLEAN_UP_PERIOD_IN_SECONDS = 1;
    // 数据缓存池
    private final Map<K, GenericCacheDataWrapper<V>> CACHE_CONTAINER = new ConcurrentHashMap<>(16);
    // 清理线程
    private final ScheduledExecutorService SCHEDULE_CLEAN_SERVICE = Executors.newSingleThreadScheduledExecutor(runnable -> {
        Thread thread = new Thread(runnable, "auth-client-cache-cleaner");
        thread.setDaemon(true);
        return thread;
    });

    public GenericCache() {
        SCHEDULE_CLEAN_SERVICE.scheduleWithFixedDelay(new CleanupThread(), 0, SCHEDULE_CLEAN_UP_PERIOD_IN_SECONDS, TimeUnit.SECONDS);
        LOGGER.info("SCHEDULE_CLEAN_SERVICE Start up, initial-delay=0s, fixed-delay=10s");
    }

    /**
     * 缓存一个键值对
     *
     * @param key   键
     * @param value 值
     */
    public void put(K key, V value) {
        Objects.requireNonNull(key, "key  is null");
        Objects.requireNonNull(value, "value is null");
        put(key, value, EXPIRE_PERIOD_IN_SECONDS);
    }

    /**
     * 缓存一个键值对
     *
     * @param key   键
     * @param value 值
     */
    public void put(K key, V value, long cacheTimeInSeconds) {
        CACHE_CONTAINER.put(key, new GenericCacheDataWrapper<>(value, cacheTimeInSeconds < 1 ? 1 : cacheTimeInSeconds));
    }

    public void remove(K key) {
        CACHE_CONTAINER.remove(key);
    }

    public void removeAll(List<K> keys) {
        for (K key : keys) {
            CACHE_CONTAINER.remove(key);
        }
    }

    /**
     * 根据键获取缓存的值
     *
     * @param key 键
     * @return 缓存的值
     */
    public V get(K key) {
        GenericCacheDataWrapper<V> wrapper = CACHE_CONTAINER.getOrDefault(key, null);
        if (wrapper == null) {
            return null;
        }
        if (wrapper.isExpired()) {
            CACHE_CONTAINER.remove(key);
            return null;
        }
        return wrapper.value;
    }

    /**
     * 根据 Key 获取缓存数据，如果缓存数据不存在，则用回调函数获取新数据，如果获取成功，则重新加入缓存
     *
     * @param key      Key
     * @param callable 回调函数，在获取缓存数据失败时调用
     * @return 缓存的数据
     */
    public V get(K key, Callable<V> callable) {
        V value = get(key);
        if (value == null) {
            value = getValueByCallable(key, callable);
            CACHE_CONTAINER.putIfAbsent(key, new GenericCacheDataWrapper<>(value));
        }
        return value;
    }

    /**
     * 根据 Key 获取缓存数据，如果缓存数据不存在，则用回调函数获取新数据，如果获取成功，则重新加入缓存
     *
     * @param key      Key
     * @param callable 回调函数，在获取缓存数据失败时调用
     * @param expireAt 缓存时间 秒
     * @return 缓存的数据
     */
    public V get(K key, Callable<V> callable, long expireAt) {
        V value = get(key);
        if (value == null) {
            value = getValueByCallable(key, callable);
            CACHE_CONTAINER.putIfAbsent(key, new GenericCacheDataWrapper<>(value, expireAt));
        }
        return value;
    }

    /**
     * 通过回调函数获取数据，但对于同一个key，不可并发调用，只要一个调用即可
     */
    private V getValueByCallable(K key, Callable<V> callable) {
        try {
            // 确保只对 key 加锁，不会全局加锁
            key.toString().intern();
            synchronized (key.toString()) {

                V value = get(key);
                if (value != null) {
                    return value;
                }

                return callable.call();
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Called Value Error", e);
            return null;
        }
    }

    /**
     * 缓存数据包装对象，包含了到期时间，默认到期时间 10 分钟
     *
     * @param <V> 被缓存的数据
     */
    class GenericCacheDataWrapper<V> {
        private final V value;
        private final long expireAt;

        GenericCacheDataWrapper(V value) {
            this.value = value;
            this.expireAt = System.currentTimeMillis() + EXPIRE_PERIOD_IN_SECONDS * 1000;
        }

        GenericCacheDataWrapper(V value, long expireAt) {
            this.value = value;
            this.expireAt = System.currentTimeMillis() + expireAt * 1000;
        }

        boolean isExpired() {
            return expireAt < System.currentTimeMillis();
        }
    }

    class CleanupThread implements Runnable {

        @Override
        public void run() {
            CACHE_CONTAINER.keySet().forEach(key -> {
                if (CACHE_CONTAINER.get(key).isExpired()) {
                    CACHE_CONTAINER.remove(key);
                    LOGGER.info(String.format("Clean up expired data: key=%s", key));
                }
            });
        }
    }

}
