package com.ksyun.auth.client.adapter;

import com.alibaba.fastjson.JSON;
import com.ksyun.auth.client.authentication.Authentication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.Cookie;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description: 只定义服务加载器
 * @author: wuzhenliang
 * @date: 2020-05-15
 */
@Component
@Slf4j
public class CookieAuthServiceLoader {
    private static List<CookieAuthAdapter> serviceList = new ArrayList<>();

    public Optional<Authentication> resolveAll(Map<String, Cookie> authCookie) {
        log.debug("CookieAuthServiceLoader serviceList = {}", JSON.toJSONString(serviceList));
        //1、预解析
        for (CookieAuthAdapter adapter : serviceList) {
            adapter.preResolve(authCookie);
        }

        //2、解析
        for (CookieAuthAdapter adapter : serviceList) {
            Optional<Authentication> optional = adapter.resolve(authCookie);
            if (optional.isPresent()) {
                return optional;
            }
        }
        return Optional.empty();
    }

    public void add(CookieAuthAdapter adapter) {
        serviceList.add(adapter);
    }

}
