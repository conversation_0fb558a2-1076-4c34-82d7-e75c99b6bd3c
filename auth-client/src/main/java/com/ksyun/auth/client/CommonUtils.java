package com.ksyun.auth.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.cache.GenericCache;
import com.ksyun.auth.client.interceptor.AuthenticationInterceptor;
import jodd.http.HttpRequest;
import jodd.http.HttpResponse;
import jodd.http.HttpStatus;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;

import static com.ksyun.auth.client.Constants.*;


@Slf4j
public class CommonUtils {

    private static final GenericCache<String, String> GENERIC_IP_CONFIG_CACHE = new GenericCache<>();
    private static final GenericCache<String, String> GENERIC_PRIVILEGE_CACHE = new GenericCache<>();
    private static final GenericCache<String, String> GENERIC_ACCESS_TIME_CACHE = new GenericCache<>();

    public static String getAppToken(String authServerUrl, String app) {
        String url = getAppTokenUrl(authServerUrl, app);
        return CommonUtils.getByApp(url, AuthenticationHelper.getAk(), AuthenticationHelper.getToken());
    }

    public static String getTenantAkSk(String authServerUrl, String tenant) {
        String url = getTenantAkSkUrl(authServerUrl, tenant);
        return CommonUtils.getByApp(url, AuthenticationHelper.getAk(), AuthenticationHelper.getToken());
    }

    public static String getTenantToken(String authServerUrl, String tenant) {
        String url = getTenantTokenUrl(authServerUrl, tenant);
        return CommonUtils.getByApp(url, AuthenticationHelper.getAk(), AuthenticationHelper.getToken());
    }

    public static String encodeByMd5(String sk, String dsk) {
        return DigestUtils.md5DigestAsHex(String.format("%s.%s", sk, dsk).getBytes());
    }

    public static Optional<Cookie> generationSsoTokenCookie(String authServerUrl, String appKey, AuthUser user) {
        return Optional.ofNullable(doCookieByPostApp(authServerUrl, JSON.toJSONString(user), appKey));
    }

    /**
     * 创建一个 Cookie, 确保只读
     *
     * @param key           Cookie 名
     * @param value         Cookie 值
     * @param domain        Cookie 域
     * @param expireSeconds 过期时间
     */
    public static Cookie newJavaCookie(String key, String value, String domain, int expireSeconds) {
        Cookie cookie = new Cookie(key, value);
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        if (DEFAULT_COOKIE_NAME.equalsIgnoreCase(key)) {
            // 会话级cookie，关闭浏览器失效
            // 解决关闭浏览器无法退出登录的问题
            // 只对数据平台sso-token有效
            cookie.setMaxAge(-1);
        } else {
            cookie.setMaxAge(expireSeconds);
        }

        cookie.setDomain(domain);
        return cookie;
    }

    static String getDsk(String authServerUrl) {
        String dsk = CommonUtils.get(getDskUrl(authServerUrl));
        if (!StringUtils.isEmpty(dsk)) {
            return dsk;
        }
        throw new RuntimeException("Get Access Key Failed...");
    }

    static String getOauthLoginUrl(String authServerUrl) {
        String oAuthLoginUrl = CommonUtils.get(getOauthLoginUrlUrl(authServerUrl));
        return oAuthLoginUrl;
    }

    private static String get(String url) {
        return handleHttpResponse(url, HttpRequest.get(url)
                .header(AUTH_TYPE, MDC.get(AUTH_TYPE) == null ? "" : MDC.get(AUTH_TYPE))
                .header(SYSTEM_TYPE, MDC.get(SYSTEM_TYPE) == null ? "" : MDC.get(SYSTEM_TYPE)).send());
    }

    private static String getByApp(String url, String appKey, String appToken) {
        log.debug("getByApp url={},appKey={},appToken={}", url, appKey, appToken);
        HttpResponse response = HttpRequest.get(url)
                .header(AUTH_TYPE, MDC.get(AUTH_TYPE) == null ? "" : MDC.get(AUTH_TYPE))
                .header(SYSTEM_TYPE, MDC.get(SYSTEM_TYPE) == null ? "" : MDC.get(SYSTEM_TYPE))
                .header(Authentication.HEADER_X_AUTH_APP, appKey)
                .header(Authentication.HEADER_X_AUTH_TOKEN, appToken).send();
        return handleHttpResponse(url, response);
    }

    private static Cookie doCookieByPostApp(String url, String body, String appKey) {
        HttpResponse response = HttpRequest.post(url.concat(SYNC_COOKIE)).header(Authentication.HEADER_X_AUTH_APP, appKey)
                .header(AUTH_TYPE, MDC.get(AUTH_TYPE) == null ? "" : MDC.get(AUTH_TYPE))
                .header(SYSTEM_TYPE, MDC.get(SYSTEM_TYPE) == null ? "" : MDC.get(SYSTEM_TYPE))
                .header(Authentication.HEADER_X_AUTH_TOKEN, AuthenticationHelper.getToken())
                .header(HttpRequest.HEADER_CONTENT_TYPE, "application/json")
                .bodyText(body, "application/json").send();
        return processResponse(response);
    }

    private static String handleHttpResponse(String url, HttpResponse response) {
        if (HttpStatus.HTTP_OK != response.statusCode()) {
            String err = "Send URL ERROR: url=%s, status=%s, status-msg=%s, response=%s";
            throw new RuntimeException(String.format(err, url, response.statusCode(), response.statusPhrase(), response.bodyText()));
        }
        log.debug("handleHttpResponse url={},response BodyText={}", url, response.bodyText());
        return response.bodyText();
    }

    private static String getDskUrl(String authServerUrl) {
        return String.format("%s/%s", authServerUrl, SYNC_DSK_PATH);
    }

    private static String getOauthLoginUrlUrl(String authServerUrl) {
        return String.format("%s/%s", authServerUrl, OAUTH_LOGIN_URL_PATH);
    }

    public static String getAppTokenUrl(String authServerUrl, String app) {
        return String.format("%s/%s", authServerUrl, SYNC_APP_TOKEN_PATH.replace("{app}", app));
    }

    public static void checkUserHasPrivilege(HttpServletRequest request, Authorize.Type authorize, AuthUser authUser, String appKey, long privilegeCacheTimeSeconds, String authUserUrl) {

        log.info("Request of parse authenticationPrivilege AuthorizeType={}", authorize);
        if (!Objects.equals(authorize, Authorize.Type.NONE)) {
            String privilegeKey = CACHE_PRIVILEGE_KEY + ":" + authUser.getId() + "_" + request.getRequestURI() + "(" + request.getMethod() + ")";
            String projectId = getHeaderAuthProjectId(request, authorize);

            String isPrivilege = GENERIC_PRIVILEGE_CACHE.get(privilegeKey,() -> parseAuthenticationPrivilege(request, authUser.getId(), projectId,request.getRequestURI() + "(" + request.getMethod() + ")", appKey, getHeaderKscNetwork(request), authUserUrl), privilegeCacheTimeSeconds);

            if (!Objects.equals(isPrivilege,SUCCESS)) {
                log.info("NO Privilege AuthorizeType={},URI={},UserId={}", authorize, request.getRequestURI(), authUser.getId());
                throw new AuthenticationInterceptor.InvalidPrivilegeException(isPrivilege);
            }
        }
    }

    private static String getHeaderAuthProjectId(HttpServletRequest request, Authorize.Type authorize) {
        if (Objects.equals(Authorize.Type.PROJECT, authorize)) {
            String projectId = request.getHeader(HEADER_AUTH_PROJECT_ID);
            if (projectId == null) {
                log.warn("GetHeaderAuthProjectId Is NUll");
                throw new AuthenticationInterceptor.InvalidPrivilegeException(NOT_FOUND_HEADER_PROJECT);
            }
            return projectId;
        }
        return null;
    }

    private static String parseAuthenticationPrivilege(HttpServletRequest request, Long userId,
                                                        String projectId,
                                                        String requestURI,
                                                        String appKey, String network, String authServerUrl) {
        String url = authServerUrl.concat("/api/privilegeAuthentication?userId=")
                                  .concat(userId + "&ak=" + appKey)
                                  .concat((StringUtil.isEmpty(projectId) ? "" : "&projectId=" + projectId))
                                  .concat("&url=" + requestURI).concat(StringUtil.isEmpty(network) ? "" : "&network=" + network);
        log.info("Start to parse authenticationPrivilege: url={}, userId={},projectId={},url={}, appKey={}", url, userId, projectId, requestURI, appKey);
        HttpResponse response = HttpRequest.get(url).header(Authentication.HEADER_X_AUTH_APP, appKey)
                .header(Authentication.HEADER_X_AUTH_TOKEN, AuthenticationHelper.getToken()).send();
        log.info("Response of parse authenticationPrivilege: url={}, responseStatusCode={}", url, response.statusCode());

        return processAuthenticationPrivilegeResponse(response);
    }

    private static String processAuthenticationPrivilegeResponse(HttpResponse response) {
        String result = response.bodyText();
        if(StringUtils.isEmpty(result)){
            log.error("Parse Authentication CommonUtils Failed, Server-Response-Message={}", result);
            return NOT_FOUND_NO_PRIVILEGE;
        }
        JSONObject obj = JSON.parseObject(result);
        if (obj.getInteger("status") != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication CommonUtils Failed, Server-Response-Message={}", obj.toJSONString());
            return obj.getString("message");
        }else{
            Boolean passed = obj.getBoolean("result");
            if(!passed){
                return obj.getString("message");
            }
        }
        return SUCCESS;
    }

    private static String getTenantAkSkUrl(String authServerUrl, String tenant) {
        return String.format("%s/%s", authServerUrl, SYNC_TENANT_SK_PATH.replace("{tenant}", tenant));
    }

    private static String getTenantTokenUrl(String authServerUrl, String tenant) {
        return String.format("%s/%s", authServerUrl, SYNC_TENANT_TOKEN_PATH.replace("{tenant}", tenant));
    }

    private static Cookie processResponse(HttpResponse response) {
        if (response.statusCode() != HttpStatus.HTTP_OK) {
            log.error("Request Auth-Server Parse Authentication Cookie Failed, Server-Response={}", response);
            return null;
        }

        JSONObject rr = JSON.parseObject(response.bodyText());
        if (rr.getInteger("status") != HttpStatus.HTTP_OK) {
            log.error("Request Auth-Server Parse Authentication Cookie Failed, Server-Response-Message={}", rr.toJSONString());
            return null;
        }

        return JSON.parseObject(rr.getString("result"), Cookie.class);
    }

    private static String getHeaderKscNetwork(HttpServletRequest request) {
        return request.getHeader(CONFIG_KEY_KSC_NETWORK);
    }

    /**
     * 黑名单
     * 白名单  测试
     * 白名单  生产
     * 白名单  测试+生产 一体化
     * NONE   无ip限制
     */
    private static String parseIpConfig(Long tenantId, String ipConfig, String appKey) {
        String url = AuthenticationHelper.getAuthServerUrl()
                .concat("/api/ipConfigStatusByTenantId?tenantId=").concat(tenantId + "&ip=" + ipConfig);
        log.info(String.format("Start to parse ipConfigStatusByTenantId: tenantId=%s, ipConfig=%s,url=%s", tenantId, ipConfig, url));

        HttpResponse response = HttpRequest.get(url).header(Authentication.HEADER_X_AUTH_APP, appKey)
                .header(Authentication.HEADER_X_AUTH_TOKEN, AuthenticationHelper.getToken()).send();
        log.info(String.format("Response of parse authenticationPrivilege: url=%s, responseStatusCode=%s", url, response.statusCode()));

        return processIpConfigResponse(response);
    }


    private static JSONObject invokerAccessTimeByUser(Long tenantId, Long userId, String appKey) {
        String url = AuthenticationHelper.getAuthServerUrl()
                .concat("/api/checkAccessTime?tenantId=").concat(tenantId + "&userId=" + userId);
        log.info(String.format("Start to parse invokerAccessTimeByUser: tenantId=%s, ipConfig=%s,url=%s", tenantId, userId, url));

        HttpResponse response = HttpRequest.get(url).header(Authentication.HEADER_X_AUTH_APP, appKey)
                .header(Authentication.HEADER_X_AUTH_TOKEN, AuthenticationHelper.getToken()).send();
        log.info(String.format("Response of parse invokerAccessTimeByUser: url=%s, responseStatusCode=%s", url, response.statusCode()));

        return accessTimeResponse(response);
    }

    private static String processIpConfigResponse(HttpResponse response) {
        if (response.statusCode() != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication CommonUtils Failed, Server-Response={}", response);
            return null;
        }

        JSONObject jsonObject = JSON.parseObject(response.bodyText());
        if (jsonObject.getInteger("status") != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication CommonUtils Failed, Server-Response-Message={}", jsonObject.toJSONString());
            return null;
        }
        return jsonObject.getString("result");
    }

    private static JSONObject accessTimeResponse(HttpResponse response) {
        if (response.statusCode() != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication CommonUtils Failed, Server-Response={}", response);
            return null;
        }

        JSONObject jsonObject = JSON.parseObject(response.bodyText());
        if (jsonObject.getInteger("status") != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication CommonUtils Failed, Server-Response-Message={}", jsonObject.toJSONString());
            return null;
        }
        return jsonObject;
    }

    public static void checkAccessTime(AuthUser authUser, String appKey) {
        log.info("Request of checkAccessTime userId={},tenantId={}", authUser.getId(), authUser.getTenant().getId());
        String ipConfigKey = CACHE_ACCESS_TIME_KEY + ":tid" + authUser.getTenant().getId() + "_uid" + authUser.getId();

        JSONObject jsonObject;
        String cache = GENERIC_ACCESS_TIME_CACHE.get(ipConfigKey);
        if (cache == null) {
            jsonObject = invokerAccessTimeByUser(authUser.getTenant().getId(), authUser.getId(), appKey);
            GENERIC_ACCESS_TIME_CACHE.put(ipConfigKey, JSONObject.toJSONString(jsonObject), 300);
        } else {
            jsonObject = JSONObject.parseObject(cache, JSONObject.class);
        }
        log.info("Request of checkAccessTime result={}", jsonObject);
        if (jsonObject != null && Objects.equals(jsonObject.getString("result"), "false")) {
            throw new AuthenticationInterceptor.InvalidBlackListException(jsonObject.getString("message"));
        }
    }

    /**
     * @return com.ksyun.auth.client.AuthClientConfig
     * @description 从统一认证服务请求auth-client配置
     */
    public static AuthClientConfig invokerAuthClientConfig() {
        String url = AuthenticationHelper.getAuthServerUrl().concat("/api/authClientConfig");
        HttpResponse response;
        try {
            response = HttpRequest.get(url).send();
            log.info("fetch auth-client config, url={}, status={}", url, response.statusCode());
            if (response.statusCode() == HttpStatus.HTTP_NOT_FOUND) {
                return null;
            }
            if (response.statusCode() != HttpStatus.HTTP_OK) {
                log.error("fetch auth-client config failed, {}", response.toString());
                return null;
            }
            JSONObject rr = JSON.parseObject(response.bodyText());
            if (rr.getInteger("status") != HttpStatus.HTTP_OK) {
                log.error("parse auth-client config failed, {}", rr.toJSONString());
                return null;
            }
            return JSON.parseObject(rr.getString("result"), AuthClientConfig.class);
        } catch (Exception e) {
            log.error("get auth-client config failed：", e);
            throw new RuntimeException("从统一登陆认证服务请求auth-client配置失败", e);
        }
    }

    /**
     * @param cookieNames valida CookieNames
     * @param cookies     request Cookies
     * @return java.util.Set<jakarta.servlet.http.Cookie>
     * @description 过滤掉和认证无关的cookie
     */
    public static Set<Cookie> filterRelatedCookies(List<String> cookieNames, Cookie[] cookies) {
        List<String> cookieList = new ArrayList<>();
        Set<Cookie> relatedCookies = new HashSet<>();
        for (Cookie cookie : cookies) {
            cookieList.add(cookie.getName());
            if (cookieNames.contains(cookie.getName())) {
                relatedCookies.add(cookie);
            }
        }
        log.info(String.format("filterRelatedCookies Request CookieNameList=%s,validCookieList=%s", JSON.toJSONString(cookieList), JSON.toJSONString(relatedCookies)));
        return relatedCookies;
    }

    public static void invokerOperationAudit(Map<String, Object> parameterMap) {
        try {
            String url = AuthenticationHelper.getAuthServerUrl().concat("/api/operationAudit");
            HttpResponse response = HttpRequest.post(url).header(HttpRequest.HEADER_CONTENT_TYPE, "application/json")
                    .bodyText(JSON.toJSONString(parameterMap), "application/json").send();
            log.info(String.format("Response of parse invokerOperationAudit: url=%s, responseStatusCode=%s", url, response.statusCode()));
        } catch (Exception e) {
            log.error("invokerOperationAudit exception：", e);
        }
    }

    /**
     * @param clientConfig 动态配置文件详细信息
     * @return java.util.List<java.lang.String>
     * @description 根据AuthClientConfig获取支持的cookie key列表
     */
    public static List<String> getCookieNamesByAuthClientConfig(AuthClientConfig clientConfig, String authType) {
        if (Objects.isNull(clientConfig)) {
            return null;
        }

        List<String> cookieNames = clientConfig.getCookieNamesMap().get(authType);
        log.info("Auth-Type={}, CookieNames={}", authType, cookieNames);
        if (Objects.isNull(cookieNames) || cookieNames.isEmpty()) {
            log.info("can not find invalid cookie names, authClientConfig={}", clientConfig.toString());
            return null;
        }
        return cookieNames;
    }
}