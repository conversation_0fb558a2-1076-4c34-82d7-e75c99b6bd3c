package com.ksyun.auth.client.interceptor;

import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.Authentication;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Optional;

public interface AuthenticationProcessor extends Comparable<AuthenticationProcessor> {
    Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize);

    int getOrder();

    AccessBy.Method getAcceptMethod();

    @Override
    default int compareTo(AuthenticationProcessor o) {
        return getOrder() - o.getOrder();
    }
}
