package com.ksyun.auth.client;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Mail <EMAIL>
 * @Date 2019-03-27 16:38
 * 访问控制，该注解只能用在指定的 Controller 方法上，用于标识该方法支持访问的方式，访问的方式包含以下几种，并可以任意组合
 * - AKSK：只支持其它 APP 通过 AKSK 的方式访问本方法
 * - TENANT：只支持其它 APP 通过已知租户的 AKSK 访问本方法
 * - COOKIE：只支持用户登录后，携带 Cookie 访问本方法
 * - NULL: 不需要拦截，公开访问
 **/
@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AccessBy {

    Method[] value() default {Method.COOKIE, Method.APP_TOKEN, Method.TENANT_TOKEN};

    enum Method {
        ANY, /*任意方式都可以，只要通过一种即可*/
        NULL, /*无需要认证*/
        COOKIE, /*Cookie认证*/
        APP_TOKEN, /*APP认证*/
        TENANT_TOKEN /*租户认证*/
    }

}
