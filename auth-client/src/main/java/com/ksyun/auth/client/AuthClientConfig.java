package com.ksyun.auth.client;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @description: auth-client配置，从统一用户认证服务获取
 * @author: wuzhenliang
 * @date: 2020-05-13
 */
@Setter
@Getter
public class AuthClientConfig {
    private Map<String, List<String>> cookieNamesMap;
    private Map<String, String> serverRouteRuleMap;
    private String cookieSalt;
    private Map<String, Long> cookieExpireMap;
    private Map<String, Long> loginCacheTimeMap;
    private Map<String, String> domainMap;
    private Map<String, String> logoutDomainMap;

    public AuthClientConfig(Map<String, List<String>> cookieNamesMap,
                            Map<String, String> serverRouteRuleMap,
                            String cookieSalt,
                            Map<String, Long> cookieExpireMap,
                            Map<String, Long> loginCacheTimeMap,
                            Map<String, String> domainMap,
                            Map<String, String> logoutDomainMap) {
        this.cookieNamesMap = cookieNamesMap;
        this.serverRouteRuleMap = serverRouteRuleMap;
        this.cookieSalt = cookieSalt;
        this.cookieExpireMap = cookieExpireMap;
        this.loginCacheTimeMap = loginCacheTimeMap;
        this.domainMap = domainMap;
        this.logoutDomainMap = logoutDomainMap;
    }

}
