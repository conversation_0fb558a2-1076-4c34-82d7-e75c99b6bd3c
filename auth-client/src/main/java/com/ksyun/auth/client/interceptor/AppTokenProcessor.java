package com.ksyun.auth.client.interceptor;

import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.client.authentication.AuthApp;
import com.ksyun.auth.client.authentication.Authentication;
import jodd.util.StringUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AppTokenProcessor implements AuthenticationProcessor {
    private static final Logger RUN_LOG = Logger.getLogger("AccessKeyInterceptor");
    private static final int ORDER = 2;

    // 请求头参数名，APP标识
    protected static final String CONFIG_KEY_APP = "X-AUTH-APP";
    protected static final String CONFIG_KEY_TENANT = "X-AUTH-APP";
    // 请求头参数名，TOKEN 标识
    protected static final String CONFIG_KEY_TOKEN = "X-AUTH-TOKEN";

    // 提示信息定义
    protected static final String NOT_FOUND_X_AUTH_TOKEN = "缺少请求参数 [X-AUTH-TOKEN]";
    protected static final String NOT_FOUND_X_AUTH_APP = "缺少请求参数 [X-AUTH-APP]";
    protected static final String INVALID_X_AUTH_TOKEN = "请求参数 [X-AUTH-TOKEN] 无效，请检查AKSK配置";

    // Token 缓存，当缓存中不包含请求中的携带的 Token 时，去认证中心获取一次，并替换掉旧的缓存
    protected static final Map<String, String> TOKEN_CACHE = new ConcurrentHashMap<>();

    // APP 标识，由认证中心分配
    protected String appKey;
    // APP 密钥，由认证中心分配
    protected String appSecret;
    // 认证中心URL
    protected String authServerUrl;

    public AppTokenProcessor(String ak, String sk, String authServerUrl) {
        // 初始化令牌生成器
        AuthenticationHelper.initialize(ak, sk, authServerUrl);

        this.appKey = ak;
        this.appSecret = sk;
        this.authServerUrl = authServerUrl;
    }

    @Override
    public Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        String requestHeaderKey = getHeaderKey(request);
        String requestHeaderToken = getHeaderToken(request);
        Optional<Authentication> optional = checkAndUpdateAuthToken(requestHeaderKey, requestHeaderToken);
        RUN_LOG.log(Level.INFO, String.format("AuthIntercept Success: url=%s, header-key=%s, header-token=%s, server-token=%s",
                request.getRequestURL(), requestHeaderKey, requestHeaderToken, StringUtil.isEmpty(requestHeaderKey) ? null : TOKEN_CACHE.get(requestHeaderKey)));
        return optional;
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        return AccessBy.Method.APP_TOKEN;
    }

    protected Optional<Authentication> checkAndUpdateAuthToken(String headerKey, String headerToken) {
        if (StringUtil.isEmpty(headerKey)) {
            return Optional.empty();
        }
        if (StringUtil.isEmpty(headerToken)) {
            throw new AuthenticationInterceptor.InvalidAuthTokenException(NOT_FOUND_X_AUTH_TOKEN);
        }
        if (!getOrUpdateAppTokenFromServer(headerKey, headerToken).contains(headerToken)) {
            throw new AuthenticationInterceptor.InvalidAuthTokenException(INVALID_X_AUTH_TOKEN);
        }
        return Optional.of(new AuthApp(headerKey));
    }

    protected String getOrUpdateAppTokenFromServer(String headerKey, String headerToken) {
        if (!TOKEN_CACHE.containsKey(headerKey) || !TOKEN_CACHE.containsValue(headerToken)) {
            String tokens = getTokenFromServer(headerKey);
            if (!StringUtil.isEmpty(tokens)) {
                TOKEN_CACHE.put(headerKey, tokens);
            }
        }
        return TOKEN_CACHE.getOrDefault(headerKey, "");
    }

    protected String getHeaderKey(HttpServletRequest request) {
        return request.getHeader(CONFIG_KEY_APP);
    }

    protected String getHeaderToken(HttpServletRequest request) {
        return request.getHeader(CONFIG_KEY_TOKEN);
    }

    protected String getTokenFromServer(String headerKey) {
        return CommonUtils.getAppToken(authServerUrl, headerKey);
    }
}
