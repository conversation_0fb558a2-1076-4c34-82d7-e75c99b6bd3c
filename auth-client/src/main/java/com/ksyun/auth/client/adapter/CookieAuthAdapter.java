package com.ksyun.auth.client.adapter;


import com.ksyun.auth.client.authentication.Authentication;

import jakarta.servlet.http.Cookie;
import java.util.Map;
import java.util.Optional;

/**
 * @description: cookie适配器接口，所有的适配器实现此接口即可
 * @author: wuzhenliang
 * @date: 2020-05-13
 */
public interface CookieAuthAdapter {

    /**
     * @param authCookie
     * @return java.com.ksyun.cmgt.kdbp.util.Optional<com.ksyun.common.domain.authentication.Authentication>
     * @description cookie解析
     */
    Optional<Authentication> resolve(Map<String, Cookie> authCookie);

    /**
     * 预解析
     *
     * @param authCookie
     */
    default void preResolve(Map<String, Cookie> authCookie) {
    }
}
