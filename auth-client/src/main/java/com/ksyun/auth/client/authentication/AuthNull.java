package com.ksyun.auth.client.authentication;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * <AUTHOR> zhang
 * @Mail <EMAIL>
 * @Date 2019-03-28 10:31
 **/
public class AuthNull implements Authentication {

    @Override
    public AuthenticationType getType() {
        return AuthenticationType.NULL;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return null;
    }

    @Override
    public boolean isAuthenticated() {
        return false;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {

    }

    @Override
    public String getName() {
        return null;
    }
}
