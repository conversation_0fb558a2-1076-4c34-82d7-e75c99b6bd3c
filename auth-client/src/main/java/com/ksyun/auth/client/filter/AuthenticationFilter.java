package com.ksyun.auth.client.filter;

import com.alibaba.fastjson.JSON;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.interceptor.AuthenticationInterceptor;
import com.ksyun.auth.client.interceptor.AuthenticationProcessor;
import com.ksyun.auth.client.interceptor.CookieProcessor;
import jodd.http.HttpStatus;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.annotation.Order;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TreeSet;

import static com.ksyun.auth.client.Constants.UNAUTHORIZED_MESSAGE;

@Deprecated
@Order(0)
public class AuthenticationFilter implements Filter, AuthenticationProcessor {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        try {
            Optional<Authentication> optional = this.doProcess(processors, request, response);
            optional.ifPresent(authentication -> request.setAttribute(Authentication.ARAN, authentication));
            chain.doFilter(request, response);
        } catch (AuthenticationInterceptor.InvalidAuthTokenException e) {
            if (request.getRequestURL().indexOf("/api/") >= 0) {
                onAuthenticateFailed(response, e.getMessage());
            }
            chain.doFilter(servletRequest, servletResponse);
        } catch (AuthenticationInterceptor.InvalidBlackListException e) {
            onBlackListFailed(response, e.getMessage());
        }
    }

    @Override
    public void destroy() {
    }

    // 拦截器链，拦截到请求后，按顺序处理，当第一个处理通过以后，请求通过，后续处理器不再处理
    private SortedSet<AuthenticationProcessor> processors = new TreeSet<>();

    // 默认访问方式，当请求的方法未标明访问方式时，采用此默认方式访问
    private AccessBy.Method[] DEFAULT_ACCESS_METHODS = new AccessBy.Method[]{AccessBy.Method.COOKIE, AccessBy.Method.APP_TOKEN};

    public AuthenticationFilter(AuthenticationProcessor... processors) {
        this.processors.addAll(Arrays.asList(processors));
    }

    public AuthenticationFilter() {
    }

    @Override
    public Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        throw new UnsupportedOperationException("Do Invoke doProcess method");
    }

    @Override
    public int getOrder() {
        throw new RuntimeException("Unordered processor...");
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        throw new RuntimeException("Unprocessable Method...");
    }

    private Optional<Authentication> doProcess(SortedSet<AuthenticationProcessor> processors, HttpServletRequest request, HttpServletResponse response) {
        for (AuthenticationProcessor processor : processors) {
            Optional<Authentication> optional = processor.process(request, response, Authorize.Type.NONE);
            if (optional.isPresent()) {
                return optional;
            }
        }
        throw new AuthenticationInterceptor.InvalidAuthTokenException(UNAUTHORIZED_MESSAGE);
    }

    private void onAuthenticateFailed(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.HTTP_UNAUTHORIZED);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.getWriter().write(message);
    }

    private void onBlackListFailed(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.HTTP_OK);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");

        Map<String, Object> data = new HashMap<String, Object>() {{
            put("status", 403);
            put("message", message);
        }};

        response.getWriter().write(JSON.toJSONString(data));
    }

}
