package com.ksyun.auth.client.authentication;

/**
 * 认证实体，可以是登录用户或者授权APP
 */
public interface Authentication extends org.springframework.security.core.Authentication {
    /**
     *  服务端认证通过以后，将用户对象保存 Request Parameter 中，此为 KEY
     */
    String ARAN = "ARAN";
    String HEADER_X_AUTH_APP = "X-AUTH-APP";
    String HEADER_X_AUTH_TOKEN = "X-AUTH-TOKEN";
    String HEADER_X_AUTH_TENANT = "X-AUTH-TENANT";
    String HEADER_X_AUTH_TENANT_ID = "X-AUTH-TENANT-ID";

    enum AuthenticationType {
        USER_AUTHENTICATION, APP_AUTHENTICATION, NULL
    }

    AuthenticationType getType();
}
