package com.ksyun.auth.client.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.client.authentication.AuthNull;
import com.ksyun.auth.client.authentication.Authentication;
import jodd.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TreeSet;

import static com.ksyun.auth.client.Constants.UNAUTHORIZED_MESSAGE;

/**
 * 栏截原则：
 * - HEADER 包含正含的 AK-TOKEN，直接通过，不再处理 Cookie
 * - HEADER 包含错误的 AK-TOKEN，直接异常，不再处理 Cookie
 * - HEADER 不包含 AK-TOKEN，处理 Cookie
 * - Cookie 包含正确的用户信息，直接通过
 * - Cookie 包含错误的用户信息，直接异常
 * - Cookie 未包含 指定 Cookie，直接异常
 */
@Slf4j
public class AuthenticationInterceptor implements HandlerInterceptor, AuthenticationProcessor {
    // 拦截器链，拦截到请求后，按顺序处理，当第一个处理通过以后，请求通过，后续处理器不再处理
    private SortedSet<AuthenticationProcessor> processors = new TreeSet<>();

    // 默认访问方式，当请求的方法未标明访问方式时，采用此默认方式访问
    private AccessBy.Method[] DEFAULT_ACCESS_METHODS = new AccessBy.Method[]{AccessBy.Method.COOKIE, AccessBy.Method.APP_TOKEN};

    public AuthenticationInterceptor(AuthenticationProcessor... processors) {
        this.processors.addAll(Arrays.asList(processors));
    }

    //@Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        try {
            AccessBy.Method[] methods = getAccessMethodsOrDefault(o);
            boolean containsNullMethod = methodsContains(methods, AccessBy.Method.NULL);
            log.info("request containsNullMethod：{}",containsNullMethod);
            if (containsNullMethod) {
                request.setAttribute(Authentication.ARAN, Optional.of(new AuthNull()));
                return true;
            }
            Authorize.Type authorize = getAuthorizeTypeOrDefault(o);
            SortedSet<AuthenticationProcessor> processableProcessors = getProcessableProcessors(methods);
            Optional<Authentication> optional = this.doProcess(processableProcessors, request, response, authorize);
            optional.ifPresent(authentication -> request.setAttribute(Authentication.ARAN, authentication));
            optional.ifPresent(authentication -> {
                SecurityContext sc = SecurityContextHolder.getContext();
                sc.setAuthentication(authentication);
            });
            return optional.isPresent();
        } catch (InvalidAuthTokenException e) {
            if (request.getRequestURL().indexOf("/api/") >= 0 ) {
                return onAuthenticateFailed(response, e.getMessage());
            }
            return true;
        } catch (InvalidPrivilegeException e) {
            return onPrivilegeFailed(response, e.getMessage());
        } catch (InvalidBlackListException e) {
            return onIPBlackList(response, e.getMessage());
        }
    }

    //@Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object o, ModelAndView modelAndView) {

    }

    //@Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object o, Exception e) {

    }

    @Override
    public Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        throw new UnsupportedOperationException("Do Invoke doProcess method");
    }

    @Override
    public int getOrder() {
        throw new RuntimeException("Unordered processor...");
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        throw new RuntimeException("Unprocessable Method...");
    }

    private Optional<Authentication> doProcess(SortedSet<AuthenticationProcessor> processors, HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        for (AuthenticationProcessor processor : processors) {
            Optional<Authentication> optional = processor.process(request, response, authorize);
            if (optional.isPresent()) {
                return optional;
            }
        }
        throw new InvalidAuthTokenException(UNAUTHORIZED_MESSAGE);
    }

    private AccessBy.Method[] getAccessMethodsOrDefault(Object object) {
        if (!(object instanceof HandlerMethod)) {
            return new AccessBy.Method[]{AccessBy.Method.NULL};
        }

        AccessBy accessBy = ((HandlerMethod) object).getMethod().getAnnotation(AccessBy.class);
        AccessBy.Method[] methods = accessBy == null ? DEFAULT_ACCESS_METHODS : accessBy.value();

        if (methodsContains(methods, AccessBy.Method.NULL)) {
            return new AccessBy.Method[]{AccessBy.Method.NULL};
        }
        if (methodsContains(methods, AccessBy.Method.ANY)) {
            return new AccessBy.Method[]{AccessBy.Method.COOKIE, AccessBy.Method.APP_TOKEN, AccessBy.Method.TENANT_TOKEN};
        }
        return methods;
    }

    private Authorize.Type getAuthorizeTypeOrDefault(Object object) {
        if (!(object instanceof HandlerMethod)) {
            return AuthenticationHelper.getDefaultAuthorizeMethod();
        }

        Authorize authorize = ((HandlerMethod) object).getMethod().getAnnotation(Authorize.class);
        if (authorize == null) {
            authorize = ((HandlerMethod) object).getBeanType().getAnnotation(Authorize.class);
        }
        return authorize == null ? AuthenticationHelper.getDefaultAuthorizeMethod() : authorize.value();
    }

    private boolean onAuthenticateFailed(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpStatus.HTTP_UNAUTHORIZED);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");

        log.info("user no login");
        String oAuthLoginUrl = AuthenticationHelper.getOauthLoginUrl();
        //TODO: 此处需要调整为 根据应用标识确定跳转页，而不仅仅是KDC OauthLoginUrl
        log.info("oAuthLoginUrl：{}",oAuthLoginUrl);
        Map<String, Object> data = new HashMap<String, Object>() {{
            put("status", 401);
            put("message", message);
            if(StringUtils.isNotEmpty(oAuthLoginUrl)){
                put("loginPageUrl",oAuthLoginUrl);
            }
        }};
        response.getWriter().write(JSON.toJSONString(data));
        return false;
    }

    private boolean onPrivilegeFailed(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpStatus.HTTP_OK);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");

        Map<String, Object> data = new HashMap<String, Object>() {{
            put("status", 402);
            put("message", message);
        }};

        response.getWriter().write(JSON.toJSONString(data));
        return false;
    }

    private boolean onIPBlackList(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpStatus.HTTP_OK);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");

        Map<String, Object> data = new HashMap<String, Object>() {{
            put("status", 403);
            put("message", message);
        }};

        response.getWriter().write(JSON.toJSONString(data));
        return false;
    }

    public static class InvalidAuthTokenException extends RuntimeException {
        public InvalidAuthTokenException(String msg) {
            super(msg);
        }
    }

    public static class InvalidPrivilegeException extends RuntimeException {
        public InvalidPrivilegeException(String msg) {
            super(msg);
        }
    }

    public static class InvalidBlackListException extends RuntimeException {
        public InvalidBlackListException(String msg) {
            super(msg);
        }
    }

    private SortedSet<AuthenticationProcessor> getProcessableProcessors(AccessBy.Method[] methods) {
        SortedSet<AuthenticationProcessor> processableProcessors = new TreeSet<>();
        this.processors.stream().filter(processor -> methodsContains(methods, processor.getAcceptMethod())).forEach(processableProcessors::add);
        return processableProcessors;
    }

    private boolean methodsContains(AccessBy.Method[] allowedMethods, AccessBy.Method method) {
        return Arrays.stream(allowedMethods).anyMatch(m -> method == m);
    }

}
