package com.ksyun.auth.client.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ksyun.auth.client.AccessBy;
import com.ksyun.auth.client.AuthClientConfig;
import com.ksyun.auth.client.AuthenticationHelper;
import com.ksyun.auth.client.Authorize;
import com.ksyun.auth.client.CommonUtils;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.client.authentication.Authentication;
import com.ksyun.auth.client.cache.GenericCache;
import jodd.http.HttpRequest;
import jodd.http.HttpResponse;
import jodd.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.ksyun.auth.client.Constants.*;


/**
 * 该处理器用于验证请求中所包含的 Cookie 信息是否满足登录状态要求，过程如下：
 * - 从请求中获取名字为 sso-token 及 的 cookie，
 * 包装成新的请求，发送到认证中心，认证中心返回正确结果则认证通过，否则认证不通过
 */
@Slf4j
public class CookieProcessor implements AuthenticationProcessor {
    private static final int ORDER = 1;

    private static final GenericCache<String, AuthUser> GENERIC_AUTH_CACHE = new GenericCache<>();
    public static final GenericCache<String, AuthClientConfig> GENERIC_AUTH_CLIENT_CONFIG_CACHE = new GenericCache<>();
    private String appKey;

    public CookieProcessor() {
    }

    public CookieProcessor(String ak, String sk, String authServerUrl) {
        appKey = ak;
        AuthenticationHelper.initialize(ak, sk, authServerUrl);
    }

    @Override
    public Optional<Authentication> process(HttpServletRequest request, HttpServletResponse response, Authorize.Type authorize) {
        try {
            //通过读取配置处理
            Optional<Authentication> isReturn = processByAuthClientConfig(request, authorize);
            if (isReturn.isPresent()) {
                return isReturn;
            }
        } catch (AuthenticationInterceptor.InvalidPrivilegeException e) {
            throw new AuthenticationInterceptor.InvalidPrivilegeException(e.getMessage());
        } catch (AuthenticationInterceptor.InvalidBlackListException e) {
            throw new AuthenticationInterceptor.InvalidBlackListException(e.getMessage());
        } catch (Exception e) {
            log.error("auth client process error", e);
        }
        return Optional.empty();
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public AccessBy.Method getAcceptMethod() {
        return AccessBy.Method.COOKIE;
    }

    /**
     * @param request   用户请求
     * @param authorize 权限认证
     * @return java.util.Optional<com.ksyun.common.domain.authentication.Authentication>
     * @description 从authServer读取auth-client端配置走新的认证逻辑
     */
    private Optional<Authentication> processByAuthClientConfig(HttpServletRequest request, Authorize.Type authorize) {
        Cookie[] cookies = AuthenticationHelper.getAuthenticationRelatedCookies(request);
        if (cookies == null) {
            log.info("CookieProcessor request.getCookies isEmpty");
            return Optional.empty();
        }
        String authServerUrl = AuthenticationHelper.getAuthServerUrl();

        filterRelatedCookies(Collections.singletonList(DEFAULT_COOKIE_NAME), cookies);
        AuthUser authUser = authCookie(cookies, authServerUrl, 1000L);

        if (authUser == null) {
            log.error("parseAuthenticationByAuthServer authUser is null url={}", request.getRequestURI());
            return Optional.empty();
        }

        // kcde 0.6：校验请求中的租户在已经绑定的租户列表中，并设置为当前租户
        authUser = authUser.toBuilder().build();
        authUser.setTenant(null);
        String headerTenantId = AuthenticationHelper.getAuthenticationTenantId(request);
        if (headerTenantId != null && !headerTenantId.equals("")) {
            Long tenantId;
            try {
                tenantId = Long.parseLong(headerTenantId);
            } catch (NumberFormatException e) {
                log.error("parse tenant id from header error {}", e.getMessage());
                return Optional.empty();
            }
            boolean found = false;
            for (AuthUser.InnerTenant it : authUser.getTenants()) {
                if (it.getId().equals(tenantId)) {
                    found = true;
                    // 创建一个新的用户对象，更新tenant
                    authUser.setTenant(it);
                }
            }
            if (!found) {
                log.error("user {} not belong to tenant {}", authUser.getId(), tenantId);
                return Optional.empty();
            }
        }

        // 8.进行功能权限验证
        CommonUtils.checkUserHasPrivilege(request, authorize, authUser, appKey, 2000L, AuthenticationHelper.getAuthServerUrl());
        return Optional.of(authUser);
    }

    /**
     * @param relatedCookies cookie
     * @param authServerUrl  认证地址
     * @return com.ksyun.common.domain.authentication.AuthUser
     * @description 认证cookie
     */
    private AuthUser authCookie(Cookie[] relatedCookies, String authServerUrl, long expire) {
        AuthUser authUser;
        String authUserCacheKey = CACHE_COOKIE_AUTH_USER_KEY + "_" + relatedCookies.toString();
        authUser = GENERIC_AUTH_CACHE.get(authUserCacheKey, () -> CookieProcessor.this.invokerAuthenticationByCookie(relatedCookies, authServerUrl), expire);
        log.debug("authUser={}", JSON.toJSONString(authUser));
        return authUser;
    }

    private AuthUser invokerAuthenticationByCookie(Cookie[] cookies, String authServerUrl) {
        String url = authServerUrl.concat("/api/authenticate");
        log.info("Start to parse authentication-cookie: url={}, cookies={}", url, JSON.toJSONString(cookies));
        HttpResponse response = HttpRequest.get(url).cookies(parseJavaCookie2JoddCookie(cookies))
                .header("Is-Client-Redirect", "true").send();
        log.info("Response of parse authentication-cookie: url={}, responseStatusCode={}", url, response.statusCode());
        return processResponse(response);
    }

    private AuthUser processResponse(HttpResponse response) {
        if (response.statusCode() != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication Cookie Failed, Server-Response={}", response);
            return null;
        }
        JSONObject rr = JSON.parseObject(response.bodyText());
        if (rr.getInteger("status") != HttpStatus.HTTP_OK) {
            log.error("Parse Authentication Cookie Failed, Server-Response-Message={}", rr);
            return null;
        }
        return JSON.parseObject(rr.getString("result"), AuthUser.class);
    }

    private jodd.http.Cookie[] parseJavaCookie2JoddCookie(Cookie[] cookies) {
        jodd.http.Cookie[] pared = new jodd.http.Cookie[cookies.length];
        for (int i = 0; i < pared.length; i++) {
            pared[i] = new jodd.http.Cookie(cookies[i].getName(), cookies[i].getValue());
        }
        return pared;
    }

    /**
     * @param cookieNames valida CookieNames
     * @param cookies     request Cookies
     * @return java.util.Set<jakarta.servlet.http.Cookie>
     * @description 过滤掉和认证无关的cookie
     */
    public static Set<Cookie> filterRelatedCookies(List<String> cookieNames, Cookie[] cookies) {
        List<String> cookieList = new ArrayList<>();
        Set<Cookie> relatedCookies = new HashSet<>();
        for (Cookie cookie : cookies) {
            cookieList.add(cookie.getName());
            if (cookieNames.contains(cookie.getName())) {
                relatedCookies.add(cookie);
            }
        }
        log.info(String.format("filterRelatedCookies Request CookieNameList=%s,validCookieList=%s", JSON.toJSONString(cookieList), JSON.toJSONString(relatedCookies)));
        return relatedCookies;
    }

}
