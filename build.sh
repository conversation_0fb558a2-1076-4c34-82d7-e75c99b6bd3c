#!/bin/bash
set -euxo pipefail

BASE_PATH="$(dirname $0)"
VERSION=$1
IMAGE=harbor.sdns.kscbigdata.cloud:11180/kbdp/kcde-auth-server:${VERSION}

DEV_IMAGE=hub.kce.ksyun.com/kcde/kcde-auth-server:${VERSION}

echo "构建项目..."
#export JAVA_HOME=/data/apps/jdk-17.0.9
export JAVA_HOME=/data/zhongbin/jdk-17.0.9
mvn clean package -DskipTests -Pjdk17_compile

echo "构建镜像..."
docker build -t ${DEV_IMAGE} -f auth-server/Dockerfile_java17 ${BASE_PATH}

echo "======Push Image======"
docker push "${DEV_IMAGE}"

echo "======Copy Image======"
docker tag "${DEV_IMAGE}" "${IMAGE}"

echo "导出镜像..."
docker save ${IMAGE} > images-authetication-${VERSION}.tar

echo "打包镜像..."
tar cvf authetication-images.tgz images-authetication-${VERSION}.tar
rm images-authetication-${VERSION}.tar

echo "打包chart..."
tar czf authetication-deploy.tgz -C auth-server/src/main/charts .

echo "拷贝制品..."
mkdir -p package-build/target/article/authetication-images/package
mv authetication-images.tgz package-build/target/article/authetication-images/package

mkdir -p package-build/target/article/authetication-deploy/package
mv authetication-deploy.tgz package-build/target/article/authetication-deploy/package
